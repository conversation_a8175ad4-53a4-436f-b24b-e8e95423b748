@using Blazorise
@using Blazorise.Components
@using EMPS.CompanyService.Currencies
@using EMPS.MoneyExchangeTransfersService.Blazor.Pages.MoneyExchangeTransfersService
@using EMPS.Shared.Enum
@using Microsoft.Extensions.Localization
@using EMPS.FeeService.Localization
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using Microsoft.AspNetCore.Components
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Http.Client
@using Blazorise.LoadingIndicator

@inherits MoneyExchangeTransfersServiceComponentBase
@inject NavigationManager NavigationManager
@inject IRemoteServiceConfigurationProvider RemoteServiceConfigurationProvider

<style>
    .inline-input-label {
        display: flex;
        justify-content: space-between;
        margin-bottom: 11px;
        align-items: center;
    }

</style>

@code {
    string FormWidthClass = "col-9";
}

<LoadingIndicator @ref="loadingIndicator">

    <Form Model="@FormCompanyToServicePoint">
        <Validations @ref=FormCompanyToServicePointValidations Model="@FormCompanyToServicePoint"
                     Mode="ValidationMode.Auto">
            <Card>
                <CardBody>

                    <Row>
                        <Column ColumnSize="ColumnSize.Is4" class="px-4 ">

                            <Div class="inline-input-label">
                                <FieldLabel>@L["No"]</FieldLabel>
                                <Div class="@FormWidthClass">
                                    <TextEdit @bind-Text="@FormCompanyToServicePoint.No" Immediate="true"
                                              @onkeydown="@LoadModelByCompanyToServicePointNo" Autofocus="true" TabIndex='1' />

                                </Div>

                            </Div>

                            <Div class="inline-input-label">
                                <FieldLabel>@L["DateTime"]</FieldLabel>
                                <Div class="@FormWidthClass">

                                    <DateEdit Style="padding-block: 7px;" TValue="DateTime?"
                                              InputMode="DateInputMode.DateTime"
                                              @bind-Date="@FormCompanyToServicePoint.TransactionDate" ReadOnly TabIndex='-1' />
                                </Div>
                            </Div>

                            <Div class="inline-input-label">
                                <FieldLabel>@L["ServicePoints"]</FieldLabel>
                                <Div class="@FormWidthClass">
                                    <Select TValue="Guid?" 
                                        SelectedValue="@FormCompanyToServicePoint.ServicePointId"
                                            SelectedValueChanged="@(async (Guid? id)=>{ await OnChangeServicePoint(id);})"
                                            class="text-center" Disabled="IsFormReadOnly()" TabIndex='2'>
                                        <SelectItem TValue="Guid?" Value="@null">

                                        </SelectItem>
                                        @foreach (var servicePoint in ServicePointCollection)
                                        {
                                            <SelectItem TValue="Guid" Value="@servicePoint.Id">
                                                @servicePoint.Name
                                            </SelectItem>
                                        }
                                    </Select>

                                </Div>
                            </Div>
                            <Div class="inline-input-label">
                                <FieldLabel>@L["ReciverCashier"]</FieldLabel>
                                <Div class="@FormWidthClass">
                                    <Select TValue="Guid?" @bind-SelectedValue="@FormCompanyToServicePoint.ExecutedByStaffId"
                                            class="text-center" Disabled="IsFormReadOnly()" TabIndex='3'>
                                        <SelectItem TValue="Guid?" Value="@null">

                                        </SelectItem>
                                        @foreach (var cashier in CashiersCollection)
                                        {
                                            <SelectItem TValue="Guid" Value="@cashier.Id">
                                                @cashier.Name
                                            </SelectItem>
                                        }
                                    </Select>

                                </Div>
                            </Div>

                        

                        </Column>

                        <Column ColumnSize="ColumnSize.Is4" class="px-4 ">

                            <Div class="inline-input-label ">
                                <FieldLabel>@L["Amount"]</FieldLabel>
                                <Div Style="justify-content:space-between;" class="@FormWidthClass" Flex="Flex.AlignContent.Start">
                                    <Div class="col-9">
                                        <NumericPicker @bind-Value="@FormCompanyToServicePoint.Amount" GroupSeparator=","
                                                       Disabled="IsFormReadOnly()" TabIndex='4' />
                                    </Div>
                                    <Div>
                                        <Select TValue="Guid?" 
                                            SelectedValue="@FormCompanyToServicePoint.CurrencyId"
                                            SelectedValueChanged="@(async (Guid? id)=>{ await OnChangeCurrency(id);})"
                                            Class="text-center "
                                                Style="padding-left:30px;padding-right:10px"
                                                Disabled="IsFormReadOnly()" TabIndex='5'>
                                            <SelectItem TValue="Guid?" Value="@null">

                                            </SelectItem>
                                            @foreach (var Currency in CurrenciesCollection)
                                            {
                                                <SelectItem TValue="Guid" Value="@Currency.Id">
                                                    @Currency.Code
                                                </SelectItem>
                                            }
                                        </Select>
                                    </Div>
                                </Div>


                            </Div>
                            <Div class="inline-input-label ">
                                <FieldLabel>@L["AvaregeCost"]</FieldLabel>
                                <Div Style="justify-content:space-between;" class="@FormWidthClass" Flex="Flex.AlignContent.Start">
                                    <Div class="col-9">
                                        <NumericPicker @bind-Value="@FormCompanyToServicePoint.AverageCost" GroupSeparator=","
                                                       ReadOnly TabIndex='-1' />
                                    </Div>
                                    <Div>
                                        <Select TValue="Guid?"
                                                @bind-value="LocalCurrencyId"
                                                Style="padding-left:30px;padding-right:10px"
                                                ReadOnly TabIndex='-1'>
                                            <SelectItem TValue="Guid?" Value="@null">

                                            </SelectItem>
                                            @foreach (var Currency in CurrenciesCollection.Where(x => x.CurrencyType == CurrencyType.Local))
                                            {
                                                <SelectItem TValue="Guid" Value="@Currency.Id">
                                                    @Currency.Code
                                                </SelectItem>
                                            }
                                        </Select>
                                    </Div>
                                </Div>


                            </Div>
                        </Column>

                        <Column ColumnSize="ColumnSize.Is4" class="px-4">


                            <Div class="inline-input-label">
                                <FieldLabel>@L["Notes"]</FieldLabel>
                                <Div class="@FormWidthClass">
                                    <MemoEdit @bind-Text="@FormCompanyToServicePoint.Notes" Disabled="IsFormReadOnly()"
                                              TabIndex='6'>
                                    </MemoEdit>
                                </Div>

                            </Div>

                        </Column>

                    </Row>
                </CardBody>
            </Card>

            <Card class="mt-2">
                <CardBody>

 

                    <Row>
                        <CardFooter>
                            <Div Style="display: flex;justify-content: space-between; padding-top:30px;">
                                <Span Style="margin-right: auto;order: 1; width: 50%;color: red;margin-right:20px;">@LastStatusBy</Span>
                                <Span Style="margin-left: auto;order: 2; width: 50%;color: red;margin-right:20px;">@LastStatusDateTime</Span>
                            </Div>
                        </CardFooter>
                    </Row>



                </CardBody>



            </Card>

        </Validations>
    </Form>


</LoadingIndicator>
