﻿using EMPS.CompanyService.Application.Contracts.Dtos.Staffs;
using EMPS.CompanyService.CentralVaults;
using EMPS.CompanyService.Currencies;
using EMPS.CompanyService.ServicePoints;
using EMPS.CompanyService.ServicePointSeats;
using EMPS.CompanyService.Staffs;
using EMPS.MoneyExchangeLedgerService.MoneyExchangeBalanceTrackings;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.Shared.Enum;
using EMPS.Shared.Enum.ExchangeLedger;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Identity;

namespace EMPS.MoneyExchangeTransfersService.CashVouchers
{
    [Authorize(MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Default)]

    public class ReceiptCashVouchersAppService : CashVouchersAppService, IReceiptCashVouchersAppService
    {
        public ReceiptCashVouchersAppService(ICashVoucherRepository cashVoucherRepository, CashVoucherManager cashVoucherManager, IIdentityUserAppService identityUserAppService, IStaffsAppService staffsAppService, IServicePointSeatsAppService servicePointSeatsAppService, IServicePointsAppService servicePointsAppService, ICentralVaultsAppService centralVaultsAppService, ICurrenciesAppService currencyAppService, IOrganizationUnitAppService organizationUnitAppService, IMoneyExchangeBalanceTrackingsAppService moneyExchangeBalanceTrackingsAppService) : base(cashVoucherRepository, cashVoucherManager, identityUserAppService, staffsAppService, servicePointSeatsAppService, servicePointsAppService, centralVaultsAppService, currencyAppService, organizationUnitAppService, moneyExchangeBalanceTrackingsAppService)
        {
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Approve)]
        public override Task<CashVoucherDto> ApproveByNoAsync(string no)
        {
            return base.ApproveByNoAsync(no);
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Delete)]
        public override Task DeletedByNoAsync(string no)
        {
            return base.DeletedByNoAsync(no);
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Receipt)]
        public  Task<CashVoucherDto> ReceiptByNoAsync(string no)
        {
            return base.ExecuteByNoAsync(no);
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Default)]
        public override Task<CashVoucherDto?> LoadByNoAsync(string no)
        {
            return base.LoadByNoAsync(no);
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Save)]
        public override Task<CashVoucherDto> SaveAsync(CashVoucherCreateDto input)
        {
            return base.SaveAsync(input);
        }
        protected override async Task<string?> GenerateNewNumberForCashVoucher()
        {
            return await _cashVoucherRepository.GenerateNewNoByAccountingMode(GetAccountingMode());
        }

        protected override AccountingMode GetAccountingMode()
        {
            return AccountingMode.Receipt;
        }

        protected override async  Task<CashVoucher?> GetRequiredCashVoucher(string no, Guid? spId = null, Guid? cvId = null)
        {
            var query = (await _cashVoucherRepository.GetQueryableAsync())
                            .Where(x => x.No == no && x.AccountingType == GetAccountingMode())
                            .WhereIf(spId.HasValue,x=>x.SeatLocationId==spId)
                            .WhereIf(cvId.HasValue, x => x.SeatLocationId == cvId)
                            .OrderByDescending(x => x.CreationTime).FirstOrDefault();
            return query;
        }        

        protected override Task<CashVoucher> SetBusinessLogicFromInheritanceClassesForApproveAsync(CashVoucher voucher)
        {
            return Task.FromResult(voucher);
        }

        protected override async Task<CashVoucher> SetBusinessLogicFromInheritanceClassesForExecuteAsync(CashVoucher voucher, StaffDto? staff, StaffSeatDto? staffSeat)
        {
            if (voucher.AssignSeatLocation == SeatLocationType.CentralVault) throw new UserFriendlyException(L["Error:CentralValutDontAccessTOExecute"]);

            if (voucher.AssignSeatLocation == SeatLocationType.ServicePoint)
            {
                if (staff == null) throw new UserFriendlyException(L["Error:ThisUserIsNotStaff"]);
                if (staffSeat == null) throw new UserFriendlyException(L["Error:ThisStaffIsNotInSeat"]);

                if (voucher.CashierId != staff.Id) throw new UserFriendlyException(L["Error:ThisStaffIsNotSameSelectedCashier"]);
                var OuSeat = await _organizationUnitAppService.GetAsync(staffSeat.OuId);
                if (OuSeat == null || !OuSeat.IsCash)
                    throw new UserFriendlyException(L["ThisSeatNoCashier"]);

                var dto = new MoneyExchangeBalanceTrackingInsertDto
                {
                    CurrencyCode = voucher.CurrencyCode,
                    CurrencyID = voucher.CurrencyId!.Value,
                    ExchangeLedgerTransactionType = ExchangeLedgerTransactionType.Bid,
                    OperationAmount = voucher.Amount,
                    OperationDate = DateTime.Now,
                    SeatID = staffSeat.SeatId,
                    ServicePointID = voucher.SeatLocationId!.Value,
                    ServicePointName = voucher.SeatLocationName,


                };

                await _moneyExchangeBalanceTrackingsAppService.InsertBalanceAsync(dto);
            }
            if (voucher.AssignSeatLocation == SeatLocationType.Company)
            {
                if (staffSeat != null)
                {

                    if (staffSeat.IsSeatForCentralVault) throw new UserFriendlyException(L["Error:ThisStaffIsSeatInCentralVault"]);
                    if (staffSeat.IsSeatForServicePoint) throw new UserFriendlyException(L["Error:ThisStaffIsSeatInServicePoint"]);
                }
                var dto = new MoneyExchangeBalanceTrackingInsertDto
                {
                    CurrencyCode = voucher.CurrencyCode,
                    CurrencyID = voucher.CurrencyId!.Value,
                    ExchangeLedgerTransactionType = ExchangeLedgerTransactionType.Bid,
                    OperationAmount = voucher.Amount,
                    OperationDate = DateTime.Now,
                    IsCompany = true,
                };

                await _moneyExchangeBalanceTrackingsAppService.InsertBalanceAsync(dto);
            }
            return voucher;
        }

        protected override Task<CashVoucher> SetBusinessLogicFromInheritanceClassesForSaveAsync(CashVoucher voucher)
        {
            voucher.AccountingType= GetAccountingMode();
            return Task.FromResult(voucher);
        }
    }
}
