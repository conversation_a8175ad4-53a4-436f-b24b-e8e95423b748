using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazorise;
using Blazorise.DataGrid;
using Volo.Abp.BlazoriseUI.Components;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.MoneyExchangeTransfersService.Shared;
using AutoMapper.Internal.Mappers;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.ServiceModel.Channels;
using Volo.Abp;
using EMPS.MoneyExchangeTransfersService.Blazor.Components;
using EMPS.MoneyExchangeTransfersService.CashVouchers;

namespace EMPS.MoneyExchangeTransfersService.Blazor.Pages.MoneyExchangeTransfersService.PaymentCashVoucherPages
{
    public partial class PaymentCashVoucherIssue
    {

        [Inject]
        private IPaymentCashVouchersAppService _PaymentVoucherService { get; set; }
        


        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar { get; } = new PageToolbar();


        private bool CanCreateOrUpdatePaymentVoucherDraft { get; set; }
        private bool CanDeletePaymentVoucherDraft { get; set; }

        private bool IsReadOnly { get; set; }


        private PaymentCashVoucherForm PaymentVoucherFormRef { get; set; }


        public PaymentCashVoucherIssue()
        {
        }

        protected override async Task OnInitializedAsync()
        {

            await SetToolbarButtons(null);
            await SetBreadcrumbItemsAsync();
            await SetPermissionsAsync();
        }


        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:PaymentVoucherIssue"]));


            return ValueTask.CompletedTask;
        }


        private async void ResetPaymentVoucher(bool forceReset = false)
        {
            if (await PaymentVoucherFormRef.ResetPaymentVoucher(forceReset))
            {
                await SetToolbarButtons(null);
                SetFormIsReadOnly(null);
                StateHasChanged();

            }


        }

        private async Task<CashVoucherDto?> SaveTransfare()
        {
            var PaymentVoucher = PaymentVoucherFormRef.FormPaymentVoucher;
            CashVoucherCreateDto createDto = ObjectMapper.Map<CashVoucherDto, CashVoucherCreateDto>(PaymentVoucher);
            if (!await ValidateForm(createDto)) return null;
            createDto.AssignSeatLocation = PaymentVoucherFormRef.SeatLocation;

            var newPaymentVoucher = await _PaymentVoucherService.SaveAsync(createDto);

            PaymentVoucherFormRef.ChangeEntity(newPaymentVoucher);
            return newPaymentVoucher;
        }

        private async Task SetPermissionsAsync()
        {
            CanCreateOrUpdatePaymentVoucherDraft = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.PaymentCashVouchers.Save);
            CanDeletePaymentVoucherDraft = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.PaymentCashVouchers.Delete);
        }


        private async  Task<bool> ValidateForm(CashVoucherCreateDto FormPaymentVoucherDraft)
        {

            if (PaymentVoucherFormRef.SeatLocation == 0)
            {
                await Message.Error(L["CashierSeatLocationMustBeSelected"]);
                return false;
            }
            if (PaymentVoucherFormRef.FormPaymentVoucher.Amount <= 0)
            {
                await Message.Error(L["AmountMustBeLargeThanZero"]);
                return false;
            }
            if (PaymentVoucherFormRef.SeatLocation == SeatLocationType.ServicePoint)
            {
                if (!PaymentVoucherFormRef.FormPaymentVoucher.SeatLocationId.HasValue)
                {

                    await Message.Error(L["ServicePointMustBeSelected"]);
                    return false;
                }
                if (!PaymentVoucherFormRef.FormPaymentVoucher.CashierId.HasValue)
                {

                    await Message.Error(L["CashierInServicePointMustBeSelected"]);
                    return false;
                }

            }
            if (PaymentVoucherFormRef.SeatLocation == SeatLocationType.CentralVault)
            {
                if (!PaymentVoucherFormRef.FormPaymentVoucher.SeatLocationId.HasValue)
                {

                    await Message.Error(L["CentralVaultMustBeSelected"]);
                    return false;
                }
                if (!PaymentVoucherFormRef.FormPaymentVoucher.CashierId.HasValue)
                {

                    await Message.Error(L["CashierInCentralVaultMustBeSelected"]);
                    return false;
                }
            }
            return true;




        }


        private void Log(string message)
        {
            Console.WriteLine($"MY-LOGGING: {message}");
        }



        private IReadOnlyList<string> stringList { get; set; }

        private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<string> e)
        {
        }

        private async Task HandleTransfareLoad(CashVoucherDto LoadedPaymentVoucher)
        {
            await SetToolbarButtons(LoadedPaymentVoucher);
            SetFormIsReadOnly(LoadedPaymentVoucher);
            Log(IsReadOnly.ToString());
            StateHasChanged();

        }

        private async Task DeleteTransfareAsync(string _No)
        {
            await _PaymentVoucherService.DeletedByNoAsync(_No);
            ResetPaymentVoucher(true);
        }


        private async Task SetToolbarButtons(CashVoucherDto? accountPayment)
        {
            Toolbar.Contributors.Clear();


            Toolbar.AddButton(
                L["NewPaymentVoucher"], () =>
                {
                    ResetPaymentVoucher();

                    return Task.CompletedTask;

                }, IconName.Add, Color.Warning


            );

            Toolbar.AddButton(
                L["SavePaymentVoucher"], async () =>
                {
                    await PerformSave();
                }, IconName.Save, requiredPolicyName:
                MoneyExchangeTransfersServicePermissions.PaymentCashVouchers.Save, color: Color.Success, disabled: !CanPaymentVoucherBeUpdated(accountPayment)
            );
            if (accountPayment != null && accountPayment.Id != Guid.Empty)
            {

                Toolbar.AddButton(
                            L["Delete"], async () =>
                            {
                                await PerformDeleteTransfare();

                            }, IconName.Delete,
                            requiredPolicyName: MoneyExchangeTransfersServicePermissions.PaymentCashVouchers.Delete,
                            color: Color.Danger,
                            disabled: !CanPaymentVoucherBeDeleted(accountPayment)
                        );

            }

            StateHasChanged();


        }


        private bool CanPaymentVoucherBeDeleted(CashVoucherDto accountPayment)
        {



            if (accountPayment == null) return false;
            if (accountPayment.Id == Guid.Empty) return false;
            if (accountPayment.IsApproved) return false;

            if (accountPayment.IsExecute) return false;

            return true;
        }
        private bool CanPaymentVoucherBeUpdated(CashVoucherDto ?accountPayment)
        {

            if (accountPayment != null)
                if (accountPayment.IsExecute|| accountPayment.IsApproved) return false;

            return true;
        }

        private void SetFormIsReadOnly(CashVoucherDto? accountPayment)
        {

            this.IsReadOnly = false;
            if (accountPayment == null) return;
            if (accountPayment.IsExecute|| accountPayment.IsApproved)
            {
                this.IsReadOnly = true;
            }
        }

        private async Task PerformSave()
        {
            await Task.Delay(300);


            if (!CanPaymentVoucherBeUpdated(PaymentVoucherFormRef.FormPaymentVoucher))
            {
                await Message.Error(L["Error:ThisPaymentVoucherCannotBeUpdated"]);
                return;
            }
            Console.WriteLine("********************* :1");
            CashVoucherDto? newPaymentVoucher = new();
            var PaymentVoucher = PaymentVoucherFormRef.FormPaymentVoucher;
            Console.WriteLine("********************* :2");



            await PaymentVoucherFormRef.loadingIndicator.Show();
            Console.WriteLine("********************* :3");

            try
            {
                newPaymentVoucher = await SaveTransfare();

                if (newPaymentVoucher != null)
                {
                    await SetToolbarButtons(newPaymentVoucher);
                    await Message.Success(L["SaveSuccess"]);
                }


            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await PaymentVoucherFormRef.loadingIndicator.Hide();

            }

        }
        private async Task PerformDeleteTransfare()
        {
            if (!PaymentVoucherFormRef.IsPaymentVoucherLoaded())
            {
                await Message.Error(L["Error:LoadPaymentVoucherFirst"]);
                return;
            }
            if (!CanPaymentVoucherBeDeleted(PaymentVoucherFormRef.FormPaymentVoucher))
            {
                await Message.Error(L["Error:ThisPaymentVoucherCannotBeDeleted"]);
                return;
            }
            if (!await Message.Confirm(L["DeleteConfirmationMessage"])) return;
            await PaymentVoucherFormRef.loadingIndicator.Show();

            try
            {
                var PaymentVoucherNo = PaymentVoucherFormRef.GetPaymentVoucherNo();


                await DeleteTransfareAsync(PaymentVoucherNo!);
                await Message.Success(L["SuccessfullyDeleted"]);

            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await PaymentVoucherFormRef.loadingIndicator.Hide();

            }
        }
        private async Task PerformNewTransfare()
        {
            ResetPaymentVoucher();

        }
        public async Task HandleKeyDown(KeyboardEventArgs e)
        {

            ExecuteJS("event.stopPropagation();");

            if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyD"))
            {
                await PerformDeleteTransfare();
            }
            else if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyR"))
            {
                await PerformNewTransfare();
            }
            else if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyG"))
            {
                await Task.Delay(50);
                await PaymentVoucherFormRef.PerformLoadPaymentVoucher();
            }
            else if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyS"))
            {
                await PerformSave();
            }


        }
        private void ExecuteJS(string script)
        {
            JSRuntime.InvokeVoidAsync("eval", script);
        }
        [Inject] IJSRuntime JSRuntime { get; set; }
        protected override void OnAfterRender(bool firstRender)
        {
            base.OnAfterRender(firstRender);
            if (firstRender)
            {
                // See warning about memory above in the article
                var dotNetReference = DotNetObjectReference.Create(this);
                JSRuntime.InvokeVoidAsync("GetDotnetInstance", dotNetReference);
            }
        }






    }
}
