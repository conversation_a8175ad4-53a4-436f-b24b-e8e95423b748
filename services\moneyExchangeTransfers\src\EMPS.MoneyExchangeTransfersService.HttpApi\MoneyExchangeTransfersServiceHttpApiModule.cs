﻿using Localization.Resources.AbpUi;
using Microsoft.Extensions.DependencyInjection;
using EMPS.MoneyExchangeTransfersService.Localization;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Localization;
using Volo.Abp.Modularity;

namespace EMPS.MoneyExchangeTransfersService;

[DependsOn(
    typeof(MoneyExchangeTransfersServiceApplicationContractsModule),
    typeof(AbpAspNetCoreMvcModule))]
public class MoneyExchangeTransfersServiceHttpApiModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        PreConfigure<IMvcBuilder>(mvcBuilder =>
        {
            mvcBuilder.AddApplicationPartIfNotExists(typeof(MoneyExchangeTransfersServiceHttpApiModule).Assembly);
        });
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpLocalizationOptions>(options =>
        {
            options.Resources
                .Get<MoneyExchangeTransfersServiceResource>()
                .AddBaseTypes(typeof(AbpUiResource));
        });
    }
}
