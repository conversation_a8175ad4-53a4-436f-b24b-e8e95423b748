using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using System.Collections.Generic;
using EMPS.CompanyService.Currencies;
using EMPS.CompanyService.Staffs;
using EMPS.CompanyService.ServicePoints;

namespace EMPS.MoneyExchangeTransfersService.Controllers.MoneyExchangeTransferCurrencies
{
    [RemoteService(Name = "MoneyExchangeTransfersService")]
    [Area("moneyExchangeTransfersService")]
    [ControllerName("ServicePointToCompanyTransferService")]
    [Route("api/money-exchange-transfers-service/ServicePointToCompanyTransferService")]
    public class ServicePointToCompanyTransferServiceController : Ab<PERSON><PERSON>ontroller, IServicePointToCompanyTransferService
    {
        public IServicePointToCompanyTransferService _ServicePointToCompanyTransferService;

        public ServicePointToCompanyTransferServiceController(IServicePointToCompanyTransferService ServicePointToCompanyTransferService)
        {
            _ServicePointToCompanyTransferService = ServicePointToCompanyTransferService;
        }
        [HttpPut]
        [Route("ApproveByNoAsync")]
        public Task<MoneyExchangeTransferCurrencyDto> ApproveByNoAsync(string no)
        {
            return _ServicePointToCompanyTransferService.ApproveByNoAsync(no);
        }

        [HttpDelete]
        [Route("DeletedByNoAsync")]
        public Task DeletedByNoAsync(string no)
        {
            return _ServicePointToCompanyTransferService.DeletedByNoAsync(no);
        }
        [HttpPut]
        [Route("ExecuteByNoAsync")]
        public Task<MoneyExchangeTransferCurrencyDto> ExecuteByNoAsync(string no)
        {
            return _ServicePointToCompanyTransferService.ExecuteByNoAsync(no);
        }
        [HttpGet]
        [Route("GetAllActiveCurrenciesAsync")]
        public Task<List<CurrencyDto>> GetAllActiveCurrenciesAsync()
        {
            return _ServicePointToCompanyTransferService.GetAllActiveCurrenciesAsync();
        }
        [HttpGet]
        [Route("GetReadyTransfersToExecuteForCompany")]
        public Task<PagedResultDto<MoneyExchangeTransferCurrencyDto>> GetReadyTransfersToExecuteForCompany()
        {
            return _ServicePointToCompanyTransferService.GetReadyTransfersToExecuteForCompany();
        }

        [HttpGet]
        [Route("LoadByNoAsync")]
        public Task<MoneyExchangeTransferCurrencyDto?> LoadByNoAsync(string no)
        {
            return _ServicePointToCompanyTransferService.LoadByNoAsync(no);
        }
        [HttpPost]
        [Route("SaveAsync")]
        public Task<MoneyExchangeTransferCurrencyDto> SaveAsync(MoneyExchangeTransferCurrencyCreateDto input)
        {
            return _ServicePointToCompanyTransferService.SaveAsync(input);
        }
    }
}