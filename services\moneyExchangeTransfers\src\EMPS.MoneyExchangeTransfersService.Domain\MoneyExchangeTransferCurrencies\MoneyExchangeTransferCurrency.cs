using System;
using System.Linq;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;
using JetBrains.Annotations;

using Volo.Abp;

namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
{
    public class MoneyExchangeTransferCurrency : FullAuditedAggregateRoot<Guid>
    {
        [CanBeNull]
        public virtual string? No { get; set; }

        public virtual double Amount { get; set; }
        public virtual double AverageCost { get; set; }

        [CanBeNull]
        public virtual string? CurrencyCode { get; set; }

        public virtual Guid? CurrencyId { get; set; }

        [CanBeNull]
        public virtual string? ServicePointName { get; set; }

        public virtual Guid? ServicePointId { get; set; }

        [CanBeNull]
        public virtual string? TransactionIssueUserName { get; set; }

        public virtual Guid? TransactionIssueUserId { get; set; }

        [CanBeNull]
        public virtual string? TransactionIssueStaffName { get; set; }

        public virtual Guid? TransactionIssueStaffId { get; set; }
        public virtual DateTime? TransactionDate { get; set; }

        public virtual Guid? TransactionIssueSeatId { get; set; }

        public virtual bool IsApproved { get; set; }

        [CanBeNull]
        public virtual string? ApprovedByUserName { get; set; }

        public virtual Guid? ApprovedByUserId { get; set; }

        public virtual DateTime? ApprovalDate { get; set; }

        public virtual DateTime? ApprovalDateTime { get; set; }

        [CanBeNull]
        public virtual string? Notes { get; set; }

        public virtual bool IsExecute { get; set; }

        [CanBeNull]
        public virtual string? ExecutedByUserName { get; set; }

        public virtual Guid? ExecutedByUserId { get; set; }

        [CanBeNull]
        public virtual string? ExecutedByStaffName { get; set; }

        public virtual Guid? ExecutedByStaffId { get; set; }

        public virtual Guid? ExecutedBySeatId { get; set; }

        public virtual DateTime? ExecuteDate { get; set; }

        public virtual DateTime? ExecuteDateTime { get; set; }

        public virtual MoneyExchangeTransferStatus TransferStatus { get; set; }

        public virtual MoneyExchangeTransferType TransferType { get; set; }

        public MoneyExchangeTransferCurrency()
        {

        }

        public MoneyExchangeTransferCurrency(Guid id, string no, double amount, string currencyCode, string servicePointName, string transactionIssueUserName, string transactionIssueStaffName, bool isApproved, string approvedByUserName, string notes, bool isExecute, string executedByUserName, string executedByStaffName, MoneyExchangeTransferStatus transferStatus, MoneyExchangeTransferType transferType, Guid? currencyId = null, Guid? servicePointId = null, Guid? transactionIssueUserId = null, Guid? transactionIssueStaffId = null, Guid? transactionIssueSeatId = null, Guid? approvedByUserId = null, DateTime? approvalDate = null, DateTime? approvalDateTime = null, Guid? executedByUserId = null, Guid? executedByStaffId = null, Guid? executedBySeatId = null, DateTime? executeDate = null, DateTime? executeDateTime = null)
        {

            Id = id;
            No = no;
            Amount = amount;
            CurrencyCode = currencyCode;
            ServicePointName = servicePointName;
            TransactionIssueUserName = transactionIssueUserName;
            TransactionIssueStaffName = transactionIssueStaffName;
            IsApproved = isApproved;
            ApprovedByUserName = approvedByUserName;
            Notes = notes;
            IsExecute = isExecute;
            ExecutedByUserName = executedByUserName;
            ExecutedByStaffName = executedByStaffName;
            TransferStatus = transferStatus;
            TransferType = transferType;
            CurrencyId = currencyId;
            ServicePointId = servicePointId;
            TransactionIssueUserId = transactionIssueUserId;
            TransactionIssueStaffId = transactionIssueStaffId;
            TransactionIssueSeatId = transactionIssueSeatId;
            ApprovedByUserId = approvedByUserId;
            ApprovalDate = approvalDate;
            ApprovalDateTime = approvalDateTime;
            ExecutedByUserId = executedByUserId;
            ExecutedByStaffId = executedByStaffId;
            ExecutedBySeatId = executedBySeatId;
            ExecuteDate = executeDate;
            ExecuteDateTime = executeDateTime;
        }

        public void FillTransactionIssueInfo(string transactionIssueUserName, Guid? transactionIssueUserId = null, string? transactionIssueStaffName = null, Guid? transactionIssueStaffId = null, Guid? transactionIssueSeatId = null)
        {
            TransactionIssueUserName = transactionIssueUserName;
            TransactionIssueStaffName = transactionIssueStaffName;
            TransactionIssueUserId = transactionIssueUserId;
            TransactionIssueStaffId = transactionIssueStaffId;
            TransactionIssueSeatId = transactionIssueSeatId;
            TransactionDate=DateTime.Now;
        }

        public void FillApprovalInfo(string? approvedByUserName, Guid? approvedByUserId = null)
        {
            ApprovedByUserName = approvedByUserName;
            ApprovedByUserId = approvedByUserId;
            ApprovalDate = DateTime.Today.Date;
            ApprovalDateTime = DateTime.Now;
            IsApproved = true;
        }

        public void FillExecutionInfo(string? executedByUserName, Guid? executedByUserId, string? executedByStaffName=null, Guid? executedByStaffId = null, Guid? executedBySeatId = null)
        {
            ExecutedByUserName = executedByUserName;
            ExecutedByStaffName = executedByStaffName;
            ExecutedByUserId = executedByUserId;
            ExecutedByStaffId = executedByStaffId;
            ExecutedBySeatId = executedBySeatId;
            ExecuteDate = DateTime.Today.Date;
            ExecuteDateTime = DateTime.Now;
            IsExecute = true;
        }

        public void FillServicePointInfo(string servicePointName, Guid? servicePointId = null)
        {
            ServicePointName = servicePointName;
            ServicePointId = servicePointId;
        }

        public void FillCurrencyInfo(string currencyCode, Guid? currencyId = null)
        {
            CurrencyCode = currencyCode;
            CurrencyId = currencyId;
        }
    }
}