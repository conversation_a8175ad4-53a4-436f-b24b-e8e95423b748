﻿using EMPS.CompanyService.Currencies;
using EMPS.CompanyService.ServicePoints;
using EMPS.CompanyService.ServicePointSeats;
using EMPS.CompanyService.Staffs;
using EMPS.MoneyExchangeLedgerService.MoneyExchangeBalanceTrackings;
using EMPS.MoneyExchangeLedgerService.MoneyExchangeCasherLedgers;
using EMPS.MoneyExchangeLedgerService.MoneyExchangeLedger;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.Shared.Enum.ExchangeLedger;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Identity;

namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
{
    [Authorize(MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Default)]

    public class CompanyToServicePointTransferService : MoneyExchangeTransferBase, ICompanyToServicePointTransferService
    {
        public CompanyToServicePointTransferService(IMoneyExchangeTransferCurrencyRepository moneyExchangeTransferCurrencyRepository, IStaffsAppService staffsAppService, IServicePointSeatsAppService servicePointSeatsAppService, IOrganizationUnitAppService organizationUnitAppService, IServicePointsAppService servicePointsAppService, ICurrenciesAppService currencyAppService, IIdentityUserAppService identityUserAppService, IMoneyExchangeCasherLedgersAppService iMoneyExchangeCasherLedgersApp, IMoneyExchangeBalanceTrackingsAppService moneyExchangeBalanceTrackingsAppService) : base(moneyExchangeTransferCurrencyRepository, staffsAppService, servicePointSeatsAppService, organizationUnitAppService, servicePointsAppService, currencyAppService, identityUserAppService, iMoneyExchangeCasherLedgersApp, moneyExchangeBalanceTrackingsAppService)
        {
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Approve)]
        public override Task<MoneyExchangeTransferCurrencyDto> ApproveByNoAsync(string no)
        {
            return base.ApproveByNoAsync(no);
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Delete)]
        public override Task DeletedByNoAsync(string no)
        {
            return base.DeletedByNoAsync(no);
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Execute)]
        public override Task<MoneyExchangeTransferCurrencyDto> ExecuteByNoAsync(string no)
        {
            return base.ExecuteByNoAsync(no);
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Save)]
        public override Task<MoneyExchangeTransferCurrencyDto> SaveAsync(MoneyExchangeTransferCurrencyCreateDto input)
        {
            return base.SaveAsync(input);
        }

        protected override async Task<MoneyExchangeTransferCurrency?> CheckRequiredBusinessLogicInUpdatingMode(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency)
        {
            return await Task.FromResult(moneyExchangeTransferCurrency);
        }

        protected override async Task<string?> GenerateNewNumberForMoneyExchangeTransfer()
        {
            return await _moneyExchangeTransferCurrencyRepository.GenerateNewNoForMoneyExchangeTransferCurrencyByTransferType(MoneyExchangeTransferType.CompanyToServicePoint);
        }

        protected override async Task<MoneyExchangeTransferCurrency?> GetRequiredMoneyExchangeTransfer(string no)
        {
            var query = (await _moneyExchangeTransferCurrencyRepository.GetQueryableAsync())
                   .Where(x => x.No == no && x.TransferType == MoneyExchangeTransferType.CompanyToServicePoint).OrderByDescending(x => x.CreationTime).FirstOrDefault();
            return query;
        }

        protected override async Task<MoneyExchangeTransferCurrency> SetBusinessLogicFromInheritanceClassesForApproveAsync(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency)
        {
            moneyExchangeTransferCurrency.FillApprovalInfo(GetFullUserName(), GeTUserId());
            return await Task.FromResult(moneyExchangeTransferCurrency);
        }

        protected override async Task<MoneyExchangeTransferCurrency> SetBusinessLogicFromInheritanceClassesForExecuteAsync(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency, CurrencyDto currency)
        {
            var ReciverStaff = await CheckStaffInformationAsync();
            var ReciverStaffSeat = await CheckSeatInformationAsync(ReciverStaff);
            if (ReciverStaff.Seat.StaffId != moneyExchangeTransferCurrency.ExecutedByStaffId) throw new UserFriendlyException(L["TheReciverStaffIsNotTheUserThatAddedInIssueToExecute"]);
            if (ReciverStaffSeat.ServicePointId != moneyExchangeTransferCurrency.ServicePointId) throw new UserFriendlyException(L["ThisOrderNotReleasedFromSameServicePoint"]);

            await CheckBalanceForCompanyCurrency(moneyExchangeTransferCurrency.CurrencyId!.Value, moneyExchangeTransferCurrency.Amount);
            if (currency.CurrencyType == CurrencyType.Local)
            {
                var dto = new TransfareBalanceDTO
                {
                    CurrencyCode = moneyExchangeTransferCurrency.CurrencyCode!,
                    CurrencyID = moneyExchangeTransferCurrency.CurrencyId.Value,
                    operationAmount = moneyExchangeTransferCurrency.Amount,
                    ReciverCasherID = ReciverStaff.Seat.Id,
                    servicePointID = ReciverStaff.ServicePoint.Id,
                    servicePointName = ReciverStaff.ServicePoint.Name,
                    TransactionSource = ExchangeLedgerTransactionSource.CompanyToServicePoint
                };
                await _moneyExchangeBalanceTrackingsAppService.MoveBalanceBetweenTwoCasherAsync(dto);
            }
            else
            {

                var dto = new MoneyExchangeLedgerInsertDto
                {
                    OperationTime = DateTime.Now,
                    ServicePointID = ReciverStaff.ServicePoint.Id,
                    ServicePointName = ReciverStaff.ServicePoint.Name,
                    ReciverCasherID = ReciverStaff.Seat.Id,
                    TransactionSource = ExchangeLedgerTransactionSource.CompanyToServicePoint,
                    TransactionNo = moneyExchangeTransferCurrency.No!,
                    BaseCurrencyID = moneyExchangeTransferCurrency.CurrencyId.Value,
                    BaseCurrencyCode = moneyExchangeTransferCurrency.CurrencyCode!,
                    MoneyExchangeCurrencyLedgerInsertDtos = new List<MoneyExchangeCurrencyLedgerInsertDto> { new MoneyExchangeCurrencyLedgerInsertDto { Amount = moneyExchangeTransferCurrency.Amount, CurrencyID = moneyExchangeTransferCurrency.CurrencyId.Value, CurrencyCode = moneyExchangeTransferCurrency.CurrencyCode!, Rate = moneyExchangeTransferCurrency.AverageCost } }
                };

                await InsertToLegerAndAssertOnBalance(dto);
            }
            moneyExchangeTransferCurrency.FillExecutionInfo(GetFullUserName(), GeTUserId(), $"{ReciverStaff.Staff.Name} {ReciverStaff.Staff.Surname}", ReciverStaff.Staff.Id, ReciverStaff.Seat.Id);
            return moneyExchangeTransferCurrency;
        }

        protected override async Task<MoneyExchangeTransferCurrency> SetBusinessLogicFromInheritanceClassesForSaveAsync(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency, MoneyExchangeTransferCurrencyCreateDto moneyExchange, CurrencyDto currency)
        {
            if (!moneyExchange.ExecutedByStaffId.HasValue) throw new UserFriendlyException(L["TheReciverStaffMustBeSelected"]);
            var reciverStaff = await CheckStaffInformationAsync(moneyExchange.ExecutedByStaffId);
            var reciverStaffSeat = await CheckSeatInformationAsync(reciverStaff);
            moneyExchangeTransferCurrency.ExecutedByStaffId = reciverStaff.Staff.Id;
            var servicePoint = await GetServicePointAsync(reciverStaffSeat.ServicePointId);
            CheckServicePointInformation(servicePoint);
            moneyExchangeTransferCurrency!.FillServicePointInfo(servicePoint.Name, servicePoint.Id);
            if (currency.CurrencyType!=CurrencyType.Local)
                moneyExchangeTransferCurrency.AverageCost = await GetAverageCostForCompany(moneyExchangeTransferCurrency.CurrencyId!.Value);
            moneyExchangeTransferCurrency.TransferType = MoneyExchangeTransferType.CompanyToServicePoint;
            moneyExchangeTransferCurrency.FillTransactionIssueInfo(GetFullUserName(), GeTUserId());
            return await Task.FromResult(moneyExchangeTransferCurrency);
        }

        public async Task<PagedResultDto<MoneyExchangeTransferCurrencyDto>> GetExternalMissionOrdersReadyToExecute()
        {
            var Staff = await CheckStaffInformationAsync();
            var StaffSeat = await CheckSeatInformationAsync(Staff);
            var query = (await _moneyExchangeTransferCurrencyRepository.GetQueryableAsync()).Where(x => x.ServicePointId == Staff.ServicePoint.Id && x.IsExecute == false && x.ExecutedByStaffId == Staff.Staff.Id);
            return new PagedResultDto<MoneyExchangeTransferCurrencyDto>
            {
                Items = ObjectMapper.Map<List<MoneyExchangeTransferCurrency>, List<MoneyExchangeTransferCurrencyDto>>(query.ToList()),
                TotalCount = query.Count(),
            };
        }
        protected override MoneyExchangeTransferType GetTransferType()
        {
            return MoneyExchangeTransferType.CompanyToServicePoint;
        }
    }
}
