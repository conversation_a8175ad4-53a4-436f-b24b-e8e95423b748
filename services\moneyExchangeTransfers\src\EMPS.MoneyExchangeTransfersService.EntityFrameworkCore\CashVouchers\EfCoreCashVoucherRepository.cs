using EMPS.MoneyExchangeTransfersService.CashVouchers;
using EMPS.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using EMPS.MoneyExchangeTransfersService.EntityFrameworkCore;

namespace EMPS.MoneyExchangeTransfersService.CashVouchers
{
    public class EfCoreCashVoucherRepository : EfCoreRepository<MoneyExchangeTransfersServiceDbContext, CashVoucher, Guid>, ICashVoucherRepository
    {
        public EfCoreCashVoucherRepository(IDbContextProvider<MoneyExchangeTransfersServiceDbContext> dbContextProvider)
            : base(dbContextProvider)
        {

        }

        public async Task<List<CashVoucher>> GetListAsync(
            string filterText = null,
            string no = null,
            double? amountMin = null,
            double? amountMax = null,
            string currencyCode = null,
            Guid? currencyId = null,
            string seatLocationName = null,
            Guid? seatLocationId = null,
            string issueUserName = null,
            Guid? issueUserId = null,
            string issueStaffName = null,
            Guid? issueStaffId = null,
            Guid? issueSeatId = null,
            bool? isApproved = null,
            string approvedByUserName = null,
            Guid? approvedByUserId = null,
            DateTime? approvalDateMin = null,
            DateTime? approvalDateMax = null,
            DateTime? approvalDateTimeMin = null,
            DateTime? approvalDateTimeMax = null,
            string notes = null,
            bool? isExecute = null,
            string executedByUserName = null,
            Guid? executedByUserId = null,
            string executedByStaffName = null,
            Guid? executedByStaffId = null,
            Guid? executedBySeatId = null,
            DateTime? executeDateMin = null,
            DateTime? executeDateMax = null,
            DateTime? executeDateTimeMin = null,
            DateTime? executeDateTimeMax = null,
            string approveByStaffName = null,
            Guid? approveByStaffId = null,
            Guid? approveBySeatId = null,
            AccountingMode? accountingType = null,
            string bolbKey = null,
            string fileName = null,
            string fileExtension = null,
            string fileSize = null,
            Guid? financialPeriodId = null,
            string financialPeriodName = null,
            Guid? financialAccountId = null,
            string financialAccountName = null,
            SeatLocationType? assignSeatLocation = null,
            Guid? costCenterId = null,
            string costCenterName = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetQueryableAsync()), filterText, no, amountMin, amountMax, currencyCode, currencyId, seatLocationName, seatLocationId, issueUserName, issueUserId, issueStaffName, issueStaffId, issueSeatId, isApproved, approvedByUserName, approvedByUserId, approvalDateMin, approvalDateMax, approvalDateTimeMin, approvalDateTimeMax, notes, isExecute, executedByUserName, executedByUserId, executedByStaffName, executedByStaffId, executedBySeatId, executeDateMin, executeDateMax, executeDateTimeMin, executeDateTimeMax, approveByStaffName, approveByStaffId, approveBySeatId, accountingType, bolbKey, fileName, fileExtension, fileSize, financialPeriodId, financialPeriodName, financialAccountId, financialAccountName, assignSeatLocation, costCenterId, costCenterName);
            query = query.OrderBy(string.IsNullOrWhiteSpace(sorting) ? CashVoucherConsts.GetDefaultSorting(false) : sorting);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountAsync(
            string filterText = null,
            string no = null,
            double? amountMin = null,
            double? amountMax = null,
            string currencyCode = null,
            Guid? currencyId = null,
            string seatLocationName = null,
            Guid? seatLocationId = null,
            string issueUserName = null,
            Guid? issueUserId = null,
            string issueStaffName = null,
            Guid? issueStaffId = null,
            Guid? issueSeatId = null,
            bool? isApproved = null,
            string approvedByUserName = null,
            Guid? approvedByUserId = null,
            DateTime? approvalDateMin = null,
            DateTime? approvalDateMax = null,
            DateTime? approvalDateTimeMin = null,
            DateTime? approvalDateTimeMax = null,
            string notes = null,
            bool? isExecute = null,
            string executedByUserName = null,
            Guid? executedByUserId = null,
            string executedByStaffName = null,
            Guid? executedByStaffId = null,
            Guid? executedBySeatId = null,
            DateTime? executeDateMin = null,
            DateTime? executeDateMax = null,
            DateTime? executeDateTimeMin = null,
            DateTime? executeDateTimeMax = null,
            string approveByStaffName = null,
            Guid? approveByStaffId = null,
            Guid? approveBySeatId = null,
            AccountingMode? accountingType = null,
            string bolbKey = null,
            string fileName = null,
            string fileExtension = null,
            string fileSize = null,
            Guid? financialPeriodId = null,
            string financialPeriodName = null,
            Guid? financialAccountId = null,
            string financialAccountName = null,
            SeatLocationType? assignSeatLocation = null,
            Guid? costCenterId = null,
            string costCenterName = null,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetDbSetAsync()), filterText, no, amountMin, amountMax, currencyCode, currencyId, seatLocationName, seatLocationId, issueUserName, issueUserId, issueStaffName, issueStaffId, issueSeatId, isApproved, approvedByUserName, approvedByUserId, approvalDateMin, approvalDateMax, approvalDateTimeMin, approvalDateTimeMax, notes, isExecute, executedByUserName, executedByUserId, executedByStaffName, executedByStaffId, executedBySeatId, executeDateMin, executeDateMax, executeDateTimeMin, executeDateTimeMax, approveByStaffName, approveByStaffId, approveBySeatId, accountingType, bolbKey, fileName, fileExtension, fileSize, financialPeriodId, financialPeriodName, financialAccountId, financialAccountName, assignSeatLocation, costCenterId, costCenterName);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        protected virtual IQueryable<CashVoucher> ApplyFilter(
            IQueryable<CashVoucher> query,
            string filterText,
            string no = null,
            double? amountMin = null,
            double? amountMax = null,
            string currencyCode = null,
            Guid? currencyId = null,
            string seatLocationName = null,
            Guid? seatLocationId = null,
            string issueUserName = null,
            Guid? issueUserId = null,
            string issueStaffName = null,
            Guid? issueStaffId = null,
            Guid? issueSeatId = null,
            bool? isApproved = null,
            string approvedByUserName = null,
            Guid? approvedByUserId = null,
            DateTime? approvalDateMin = null,
            DateTime? approvalDateMax = null,
            DateTime? approvalDateTimeMin = null,
            DateTime? approvalDateTimeMax = null,
            string notes = null,
            bool? isExecute = null,
            string executedByUserName = null,
            Guid? executedByUserId = null,
            string executedByStaffName = null,
            Guid? executedByStaffId = null,
            Guid? executedBySeatId = null,
            DateTime? executeDateMin = null,
            DateTime? executeDateMax = null,
            DateTime? executeDateTimeMin = null,
            DateTime? executeDateTimeMax = null,
            string approveByStaffName = null,
            Guid? approveByStaffId = null,
            Guid? approveBySeatId = null,
            AccountingMode? accountingType = null,
            string bolbKey = null,
            string fileName = null,
            string fileExtension = null,
            string fileSize = null,
            Guid? financialPeriodId = null,
            string financialPeriodName = null,
            Guid? financialAccountId = null,
            string financialAccountName = null,
            SeatLocationType? assignSeatLocation = null,
            Guid? costCenterId = null,
            string costCenterName = null)
        {
            return query
                    .WhereIf(!string.IsNullOrWhiteSpace(filterText), e => e.No.Contains(filterText) || e.CurrencyCode.Contains(filterText) || e.SeatLocationName.Contains(filterText) || e.IssueUserName.Contains(filterText) || e.IssueStaffName.Contains(filterText) || e.ApprovedByUserName.Contains(filterText) || e.Notes.Contains(filterText) || e.ExecutedByUserName.Contains(filterText) || e.ExecutedByStaffName.Contains(filterText) || e.ApproveByStaffName.Contains(filterText) || e.BolbKey.Contains(filterText) || e.FileName.Contains(filterText) || e.FileExtension.Contains(filterText) || e.FileSize.Contains(filterText) || e.FinancialPeriodName.Contains(filterText) || e.FinancialAccountName.Contains(filterText) || e.CostCenterName.Contains(filterText))
                    .WhereIf(!string.IsNullOrWhiteSpace(no), e => e.No.Contains(no))
                    .WhereIf(amountMin.HasValue, e => e.Amount >= amountMin.Value)
                    .WhereIf(amountMax.HasValue, e => e.Amount <= amountMax.Value)
                    .WhereIf(!string.IsNullOrWhiteSpace(currencyCode), e => e.CurrencyCode.Contains(currencyCode))
                    .WhereIf(currencyId.HasValue, e => e.CurrencyId == currencyId)
                    .WhereIf(!string.IsNullOrWhiteSpace(seatLocationName), e => e.SeatLocationName.Contains(seatLocationName))
                    .WhereIf(seatLocationId.HasValue, e => e.SeatLocationId == seatLocationId)
                    .WhereIf(!string.IsNullOrWhiteSpace(issueUserName), e => e.IssueUserName.Contains(issueUserName))
                    .WhereIf(issueUserId.HasValue, e => e.IssueUserId == issueUserId)
                    .WhereIf(!string.IsNullOrWhiteSpace(issueStaffName), e => e.IssueStaffName.Contains(issueStaffName))
                    .WhereIf(issueStaffId.HasValue, e => e.IssueStaffId == issueStaffId)
                    .WhereIf(issueSeatId.HasValue, e => e.IssueSeatId == issueSeatId)
                    .WhereIf(isApproved.HasValue, e => e.IsApproved == isApproved)
                    .WhereIf(!string.IsNullOrWhiteSpace(approvedByUserName), e => e.ApprovedByUserName.Contains(approvedByUserName))
                    .WhereIf(approvedByUserId.HasValue, e => e.ApprovedByUserId == approvedByUserId)
                    .WhereIf(approvalDateMin.HasValue, e => e.ApprovalDate >= approvalDateMin.Value)
                    .WhereIf(approvalDateMax.HasValue, e => e.ApprovalDate <= approvalDateMax.Value)
                    .WhereIf(approvalDateTimeMin.HasValue, e => e.ApprovalDateTime >= approvalDateTimeMin.Value)
                    .WhereIf(approvalDateTimeMax.HasValue, e => e.ApprovalDateTime <= approvalDateTimeMax.Value)
                    .WhereIf(!string.IsNullOrWhiteSpace(notes), e => e.Notes.Contains(notes))
                    .WhereIf(isExecute.HasValue, e => e.IsExecute == isExecute)
                    .WhereIf(!string.IsNullOrWhiteSpace(executedByUserName), e => e.ExecutedByUserName.Contains(executedByUserName))
                    .WhereIf(executedByUserId.HasValue, e => e.ExecutedByUserId == executedByUserId)
                    .WhereIf(!string.IsNullOrWhiteSpace(executedByStaffName), e => e.ExecutedByStaffName.Contains(executedByStaffName))
                    .WhereIf(executedByStaffId.HasValue, e => e.ExecutedByStaffId == executedByStaffId)
                    .WhereIf(executedBySeatId.HasValue, e => e.ExecutedBySeatId == executedBySeatId)
                    .WhereIf(executeDateMin.HasValue, e => e.ExecuteDate >= executeDateMin.Value)
                    .WhereIf(executeDateMax.HasValue, e => e.ExecuteDate <= executeDateMax.Value)
                    .WhereIf(executeDateTimeMin.HasValue, e => e.ExecuteDateTime >= executeDateTimeMin.Value)
                    .WhereIf(executeDateTimeMax.HasValue, e => e.ExecuteDateTime <= executeDateTimeMax.Value)
                    .WhereIf(!string.IsNullOrWhiteSpace(approveByStaffName), e => e.ApproveByStaffName.Contains(approveByStaffName))
                    .WhereIf(approveByStaffId.HasValue, e => e.ApproveByStaffId == approveByStaffId)
                    .WhereIf(approveBySeatId.HasValue, e => e.ApproveBySeatId == approveBySeatId)
                    .WhereIf(accountingType.HasValue, e => e.AccountingType == accountingType)
                    .WhereIf(!string.IsNullOrWhiteSpace(bolbKey), e => e.BolbKey.Contains(bolbKey))
                    .WhereIf(!string.IsNullOrWhiteSpace(fileName), e => e.FileName.Contains(fileName))
                    .WhereIf(!string.IsNullOrWhiteSpace(fileExtension), e => e.FileExtension.Contains(fileExtension))
                    .WhereIf(!string.IsNullOrWhiteSpace(fileSize), e => e.FileSize.Contains(fileSize))
                    .WhereIf(financialPeriodId.HasValue, e => e.FinancialPeriodId == financialPeriodId)
                    .WhereIf(!string.IsNullOrWhiteSpace(financialPeriodName), e => e.FinancialPeriodName.Contains(financialPeriodName))
                    .WhereIf(financialAccountId.HasValue, e => e.FinancialAccountId == financialAccountId)
                    .WhereIf(!string.IsNullOrWhiteSpace(financialAccountName), e => e.FinancialAccountName.Contains(financialAccountName))
                    .WhereIf(assignSeatLocation.HasValue, e => e.AssignSeatLocation == assignSeatLocation)
                    .WhereIf(costCenterId.HasValue, e => e.CostCenterId == costCenterId)
                    .WhereIf(!string.IsNullOrWhiteSpace(costCenterName), e => e.CostCenterName.Contains(costCenterName));
        }

        public async Task<string> GenerateNewNoByAccountingMode(AccountingMode accountingMode)
        {
            // Get the last transaction of the specified type in the current year
            var transactions = await(await GetQueryableAsync())
                .IgnoreQueryFilters()
                .Where(x => x.AccountingType == accountingMode)
                .ToListAsync();

            var lastTransaction = transactions
              .OrderByDescending(x => int.Parse(x.No)) // Sort in memory
              .FirstOrDefault();

            // If no transaction exists for this type in the current year, start with 1
            if (lastTransaction == null)
            {
                return "1";
            }

            // Extract the last index and increment it by 1
            if (int.TryParse(lastTransaction.No, out int lastIndex))
            {
                return (lastIndex + 1).ToString();
            }

            // If parsing fails, return -1 as a fallback
            return "-1";
        }
    }
}