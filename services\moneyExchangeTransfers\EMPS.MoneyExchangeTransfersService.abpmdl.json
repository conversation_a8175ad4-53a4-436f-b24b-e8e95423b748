{"folders": {"items": {"src": {}, "test": {}}}, "packages": {"EMPS.MoneyExchangeTransfersService.Domain.Shared": {"path": "src/EMPS.MoneyExchangeTransfersService.Domain.Shared/EMPS.MoneyExchangeTransfersService.Domain.Shared.abppkg.json", "folder": "src"}, "EMPS.MoneyExchangeTransfersService.Application.Contracts": {"path": "src/EMPS.MoneyExchangeTransfersService.Application.Contracts/EMPS.MoneyExchangeTransfersService.Application.Contracts.abppkg.json", "folder": "src"}, "EMPS.MoneyExchangeTransfersService.Domain": {"path": "src/EMPS.MoneyExchangeTransfersService.Domain/EMPS.MoneyExchangeTransfersService.Domain.abppkg.json", "folder": "src"}, "EMPS.MoneyExchangeTransfersService.Application": {"path": "src/EMPS.MoneyExchangeTransfersService.Application/EMPS.MoneyExchangeTransfersService.Application.abppkg.json", "folder": "src"}, "EMPS.MoneyExchangeTransfersService.EntityFrameworkCore": {"path": "src/EMPS.MoneyExchangeTransfersService.EntityFrameworkCore/EMPS.MoneyExchangeTransfersService.EntityFrameworkCore.abppkg.json", "folder": "src"}, "EMPS.MoneyExchangeTransfersService.HttpApi": {"path": "src/EMPS.MoneyExchangeTransfersService.HttpApi/EMPS.MoneyExchangeTransfersService.HttpApi.abppkg.json", "folder": "src"}, "EMPS.MoneyExchangeTransfersService.HttpApi.Client": {"path": "src/EMPS.MoneyExchangeTransfersService.HttpApi.Client/EMPS.MoneyExchangeTransfersService.HttpApi.Client.abppkg.json", "folder": "src"}, "EMPS.MoneyExchangeTransfersService.HttpApi.Host": {"path": "src/EMPS.MoneyExchangeTransfersService.HttpApi.Host/EMPS.MoneyExchangeTransfersService.HttpApi.Host.abppkg.json", "folder": "src"}, "EMPS.MoneyExchangeTransfersService.Blazor": {"path": "src/EMPS.MoneyExchangeTransfersService.Blazor/EMPS.MoneyExchangeTransfersService.Blazor.abppkg.json", "folder": "src"}, "EMPS.MoneyExchangeTransfersService.Domain.Tests": {"path": "test/EMPS.MoneyExchangeTransfersService.Domain.Tests/EMPS.MoneyExchangeTransfersService.Domain.Tests.abppkg.json", "folder": "test"}, "EMPS.MoneyExchangeTransfersService.EntityFrameworkCore.Tests": {"path": "test/EMPS.MoneyExchangeTransfersService.EntityFrameworkCore.Tests/EMPS.MoneyExchangeTransfersService.EntityFrameworkCore.Tests.abppkg.json", "folder": "test"}, "EMPS.MoneyExchangeTransfersService.TestBase": {"path": "test/EMPS.MoneyExchangeTransfersService.TestBase/EMPS.MoneyExchangeTransfersService.TestBase.abppkg.json", "folder": "test"}, "EMPS.MoneyExchangeTransfersService.Application.Tests": {"path": "test/EMPS.MoneyExchangeTransfersService.Application.Tests/EMPS.MoneyExchangeTransfersService.Application.Tests.abppkg.json", "folder": "test"}}}