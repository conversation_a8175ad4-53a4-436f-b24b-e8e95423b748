﻿using EMPS.CompanyService;
using Volo.Abp.Application;
using Volo.Abp.Authorization;
using Volo.Abp.Modularity;

namespace EMPS.MoneyExchangeTransfersService;

[DependsOn(
    typeof(MoneyExchangeTransfersServiceDomainSharedModule),
    typeof(AbpDddApplicationContractsModule),
    typeof(AbpAuthorizationModule),
    typeof(CompanyServiceApplicationContractsModule)

    )]
public class MoneyExchangeTransfersServiceApplicationContractsModule : AbpModule
{

}
