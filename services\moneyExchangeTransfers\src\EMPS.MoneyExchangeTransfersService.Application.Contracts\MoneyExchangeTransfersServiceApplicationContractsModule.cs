﻿using EMPS.CompanyService;
using EMPS.MoneyExchangeLedgerService;
using Volo.Abp.Application;
using Volo.Abp.Authorization;
using Volo.Abp.Modularity;

namespace EMPS.MoneyExchangeTransfersService;

[DependsOn(
    typeof(MoneyExchangeTransfersServiceDomainSharedModule),
    typeof(AbpDddApplicationContractsModule),
    typeof(AbpAuthorizationModule),
    typeof(MoneyExchangeLedgerServiceApplicationContractsModule),

    typeof(CompanyServiceApplicationContractsModule)

    )]
public class MoneyExchangeTransfersServiceApplicationContractsModule : AbpModule
{

}
