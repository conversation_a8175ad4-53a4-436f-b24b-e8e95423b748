using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using EMPS.MoneyExchangeTransfersService.EntityFrameworkCore;

namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
{
    public class EfCoreMoneyExchangeTransferCurrencyRepository : EfCoreRepository<MoneyExchangeTransfersServiceDbContext, MoneyExchangeTransferCurrency, Guid>, IMoneyExchangeTransferCurrencyRepository
    {
        public EfCoreMoneyExchangeTransferCurrencyRepository(IDbContextProvider<MoneyExchangeTransfersServiceDbContext> dbContextProvider)
            : base(dbContextProvider)
        {

        }

        public async Task<List<MoneyExchangeTransferCurrency>> GetListAsync(
            string filterText = null,
            string no = null,
            double? amountMin = null,
            double? amountMax = null,
            string currencyCode = null,
            Guid? currencyId = null,
            string servicePointName = null,
            Guid? servicePointId = null,
            string transactionIssueUserName = null,
            Guid? transactionIssueUserId = null,
            string transactionIssueStaffName = null,
            Guid? transactionIssueStaffId = null,
            Guid? transactionIssueSeatId = null,
            bool? isApproved = null,
            string approvedByUserName = null,
            Guid? approvedByUserId = null,
            DateTime? approvalDateMin = null,
            DateTime? approvalDateMax = null,
            DateTime? approvalDateTimeMin = null,
            DateTime? approvalDateTimeMax = null,
            string notes = null,
            bool? isExecute = null,
            string executedByUserName = null,
            Guid? executedByUserId = null,
            string executedByStaffName = null,
            Guid? executedByStaffId = null,
            Guid? executedBySeatId = null,
            DateTime? executeDateMin = null,
            DateTime? executeDateMax = null,
            DateTime? executeDateTimeMin = null,
            DateTime? executeDateTimeMax = null,
            MoneyExchangeTransferStatus? transferStatus = null,
            MoneyExchangeTransferType? transferType = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetQueryableAsync()), filterText, no, amountMin, amountMax, currencyCode, currencyId, servicePointName, servicePointId, transactionIssueUserName, transactionIssueUserId, transactionIssueStaffName, transactionIssueStaffId, transactionIssueSeatId, isApproved, approvedByUserName, approvedByUserId, approvalDateMin, approvalDateMax, approvalDateTimeMin, approvalDateTimeMax, notes, isExecute, executedByUserName, executedByUserId, executedByStaffName, executedByStaffId, executedBySeatId, executeDateMin, executeDateMax, executeDateTimeMin, executeDateTimeMax, transferStatus, transferType);
            query = query.OrderBy(string.IsNullOrWhiteSpace(sorting) ? MoneyExchangeTransferCurrencyConsts.GetDefaultSorting(false) : sorting);
            return await query.PageBy(skipCount, maxResultCount).ToListAsync(cancellationToken);
        }

        public async Task<long> GetCountAsync(
            string filterText = null,
            string no = null,
            double? amountMin = null,
            double? amountMax = null,
            string currencyCode = null,
            Guid? currencyId = null,
            string servicePointName = null,
            Guid? servicePointId = null,
            string transactionIssueUserName = null,
            Guid? transactionIssueUserId = null,
            string transactionIssueStaffName = null,
            Guid? transactionIssueStaffId = null,
            Guid? transactionIssueSeatId = null,
            bool? isApproved = null,
            string approvedByUserName = null,
            Guid? approvedByUserId = null,
            DateTime? approvalDateMin = null,
            DateTime? approvalDateMax = null,
            DateTime? approvalDateTimeMin = null,
            DateTime? approvalDateTimeMax = null,
            string notes = null,
            bool? isExecute = null,
            string executedByUserName = null,
            Guid? executedByUserId = null,
            string executedByStaffName = null,
            Guid? executedByStaffId = null,
            Guid? executedBySeatId = null,
            DateTime? executeDateMin = null,
            DateTime? executeDateMax = null,
            DateTime? executeDateTimeMin = null,
            DateTime? executeDateTimeMax = null,
            MoneyExchangeTransferStatus? transferStatus = null,
            MoneyExchangeTransferType? transferType = null,
            CancellationToken cancellationToken = default)
        {
            var query = ApplyFilter((await GetDbSetAsync()), filterText, no, amountMin, amountMax, currencyCode, currencyId, servicePointName, servicePointId, transactionIssueUserName, transactionIssueUserId, transactionIssueStaffName, transactionIssueStaffId, transactionIssueSeatId, isApproved, approvedByUserName, approvedByUserId, approvalDateMin, approvalDateMax, approvalDateTimeMin, approvalDateTimeMax, notes, isExecute, executedByUserName, executedByUserId, executedByStaffName, executedByStaffId, executedBySeatId, executeDateMin, executeDateMax, executeDateTimeMin, executeDateTimeMax, transferStatus, transferType);
            return await query.LongCountAsync(GetCancellationToken(cancellationToken));
        }

        protected virtual IQueryable<MoneyExchangeTransferCurrency> ApplyFilter(
            IQueryable<MoneyExchangeTransferCurrency> query,
            string filterText,
            string no = null,
            double? amountMin = null,
            double? amountMax = null,
            string currencyCode = null,
            Guid? currencyId = null,
            string servicePointName = null,
            Guid? servicePointId = null,
            string transactionIssueUserName = null,
            Guid? transactionIssueUserId = null,
            string transactionIssueStaffName = null,
            Guid? transactionIssueStaffId = null,
            Guid? transactionIssueSeatId = null,
            bool? isApproved = null,
            string approvedByUserName = null,
            Guid? approvedByUserId = null,
            DateTime? approvalDateMin = null,
            DateTime? approvalDateMax = null,
            DateTime? approvalDateTimeMin = null,
            DateTime? approvalDateTimeMax = null,
            string notes = null,
            bool? isExecute = null,
            string executedByUserName = null,
            Guid? executedByUserId = null,
            string executedByStaffName = null,
            Guid? executedByStaffId = null,
            Guid? executedBySeatId = null,
            DateTime? executeDateMin = null,
            DateTime? executeDateMax = null,
            DateTime? executeDateTimeMin = null,
            DateTime? executeDateTimeMax = null,
            MoneyExchangeTransferStatus? transferStatus = null,
            MoneyExchangeTransferType? transferType = null)
        {
            return query
                    .WhereIf(!string.IsNullOrWhiteSpace(filterText), e => e.No.Contains(filterText) || e.CurrencyCode.Contains(filterText) || e.ServicePointName.Contains(filterText) || e.TransactionIssueUserName.Contains(filterText) || e.TransactionIssueStaffName.Contains(filterText) || e.ApprovedByUserName.Contains(filterText) || e.Notes.Contains(filterText) || e.ExecutedByUserName.Contains(filterText) || e.ExecutedByStaffName.Contains(filterText))
                    .WhereIf(!string.IsNullOrWhiteSpace(no), e => e.No.Contains(no))
                    .WhereIf(amountMin.HasValue, e => e.Amount >= amountMin.Value)
                    .WhereIf(amountMax.HasValue, e => e.Amount <= amountMax.Value)
                    .WhereIf(!string.IsNullOrWhiteSpace(currencyCode), e => e.CurrencyCode.Contains(currencyCode))
                    .WhereIf(currencyId.HasValue, e => e.CurrencyId == currencyId)
                    .WhereIf(!string.IsNullOrWhiteSpace(servicePointName), e => e.ServicePointName.Contains(servicePointName))
                    .WhereIf(servicePointId.HasValue, e => e.ServicePointId == servicePointId)
                    .WhereIf(!string.IsNullOrWhiteSpace(transactionIssueUserName), e => e.TransactionIssueUserName.Contains(transactionIssueUserName))
                    .WhereIf(transactionIssueUserId.HasValue, e => e.TransactionIssueUserId == transactionIssueUserId)
                    .WhereIf(!string.IsNullOrWhiteSpace(transactionIssueStaffName), e => e.TransactionIssueStaffName.Contains(transactionIssueStaffName))
                    .WhereIf(transactionIssueStaffId.HasValue, e => e.TransactionIssueStaffId == transactionIssueStaffId)
                    .WhereIf(transactionIssueSeatId.HasValue, e => e.TransactionIssueSeatId == transactionIssueSeatId)
                    .WhereIf(isApproved.HasValue, e => e.IsApproved == isApproved)
                    .WhereIf(!string.IsNullOrWhiteSpace(approvedByUserName), e => e.ApprovedByUserName.Contains(approvedByUserName))
                    .WhereIf(approvedByUserId.HasValue, e => e.ApprovedByUserId == approvedByUserId)
                    .WhereIf(approvalDateMin.HasValue, e => e.ApprovalDate >= approvalDateMin.Value)
                    .WhereIf(approvalDateMax.HasValue, e => e.ApprovalDate <= approvalDateMax.Value)
                    .WhereIf(approvalDateTimeMin.HasValue, e => e.ApprovalDateTime >= approvalDateTimeMin.Value)
                    .WhereIf(approvalDateTimeMax.HasValue, e => e.ApprovalDateTime <= approvalDateTimeMax.Value)
                    .WhereIf(!string.IsNullOrWhiteSpace(notes), e => e.Notes.Contains(notes))
                    .WhereIf(isExecute.HasValue, e => e.IsExecute == isExecute)
                    .WhereIf(!string.IsNullOrWhiteSpace(executedByUserName), e => e.ExecutedByUserName.Contains(executedByUserName))
                    .WhereIf(executedByUserId.HasValue, e => e.ExecutedByUserId == executedByUserId)
                    .WhereIf(!string.IsNullOrWhiteSpace(executedByStaffName), e => e.ExecutedByStaffName.Contains(executedByStaffName))
                    .WhereIf(executedByStaffId.HasValue, e => e.ExecutedByStaffId == executedByStaffId)
                    .WhereIf(executedBySeatId.HasValue, e => e.ExecutedBySeatId == executedBySeatId)
                    .WhereIf(executeDateMin.HasValue, e => e.ExecuteDate >= executeDateMin.Value)
                    .WhereIf(executeDateMax.HasValue, e => e.ExecuteDate <= executeDateMax.Value)
                    .WhereIf(executeDateTimeMin.HasValue, e => e.ExecuteDateTime >= executeDateTimeMin.Value)
                    .WhereIf(executeDateTimeMax.HasValue, e => e.ExecuteDateTime <= executeDateTimeMax.Value)
                    .WhereIf(transferStatus.HasValue, e => e.TransferStatus == transferStatus)
                    .WhereIf(transferType.HasValue, e => e.TransferType == transferType);
        }

        public async Task<string> GenerateNewNoForMoneyExchangeTransferCurrencyByTransferType(MoneyExchangeTransferType transferType)
        {

            // Get the last transaction of the specified type in the current year
            var transactions = await(await GetQueryableAsync())
                .IgnoreQueryFilters()
                .Where(x => x.TransferType == transferType)
                .ToListAsync();

            var lastTransaction = transactions
              .OrderByDescending(x => int.Parse(x.No)) // Sort in memory
              .FirstOrDefault();

            // If no transaction exists for this type in the current year, start with 1
            if (lastTransaction == null)
            {
                return "1";
            }

            // Extract the last index and increment it by 1
            if (int.TryParse(lastTransaction.No, out int lastIndex))
            {
                return (lastIndex + 1).ToString();
            }

            // If parsing fails, return -1 as a fallback
            return "-1";
        }
    }
}