using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazorise;
using Blazorise.DataGrid;
using Volo.Abp.BlazoriseUI.Components;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.MoneyExchangeTransfersService.Shared;
using AutoMapper.Internal.Mappers;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.ServiceModel.Channels;
using Volo.Abp;
using EMPS.MoneyExchangeTransfersService.Blazor.Components;

namespace EMPS.MoneyExchangeTransfersService.Blazor.Pages.MoneyExchangeTransfersService.ServicePointToCompanyTransferPages
{
    public partial class ServicePointToCompanyTransferIssue
    {

        [Inject]
        private IServicePointToCompanyTransferService _ServicePointToCompanyTransferService { get; set; }



        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar { get; } = new PageToolbar();


        private bool CanCreateOrUpdateServicePointToCompanyTransferDraft { get; set; }
        private bool CanDeleteServicePointToCompanyTransferDraft { get; set; }

        private bool IsReadOnly { get; set; }


        private ServicePointToCompanyForm ServicePointToCompanyFormRef { get; set; }


        public ServicePointToCompanyTransferIssue()
        {
        }

        protected override async Task OnInitializedAsync()
        {

            await SetToolbarButtons(null);
            await SetBreadcrumbItemsAsync();
            await SetPermissionsAsync();
        }


        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:ServicePointToCompanyTransferIssue"]));


            return ValueTask.CompletedTask;
        }


        private async void ResetServicePointToCompanyTransfer(bool forceReset = false)
        {
            if (await ServicePointToCompanyFormRef.ResetServicePointToCompany(forceReset))
            {
                await SetToolbarButtons(null);
                SetFormIsReadOnly(null);
                StateHasChanged();

            }


        }

        private async Task SetPermissionsAsync()
        {
            CanCreateOrUpdateServicePointToCompanyTransferDraft = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Save);
            CanDeleteServicePointToCompanyTransferDraft = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Delete);
        }


        private async  Task<bool> ValidateForm(MoneyExchangeTransferCurrencyCreateDto FormServicePointToCompanyTransferDraft)
        {




            if (FormServicePointToCompanyTransferDraft.Amount <= 0)
            {
                await Message.Error(L["AmountMustBeLargeThanZero"]);
                return false;
            }
            if (!FormServicePointToCompanyTransferDraft.CurrencyId.HasValue)
            {
                await Message.Error(L["CurrencyFeildMustBeSelected"]);
                return false;
            }
            return true;

        }


        private void Log(string message)
        {
            Console.WriteLine($"MY-LOGGING: {message}");
        }

        private async Task<MoneyExchangeTransferCurrencyDto?> SaveTransfare()
        {
            var ServicePointToCompanyTransfer = ServicePointToCompanyFormRef.FormServicePointToCompany;
            MoneyExchangeTransferCurrencyCreateDto createDto = ObjectMapper.Map<MoneyExchangeTransferCurrencyDto, MoneyExchangeTransferCurrencyCreateDto>(ServicePointToCompanyTransfer);
            if (!await ValidateForm(createDto)) return null;

            var newServicePointToCompanyTransfer = await _ServicePointToCompanyTransferService.SaveAsync(createDto);
            ServicePointToCompanyFormRef.ChangeEntity(newServicePointToCompanyTransfer);
            return newServicePointToCompanyTransfer;
        }
        private async Task PerformSave()
        {
            await Task.Delay(300);


            if (!CanServicePointToCompanyTransferBeSaved(ServicePointToCompanyFormRef.FormServicePointToCompany))
            {
                await Message.Error(L["Error:ThisServicePointToCompanyTransferCannotBeUpdated"]);
                return;
            }
            Console.WriteLine("********************* :1");
            MoneyExchangeTransferCurrencyDto? newServicePointToCompanyTransfer = new();
            var ServicePointToCompanyTransfer = ServicePointToCompanyFormRef.FormServicePointToCompany;
            Console.WriteLine("********************* :2");



            await ServicePointToCompanyFormRef.loadingIndicator.Show();
            Console.WriteLine("********************* :3");

            try
            {
                newServicePointToCompanyTransfer = await SaveTransfare();

                if (newServicePointToCompanyTransfer != null)
                {
                    await SetToolbarButtons(newServicePointToCompanyTransfer);
                    await Message.Success(L["SaveSuccess"]);
                }


            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await ServicePointToCompanyFormRef.loadingIndicator.Hide();

            }

        }

        private void SetFormIsReadOnly(MoneyExchangeTransferCurrencyDto? ServicePointToCompanyTransfer)
        {
            IsReadOnly = ServicePointToCompanyTransfer != null && (ServicePointToCompanyTransfer.IsApproved || ServicePointToCompanyTransfer.IsExecute);
        }

        private bool CanServicePointToCompanyTransferBeSaved(MoneyExchangeTransferCurrencyDto? ServicePointToCompanyTransfer)
        {
            if (ServicePointToCompanyTransfer != null)
            {

                if (ServicePointToCompanyTransfer.IsApproved) return false;

                if (ServicePointToCompanyTransfer.IsExecute) return false;
            }

            return true;
        }

        private bool CanServicePointToCompanyTransferBeDeleted(MoneyExchangeTransferCurrencyDto? ServicePointToCompanyTransfer)
        {

            if (ServicePointToCompanyTransfer == null) return false;
            if (ServicePointToCompanyTransfer.Id == Guid.Empty) return false;
            if (ServicePointToCompanyTransfer.IsApproved) return false;

            if (ServicePointToCompanyTransfer.IsExecute) return false;

            return true;
        }



        private IReadOnlyList<string> stringList { get; set; }

        private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<string> e)
        {
        }

        private async Task HandleTransfareLoad(MoneyExchangeTransferCurrencyDto LoadedServicePointToCompanyTransfer)
        {
            await SetToolbarButtons(LoadedServicePointToCompanyTransfer);
            SetFormIsReadOnly(LoadedServicePointToCompanyTransfer);
            Log(IsReadOnly.ToString());
            StateHasChanged();

        }

        private async Task DeleteTransfareAsync(string _No)
        {
            await _ServicePointToCompanyTransferService.DeletedByNoAsync(_No);
            ResetServicePointToCompanyTransfer(true);
        }


        private async Task PerformDelete()
        {
            if (!ServicePointToCompanyFormRef.IsServicePointToCompanyLoaded())
            {
                await Message.Error(L["Error:LoadServicePointToCompanyTransferFirst"]);
                return;
            }
            if (!CanServicePointToCompanyTransferBeDeleted(ServicePointToCompanyFormRef.FormServicePointToCompany))
            {
                await Message.Error(L["Error:ThisServicePointToCompanyTransferCannotBeDeleted"]);
                return;
            }
            if (!await Message.Confirm(L["DeleteConfirmationMessage"])) return;
            await ServicePointToCompanyFormRef.loadingIndicator.Show();

            try
            {
                var ServicePointToCompanyTransferNo = ServicePointToCompanyFormRef.GetServicePointToCompanyNo();


                await DeleteTransfareAsync(ServicePointToCompanyTransferNo!);
                await Message.Success(L["SuccessfullyDeleted"]);

            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await ServicePointToCompanyFormRef.loadingIndicator.Hide();

            }
        }

        private async Task SetToolbarButtons(MoneyExchangeTransferCurrencyDto? servicePointToCompanyTransfer)
        {
            Toolbar.Contributors.Clear();

            Toolbar.AddButton(
         L["NewServicePointToCompanyTransfer"], () =>
         {
             ResetServicePointToCompanyTransfer();

             return Task.CompletedTask;

         }, IconName.Add, Color.Warning


     );

            Toolbar.AddButton(
                L["Save"], async () =>
                {
                    await PerformSave();
                }, IconName.Save, requiredPolicyName:
                MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Save, color: Color.Success, disabled: !CanServicePointToCompanyTransferBeSaved(servicePointToCompanyTransfer)
            );
            if (servicePointToCompanyTransfer != null && servicePointToCompanyTransfer.Id != Guid.Empty)
            {
                Toolbar.AddButton(
L["Delete"], async () =>
{
    await PerformDelete();
}, IconName.Delete, requiredPolicyName:
MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Delete, color: Color.Danger, disabled: !CanServicePointToCompanyTransferBeDeleted(servicePointToCompanyTransfer)
);
            }


            StateHasChanged();


        }

        private async Task HandleKeyDown(KeyboardEventArgs e)
        {
            if (e.Key == "F2")
            {
                await PerformSave();
            }
        }


    }
}
