﻿using EMPS.CompanyService;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Application;
using Volo.Abp.AutoMapper;
using Volo.Abp.Modularity;

namespace EMPS.MoneyExchangeTransfersService;

[DependsOn(
    typeof(MoneyExchangeTransfersServiceDomainModule),
    typeof(MoneyExchangeTransfersServiceApplicationContractsModule),
    typeof(AbpDddApplicationModule),
    typeof(AbpAutoMapperModule),
    typeof(CompanyServiceHttpApiClientModule))]
public class MoneyExchangeTransfersServiceApplicationModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddAutoMapperObjectMapper<MoneyExchangeTransfersServiceApplicationModule>();
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<MoneyExchangeTransfersServiceApplicationModule>(validate: true);
        });
    }
}
