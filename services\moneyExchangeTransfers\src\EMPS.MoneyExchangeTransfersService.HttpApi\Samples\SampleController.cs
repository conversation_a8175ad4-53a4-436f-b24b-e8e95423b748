﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;

namespace EMPS.MoneyExchangeTransfersService.Samples;

[RemoteService(Name = MoneyExchangeTransfersServiceRemoteServiceConsts.RemoteServiceName)]
[Area("MoneyExchangeTransfersService")]
[ControllerName("MoneyExchangeTransfersService")]
[Route("api/MoneyExchangeTransfersService/sample")]
public class SampleController : MoneyExchangeTransfersServiceController, ISampleAppService
{
    private readonly ISampleAppService _sampleAppService;

    public SampleController(ISampleAppService sampleAppService)
    {
        _sampleAppService = sampleAppService;
    }

    [HttpGet]
    public async Task<SampleDto> GetAsync()
    {
        return await _sampleAppService.GetAsync();
    }

    [HttpGet]
    [Route("authorized")]
    public async Task<SampleDto> GetAuthorizedAsync()
    {
        return await _sampleAppService.GetAsync();
    }
}
