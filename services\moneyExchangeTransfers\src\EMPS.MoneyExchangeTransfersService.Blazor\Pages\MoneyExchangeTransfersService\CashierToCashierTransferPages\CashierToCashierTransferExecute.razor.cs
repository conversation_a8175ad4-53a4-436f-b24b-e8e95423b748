using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Blazorise;
using Blazorise.DataGrid;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.Permissions;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using EMPS.MoneyExchangeTransfersService.Blazor.Components;

namespace EMPS.MoneyExchangeTransfersService.Blazor.Pages.MoneyExchangeTransfersService.CashierToCashierTransferPages
{
    public partial class CashierToCashierTransferExecute
    {

        [Inject]
        private ICashierToCashierTransferService _CashierToCashierTransferService { get; set; }



        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar { get; } = new PageToolbar();


        private bool CanExecuteCashierToCashierTransferDraft { get; set; }

        private bool IsReadOnly { get; set; }


        private CashierToCashierForm CashierToCashierFormRef { get; set; }


        public CashierToCashierTransferExecute()
        {
        }

        protected override async Task OnInitializedAsync()
        {

            await SetToolbarButtons(null);
            await SetBreadcrumbItemsAsync();
            await SetPermissionsAsync();
        }


        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:CashierToCashierTransferExecute"]));


            return ValueTask.CompletedTask;
        }


        private async void ResetCashierToCashierTransfer(bool forceReset = false)
        {
            if (await CashierToCashierFormRef.ResetCashierToCashierTransfer(forceReset))
            {
                await SetToolbarButtons(null);
                SetFormIsReadOnly(null);
                StateHasChanged();

            }


        }


        private async Task SetPermissionsAsync()
        {

            CanExecuteCashierToCashierTransferDraft = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Execute);
        }




        private void Log(string message)
        {
            Console.WriteLine($"MY-LOGGING: {message}");
        }



        private IReadOnlyList<string> stringList { get; set; }

        private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<string> e)
        {
        }

        private async Task HandleTransfareLoad(MoneyExchangeTransferCurrencyDto LoadedCashierToCashierTransfer)
        {
            await SetToolbarButtons(LoadedCashierToCashierTransfer);
            SetFormIsReadOnly(LoadedCashierToCashierTransfer);
            Log(IsReadOnly.ToString());
            StateHasChanged();

        }



        private async Task SetToolbarButtons(MoneyExchangeTransferCurrencyDto? accountPayment)
        {
            Toolbar.Contributors.Clear();




            Toolbar.AddButton(
         L["NewCashierToCashierTransfer"], () =>
         {
             ResetCashierToCashierTransfer();

             return Task.CompletedTask;

         }, IconName.Add, Color.Warning


     );

            Toolbar.AddButton(
                L["ExecuteCashierToCashierTransfer"], async () =>
                {
                    await PerformExecute();
                }, IconName.Save, requiredPolicyName:
                MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Execute, color: Color.Success, disabled: !CanCashierToCashierTransferBeExecute(accountPayment)
            );

            StateHasChanged();


        }

        private async Task PerformExecute()
        {
            await Task.Delay(300);


            if (CashierToCashierFormRef.GetCashierToCashierTransferId() == Guid.Empty)
            {
                await Message.Error(L["Error:LoadCashierToCashierTransferFirst"]);
                return;
            }
            if (!CanCashierToCashierTransferBeExecute(CashierToCashierFormRef.FormCashierToCashierTransfer))
            {
                await Message.Error(L["Error:ThisCashierToCashierTransferCannotBeExecuted"]);
                return;
            }

            var confirmation = await Message.Confirm(L["ExecuteCashierToCashierTransferConfirmationMessage"] + CashierToCashierFormRef.GetCashierToCashierTransferNo()!);
            if (!confirmation) return;

            await CashierToCashierFormRef.loadingIndicator.Show();

            try
            {

                var transfare = await _CashierToCashierTransferService.ExecuteByNoAsync(CashierToCashierFormRef.GetCashierToCashierTransferNo()!);

                await Message.Success(L["ExecutedSuccessfully"]);


                CashierToCashierFormRef.ChangeEntity(transfare);
                await SetToolbarButtons(transfare);
            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await CashierToCashierFormRef.loadingIndicator.Hide();

            }

        }

        private bool CanCashierToCashierTransferBeExecute(MoneyExchangeTransferCurrencyDto accountPayment)
        {



            if (accountPayment == null) return false;
            if (accountPayment.Id == Guid.Empty) return false;
            if (accountPayment.IsExecute) return false;

            return true;
        }

        private void SetFormIsReadOnly(MoneyExchangeTransferCurrencyDto? accountPayment)
        {

            this.IsReadOnly = false;
            if (accountPayment == null) return;
            if (accountPayment.IsExecute)
            {
                this.IsReadOnly = true;
            }
        }

        private async Task PerformNewTransfare()
        {
            ResetCashierToCashierTransfer();

        }
        public async Task HandleKeyDown(KeyboardEventArgs e)
        {

            ExecuteJS("event.stopPropagation();");

            if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyA"))
            {
                await PerformExecute();
            }
            else if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyR"))
            {
                await PerformNewTransfare();
            }
            else if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyG"))
            {
                await Task.Delay(1000);

                await CashierToCashierFormRef.PerformLoadCashierToCashierTransfer();
            }



        }
        private void ExecuteJS(string script)
        {
            JSRuntime.InvokeVoidAsync("eval", script);
        }
        [Inject] IJSRuntime JSRuntime { get; set; }
        protected override void OnAfterRender(bool firstRender)
        {
            base.OnAfterRender(firstRender);
            if (firstRender)
            {
                // See warning about memory above in the article
                var dotNetReference = DotNetObjectReference.Create(this);
                JSRuntime.InvokeVoidAsync("GetDotnetInstance", dotNetReference);
            }
        }






    }
}
