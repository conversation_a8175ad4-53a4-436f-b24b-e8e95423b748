using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazorise;
using Blazorise.DataGrid;
using Volo.Abp.BlazoriseUI.Components;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.MoneyExchangeTransfersService.Shared;
using AutoMapper.Internal.Mappers;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.ServiceModel.Channels;
using Volo.Abp;
using EMPS.MoneyExchangeTransfersService.Blazor.Components;

namespace EMPS.MoneyExchangeTransfersService.Blazor.Pages.MoneyExchangeTransfersService.CashierToCashierTransferPages
{
    public partial class CashierToCashierTransferIssue
    {

        [Inject]
        private ICashierToCashierTransferService _CashierToCashierTransferService { get; set; }



        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar { get; } = new PageToolbar();


        private bool CanCreateOrUpdateCashierToCashierTransferDraft { get; set; }
        private bool CanDeleteCashierToCashierTransferDraft { get; set; }

        private bool IsReadOnly { get; set; }


        private CashierToCashierForm CashierToCashierFormRef { get; set; }


        public CashierToCashierTransferIssue()
        {
        }

        protected override async Task OnInitializedAsync()
        {

            await SetToolbarButtons(null);
            await SetBreadcrumbItemsAsync();
            await SetPermissionsAsync();
        }


        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:CashierToCashierTransferIssue"]));


            return ValueTask.CompletedTask;
        }


        private async void ResetCashierToCashierTransfer(bool forceReset = false)
        {
            if (await CashierToCashierFormRef.ResetCashierToCashierTransfer(forceReset))
            {
                await SetToolbarButtons(null);
                SetFormIsReadOnly(null);
                StateHasChanged();

            }


        }

        private async Task<MoneyExchangeTransferCurrencyDto?> SaveTransfare()
        {
            var CashierToCashierTransfer = CashierToCashierFormRef.FormCashierToCashierTransfer;
            MoneyExchangeTransferCurrencyCreateDto createDto = ObjectMapper.Map<MoneyExchangeTransferCurrencyDto, MoneyExchangeTransferCurrencyCreateDto>(CashierToCashierTransfer);
            if (!await ValidateForm(createDto)) return null;

            var newCashierToCashierTransfer = await _CashierToCashierTransferService.SaveAsync(createDto);
            CashierToCashierFormRef.ChangeEntity(newCashierToCashierTransfer);
            return newCashierToCashierTransfer;
        }

        private async Task SetPermissionsAsync()
        {
            CanCreateOrUpdateCashierToCashierTransferDraft = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Save);
            CanDeleteCashierToCashierTransferDraft = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Delete);
        }


        private Task<bool> ValidateForm(MoneyExchangeTransferCurrencyCreateDto FormCashierToCashierTransferDraft)
        {

       


            return Task.FromResult(true);

        }


        private void Log(string message)
        {
            Console.WriteLine($"MY-LOGGING: {message}");
        }



        private IReadOnlyList<string> stringList { get; set; }

        private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<string> e)
        {
        }

        private async Task HandleTransfareLoad(MoneyExchangeTransferCurrencyDto LoadedCashierToCashierTransfer)
        {
            await SetToolbarButtons(LoadedCashierToCashierTransfer);
            SetFormIsReadOnly(LoadedCashierToCashierTransfer);
            Log(IsReadOnly.ToString());
            StateHasChanged();

        }

        private async Task DeleteTransfareAsync(string _No)
        {
            await _CashierToCashierTransferService.DeletedByNoAsync(_No);
            ResetCashierToCashierTransfer(true);
        }


        private async Task SetToolbarButtons(MoneyExchangeTransferCurrencyDto? accountPayment)
        {
            Toolbar.Contributors.Clear();


            Toolbar.AddButton(
                L["NewCashierToCashierTransfer"], () =>
                {
                    ResetCashierToCashierTransfer();

                    return Task.CompletedTask;

                }, IconName.Add, Color.Warning


            );

            Toolbar.AddButton(
                L["SaveCashierToCashierTransfer"], async () =>
                {
                    await PerformSave();
                }, IconName.Save, requiredPolicyName:
                MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Save, color: Color.Success, disabled: !CanCashierToCashierTransferBeUpdated(accountPayment)
            );
            if (accountPayment != null && accountPayment.Id != Guid.Empty)
            {

                Toolbar.AddButton(
                            L["Delete"], async () =>
                            {
                                await PerformDeleteTransfare();

                            }, IconName.Delete,
                            requiredPolicyName: MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Delete,
                            color: Color.Danger,
                            disabled: !CanCashierToCashierTransferBeDeleted(accountPayment)
                        );

            }

            StateHasChanged();


        }


        private bool CanCashierToCashierTransferBeDeleted(MoneyExchangeTransferCurrencyDto accountPayment)
        {



            if (accountPayment == null) return false;
            if (accountPayment.Id == Guid.Empty) return false;
            if (accountPayment.IsExecute) return false;

            return true;
        }
        private bool CanCashierToCashierTransferBeUpdated(MoneyExchangeTransferCurrencyDto accountPayment)
        {

            if (accountPayment != null)
                if (accountPayment.IsExecute) return false;

            return true;
        }

        private void SetFormIsReadOnly(MoneyExchangeTransferCurrencyDto? accountPayment)
        {

            this.IsReadOnly = false;
            if (accountPayment == null) return;
            if (accountPayment.IsExecute)
            {
                this.IsReadOnly = true;
            }
        }

        private async Task PerformSave()
        {
            await Task.Delay(300);


            if (!CanCashierToCashierTransferBeUpdated(CashierToCashierFormRef.FormCashierToCashierTransfer))
            {
                await Message.Error(L["Error:ThisCashierToCashierTransferCannotBeUpdated"]);
                return;
            }
            Console.WriteLine("********************* :1");
            MoneyExchangeTransferCurrencyDto? newCashierToCashierTransfer = new();
            var CashierToCashierTransfer = CashierToCashierFormRef.FormCashierToCashierTransfer;
            Console.WriteLine("********************* :2");



            await CashierToCashierFormRef.loadingIndicator.Show();
            Console.WriteLine("********************* :3");

            try
            {
                newCashierToCashierTransfer = await SaveTransfare();

                if (newCashierToCashierTransfer != null)
                {
                    await SetToolbarButtons(newCashierToCashierTransfer);
                    await Message.Success(L["SaveSuccess"]);
                }


            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await CashierToCashierFormRef.loadingIndicator.Hide();

            }

        }
        private async Task PerformDeleteTransfare()
        {
            if (!CashierToCashierFormRef.IsCashierToCashierTransferLoaded())
            {
                await Message.Error(L["Error:LoadCashierToCashierTransferFirst"]);
                return;
            }
            if (!CanCashierToCashierTransferBeDeleted(CashierToCashierFormRef.FormCashierToCashierTransfer))
            {
                await Message.Error(L["Error:ThisCashierToCashierTransferCannotBeDeleted"]);
                return;
            }
            if (!await Message.Confirm(L["DeleteConfirmationMessage"])) return;
            await CashierToCashierFormRef.loadingIndicator.Show();

            try
            {
                var CashierToCashierTransferNo = CashierToCashierFormRef.GetCashierToCashierTransferNo();


                await DeleteTransfareAsync(CashierToCashierTransferNo!);
                await Message.Success(L["SuccessfullyDeleted"]);

            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await CashierToCashierFormRef.loadingIndicator.Hide();

            }
        }
        private async Task PerformNewTransfare()
        {
            ResetCashierToCashierTransfer();

        }
        public async Task HandleKeyDown(KeyboardEventArgs e)
        {

            ExecuteJS("event.stopPropagation();");

            if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyD"))
            {
                await PerformDeleteTransfare();
            }
            else if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyR"))
            {
                await PerformNewTransfare();
            }
            else if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyG"))
            {
                await Task.Delay(50);
                await CashierToCashierFormRef.PerformLoadCashierToCashierTransfer();
            }
            else if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyS"))
            {
                await PerformSave();
            }


        }
        private void ExecuteJS(string script)
        {
            JSRuntime.InvokeVoidAsync("eval", script);
        }
        [Inject] IJSRuntime JSRuntime { get; set; }
        protected override void OnAfterRender(bool firstRender)
        {
            base.OnAfterRender(firstRender);
            if (firstRender)
            {
                // See warning about memory above in the article
                var dotNetReference = DotNetObjectReference.Create(this);
                JSRuntime.InvokeVoidAsync("GetDotnetInstance", dotNetReference);
            }
        }






    }
}
