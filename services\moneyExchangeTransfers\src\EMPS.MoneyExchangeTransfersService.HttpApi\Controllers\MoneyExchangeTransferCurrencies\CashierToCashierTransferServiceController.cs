using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using System.Collections.Generic;
using EMPS.CompanyService.Currencies;
using EMPS.CompanyService.Staffs;
using EMPS.CompanyService.ServicePoints;

namespace EMPS.MoneyExchangeTransfersService.Controllers.MoneyExchangeTransferCurrencies
{
    [RemoteService(Name = "MoneyExchangeTransfersService")]
    [Area("moneyExchangeTransfersService")]
    [ControllerName("CashierToCashierTransferService")]
    [Route("api/money-exchange-transfers-service/CashierToCashierTransferService")]
    public class CashierToCashierTransferServiceController : AbpController, ICashierToCashierTransferService
    {
        private readonly ICashierToCashierTransferService _cashierToCashierTransferService;

        public CashierToCashierTransferServiceController(ICashierToCashierTransferService cashierToCashierTransferService)
        {
            _cashierToCashierTransferService = cashierToCashierTransferService;
        }

        [HttpDelete]
        [Route("DeletedByNoAsync")]
        public Task DeletedByNoAsync(string no)
        {
            return _cashierToCashierTransferService.DeletedByNoAsync(no);
        }
        [HttpPut]
        [Route("ExecuteByNoAsync")]
        public Task<MoneyExchangeTransferCurrencyDto> ExecuteByNoAsync(string no)
        {
            return _cashierToCashierTransferService.ExecuteByNoAsync(no);
        }
        [HttpGet]
        [Route("GetAllActiveCurrenciesAsync")]
        public Task<List<CurrencyDto>> GetAllActiveCurrenciesAsync()
        {
            return _cashierToCashierTransferService.GetAllActiveCurrenciesAsync();
        }
        [HttpGet]
        [Route("GetAllCashierInServicePointAsync")]
        public Task<List<StaffDto>> GetAllCashierInServicePointAsync(Guid ServicePointId)
        {
            return _cashierToCashierTransferService.GetAllCashierInServicePointAsync(ServicePointId);
        }
        [HttpGet]
        [Route("GetStaffWithNavigationPropertiesAsync")]
        public Task<StaffWithNavigationPropertiesDto> GetStaffWithNavigationPropertiesAsync()
        {
            return _cashierToCashierTransferService.GetStaffWithNavigationPropertiesAsync();
        }

        [HttpGet]
        [Route("LoadByNoAsync")]
        public Task<MoneyExchangeTransferCurrencyDto?> LoadByNoAsync(string no)
        {
            return _cashierToCashierTransferService.LoadByNoAsync(no);
        }
        [HttpPost]
        [Route("SaveAsync")]
        public Task<MoneyExchangeTransferCurrencyDto> SaveAsync(MoneyExchangeTransferCurrencyCreateDto input)
        {
            return _cashierToCashierTransferService.SaveAsync(input);
        }
        [HttpGet]
        [Route("GetTransfersReadyToExecuteForCashier")]
        public Task<PagedResultDto<MoneyExchangeTransferCurrencyDto>> GetTransfersReadyToExecuteForCashier()
        {
            return _cashierToCashierTransferService.GetTransfersReadyToExecuteForCashier();
        }
    }
}