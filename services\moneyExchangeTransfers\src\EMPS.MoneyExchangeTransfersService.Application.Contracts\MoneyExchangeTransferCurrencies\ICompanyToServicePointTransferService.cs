﻿using EMPS.CompanyService.ServicePoints;
using EMPS.CompanyService.Staffs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
{
    public interface ICompanyToServicePointTransferService : IMoneyExchangeTransferBase
    {
        Task<List<ServicePointDto>> GetAllActiveServicePointAsync();
        Task<List<StaffDto>> GetAllCashierInServicePointAsync(Guid ServicePointId);
        Task<MoneyExchangeTransferCurrencyDto> ApproveByNoAsync(string no);

    }
}
