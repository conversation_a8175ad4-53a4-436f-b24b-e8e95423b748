using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.CashVouchers;
using EMPS.CompanyService.Application.Contracts.Dtos.Staffs;
using System.Collections.Generic;
using EMPS.CompanyService.Staffs;

namespace EMPS.MoneyExchangeTransfersService.Controllers.MoneyExchangeTransferCurrencies
{
    [RemoteService(Name = "MoneyExchangeTransfersService")]
    [Area("moneyExchangeTransfersService")]
    [ControllerName("PaymentCashVouchersController")]
    [Route("api/money-exchange-transfers-service/PaymentCashVouchersController")]
    public class PaymentCashVouchersController : AbpController, IPaymentCashVouchersAppService
    {
        private readonly IPaymentCashVouchersAppService paymentCashVouchersAppService;

        public PaymentCashVouchersController(IPaymentCashVouchersAppService paymentCashVouchersAppService)
        {
            this.paymentCashVouchersAppService = paymentCashVouchersAppService;
        }

        [HttpPut]
        [Route("ApproveByNoAsync")]
        public Task<CashVoucherDto> ApproveByNoAsync(string no)
        {
            return paymentCashVouchersAppService.ApproveByNoAsync(no);
        }

        [HttpDelete]
        [Route("DeletedByNoAsync")]
        public Task DeletedByNoAsync(string no)
        {
            return paymentCashVouchersAppService.DeletedByNoAsync(no);
        }

        [HttpPut]
        [Route("PayByNoAsync")]
        public Task<CashVoucherDto> PayByNoAsync(string no)
        {
            return paymentCashVouchersAppService.PayByNoAsync(no);
        }

        [HttpGet]
        [Route("{id}")]
        public Task<CashVoucherDto> GetAsync(Guid id)
        {
            return paymentCashVouchersAppService.GetAsync(id);
        }
        [HttpGet]

        public Task<PagedResultDto<CashVoucherDto>> GetListAsync(GetCashVouchersInput input)
        {
            return paymentCashVouchersAppService.GetListAsync(input);
        }
        [HttpGet]
        [Route("LoadByNoAsync")]
        public Task<CashVoucherDto?> LoadByNoAsync(string no)
        {
            return paymentCashVouchersAppService.LoadByNoAsync(no);
        }
        [HttpPut]
        [Route("SaveAsync")]
        public Task<CashVoucherDto> SaveAsync(CashVoucherCreateDto input)
        {
            return paymentCashVouchersAppService.SaveAsync(input);
        }
        [HttpGet]
        [Route("GetStaffSeatAsync")]
        public Task<StaffSeatDto?> GetStaffSeatAsync()
        {
            return paymentCashVouchersAppService.GetStaffSeatAsync();
        }
        [HttpGet]
        [Route("GetAllCashierInServicePointAsync")]
        public Task<List<StaffDto>> GetAllCashierInServicePointAsync(Guid ServicePointId)
        {
            return paymentCashVouchersAppService.GetAllCashierInServicePointAsync(ServicePointId);
        }
        [HttpGet]
        [Route("GetBlobByNameAsync")]
        public Task<byte[]> GetBlobByNameAsync(string Name)
        {
            return paymentCashVouchersAppService.GetBlobByNameAsync(Name);
        }
    }
}