﻿using EMPS.MoneyExchangeTransfersService.EntityFrameworkCore;
using Volo.Abp.Modularity;

namespace EMPS.MoneyExchangeTransfersService;

/* Domain tests are configured to use the EF Core provider.
 * You can switch to MongoDB, however your domain tests should be
 * database independent anyway.
 */
[DependsOn(
    typeof(MoneyExchangeTransfersServiceEntityFrameworkCoreTestModule)
    )]
public class MoneyExchangeTransfersServiceDomainTestModule : AbpModule
{

}
