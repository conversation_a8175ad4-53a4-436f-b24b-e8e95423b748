

// Ignore Spelling: Blazor

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Blazorise;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.JSInterop;
using Blazorise.LoadingIndicator;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.CompanyService.Currencies;
using EMPS.CompanyService.ServicePoints;
using EMPS.CompanyService.Staffs;




namespace EMPS.MoneyExchangeTransfersService.Blazor.Components
{
    public partial class CashierToCashierForm
    {

        [Inject]
        private IJSRuntime JSRuntime { get; set; }
        [Inject]
        private ICashierToCashierTransferService CashierToCashierTransferService { get; set; }
        [Inject]
        private IServicePointsAppService ServicePointsAppService { get; set; }

        [Parameter]
        public bool IsReadOnlyMode { get; set; }

        private bool CanDeleteCashierToCashierTransfer { get; set; }
        private bool CanCreateOrUpdateCashierToCashierTransfer { get; set; }

        [Parameter]
        public MoneyExchangeTransferCurrencyDto FormCashierToCashierTransfer { get; set; }

        private Validations FormCashierToCashierTransferValidations { get; set; } = new();

        private Guid EditingCashierToCashierTransferId { get; set; }

        [Parameter]
        public Func<MoneyExchangeTransferCurrencyDto, Task> OnCashierToCashierTransferLoaded { get; set; }

        public Func<Task<bool>> OnReset { get; set; }

        public bool IsCashierToCashierTransferLoadedProp { get; private set; }

        public LoadingIndicator loadingIndicator;

        private string LastStatusBy { get; set; }
        private string LastStatusDateTime { get; set; }
        private IReadOnlyList<CurrencyDto> CurrenciesCollection { get; set; } = new List<CurrencyDto>();
        private StaffWithNavigationPropertiesDto StaffWithNavigation { get; set; } = new();
        private IReadOnlyList<StaffDto> CashiersCollection { get; set; } = new List<StaffDto>();
        private async Task GetCurrenciesLookupAsync()
        {
            CurrenciesCollection = await CashierToCashierTransferService.GetAllActiveCurrenciesAsync();
        }

        private async Task GetCashiersLookupAsync()
        {
            CashiersCollection = await CashierToCashierTransferService.GetAllCashierInServicePointAsync(StaffWithNavigation.ServicePoint.Id);
        }
        private void SetLastStatus()
        {

            LastStatusBy = "";
            LastStatusDateTime = "";
            if (FormCashierToCashierTransfer == null) return;
            if (FormCashierToCashierTransfer.TransactionIssueUserId != null && FormCashierToCashierTransfer.TransactionIssueUserId != Guid.Empty)
            {
                LastStatusBy = L["CreatedByUserName"] + ": " + FormCashierToCashierTransfer.TransactionIssueUserName;
                var lastDate = FormCashierToCashierTransfer.LastModificationTime.HasValue ?
                    FormCashierToCashierTransfer.LastModificationTime : FormCashierToCashierTransfer.CreationTime;
                LastStatusDateTime = L["CreatedDate"] + ": " + lastDate;

            }


            if (FormCashierToCashierTransfer.IsExecute)
            {
                LastStatusBy = L["ExecutedByUserName"] + ": " + FormCashierToCashierTransfer.ExecutedByUserName;
                LastStatusDateTime = L["ExecuteDate"] + ": " + FormCashierToCashierTransfer.ExecuteDateTime; ;
            }



        }
        public CashierToCashierForm()
        {
            FormCashierToCashierTransfer = new();
        }
        protected override async Task OnInitializedAsync()
        {

            await SetPermissionsAsync();
            await GetCurrenciesLookupAsync();
            StaffWithNavigation = await GetStaff();
            await GetCashiersLookupAsync();

        }
        private async Task<StaffWithNavigationPropertiesDto> GetStaff()
        {
            return await CashierToCashierTransferService.GetStaffWithNavigationPropertiesAsync();
        }
        private async Task SetPermissionsAsync()
        {
            CanCreateOrUpdateCashierToCashierTransfer = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Save);
            CanDeleteCashierToCashierTransfer = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Delete);

        }
        public async Task<bool> ResetCashierToCashierTransfer(bool forceReset = false)
        {
            if (!forceReset)
                if (!await Message.Confirm(L["ResetConfirmationMessage"])) return false;
            FormCashierToCashierTransfer = new();
            StaffWithNavigation = new();

            SetLastStatus();
            StateHasChanged();
            IsCashierToCashierTransferLoadedProp = false;
            return true;

        }
        public string? GetCashierToCashierTransferNo()
        {
            return FormCashierToCashierTransfer.No;
        }
        public Guid GetCashierToCashierTransferId()
        {
            return FormCashierToCashierTransfer.Id;
        }
        public bool IsCashierToCashierTransferLoaded()
        {
            return IsCashierToCashierTransferLoadedProp;

        }
        public bool IsFormReadOnly()
        {
            if (IsReadOnlyMode) return true;
            if (FormCashierToCashierTransfer.IsExecute) return true;

            return false;
        }
        private async Task LoadCashierToCashierTransfer()
        {

            var LoadResult = await GetLoadResult();
            if (LoadResult == null)
            {
                _ = Message.Error(L["CashierToCashierTransferNotFound"]);
                return;
            }

            FormCashierToCashierTransfer = LoadResult;


            OnCashierToCashierTransferLoaded?.Invoke(LoadResult);
            IsCashierToCashierTransferLoadedProp = true;

            SetLastStatus();
            StateHasChanged();

        }
        private async Task<MoneyExchangeTransferCurrencyDto?> GetLoadResult()
        {
            return await CashierToCashierTransferService.LoadByNoAsync(FormCashierToCashierTransfer.No!);
        }
        private async Task LoadModelByCashierToCashierTransferNo(KeyboardEventArgs e)
        {
            await Task.Delay(50);

            if (e.Key != "Enter") return;

            if (string.IsNullOrEmpty(FormCashierToCashierTransfer.No)) return;


            await PerformLoadCashierToCashierTransfer();

            await Task.CompletedTask;

        }
        public async Task PerformLoadCashierToCashierTransfer()
        {

            try
            {
                await loadingIndicator.Show();

                await LoadCashierToCashierTransfer();
                StateHasChanged();


            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await loadingIndicator.Hide();

            }
        }
        public void ChangeEntity(MoneyExchangeTransferCurrencyDto CashierToCashierTransfer)
        {
            this.FormCashierToCashierTransfer = CashierToCashierTransfer;
            IsCashierToCashierTransferLoadedProp = true;
            SetLastStatus();
            StateHasChanged();
        }

        public async Task OnChangeCurrency(Guid? id)
        {
            FormCashierToCashierTransfer.CurrencyId = id;
            if (FormCashierToCashierTransfer.CurrencyId == null)
            {
                //rest all
            }
            //get avgCost for this currency 
            //method have 2 parameter currency id and cashier id 

            //avgCost = avgCost for currency
            StateHasChanged();

        }




    }
}
