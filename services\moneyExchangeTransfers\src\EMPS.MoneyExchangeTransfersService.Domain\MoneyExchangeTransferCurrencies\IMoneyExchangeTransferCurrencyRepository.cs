using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
{
    public interface IMoneyExchangeTransferCurrencyRepository : IRepository<MoneyExchangeTransferCurrency, Guid>
    {
        Task<string> GenerateNewNoForMoneyExchangeTransferCurrencyByTransferType(MoneyExchangeTransferType transferType);

        Task<List<MoneyExchangeTransferCurrency>> GetListAsync(
            string filterText = null,
            string no = null,
            double? amountMin = null,
            double? amountMax = null,
            string currencyCode = null,
            Guid? currencyId = null,
            string servicePointName = null,
            Guid? servicePointId = null,
            string transactionIssueUserName = null,
            Guid? transactionIssueUserId = null,
            string transactionIssueStaffName = null,
            Guid? transactionIssueStaffId = null,
            Guid? transactionIssueSeatId = null,
            bool? isApproved = null,
            string approvedByUserName = null,
            Guid? approvedByUserId = null,
            DateTime? approvalDateMin = null,
            DateTime? approvalDateMax = null,
            DateTime? approvalDateTimeMin = null,
            DateTime? approvalDateTimeMax = null,
            string notes = null,
            bool? isExecute = null,
            string executedByUserName = null,
            Guid? executedByUserId = null,
            string executedByStaffName = null,
            Guid? executedByStaffId = null,
            Guid? executedBySeatId = null,
            DateTime? executeDateMin = null,
            DateTime? executeDateMax = null,
            DateTime? executeDateTimeMin = null,
            DateTime? executeDateTimeMax = null,
            MoneyExchangeTransferStatus? transferStatus = null,
            MoneyExchangeTransferType? transferType = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default
        );

        Task<long> GetCountAsync(
            string filterText = null,
            string no = null,
            double? amountMin = null,
            double? amountMax = null,
            string currencyCode = null,
            Guid? currencyId = null,
            string servicePointName = null,
            Guid? servicePointId = null,
            string transactionIssueUserName = null,
            Guid? transactionIssueUserId = null,
            string transactionIssueStaffName = null,
            Guid? transactionIssueStaffId = null,
            Guid? transactionIssueSeatId = null,
            bool? isApproved = null,
            string approvedByUserName = null,
            Guid? approvedByUserId = null,
            DateTime? approvalDateMin = null,
            DateTime? approvalDateMax = null,
            DateTime? approvalDateTimeMin = null,
            DateTime? approvalDateTimeMax = null,
            string notes = null,
            bool? isExecute = null,
            string executedByUserName = null,
            Guid? executedByUserId = null,
            string executedByStaffName = null,
            Guid? executedByStaffId = null,
            Guid? executedBySeatId = null,
            DateTime? executeDateMin = null,
            DateTime? executeDateMax = null,
            DateTime? executeDateTimeMin = null,
            DateTime? executeDateTimeMax = null,
            MoneyExchangeTransferStatus? transferStatus = null,
            MoneyExchangeTransferType? transferType = null,
            CancellationToken cancellationToken = default);
    }
}