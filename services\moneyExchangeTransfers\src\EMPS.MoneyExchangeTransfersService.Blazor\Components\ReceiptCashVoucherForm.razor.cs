

// Ignore Spelling: Blazor

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Blazorise;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.JSInterop;
using Blazorise.LoadingIndicator;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.CompanyService.Currencies;
using EMPS.CompanyService.ServicePoints;
using EMPS.CompanyService.Staffs;
using System.Linq;
using EMPS.MoneyExchangeTransfersService.CashVouchers;
using EMPS.CompanyService.Application.Contracts.Dtos.Staffs;
using System.IO;
using System.Text;
using System.Net.Http;
using System.Text.Json;
using Volo.Abp;
using Microsoft.Extensions.Configuration;
using EMPS.CompanyService.CentralVaults;




namespace EMPS.MoneyExchangeTransfersService.Blazor.Components
{
    public partial class ReceiptCashVoucherForm
    {

        [Inject]
        private IJSRuntime JSRuntime { get; set; }

        [Inject]
        private IReceiptCashVouchersAppService ReceiptCashVouchersAppService { get; set; }
        [Inject]
        private ICurrenciesAppService CurrenciesAppService { get; set; }

        [Inject]
        private IServicePointsAppService ServicePointsAppService { get; set; }
        [Inject]
        private ICentralVaultsAppService CentralVaultsAppService { get; set; }

        [Inject]
        public IConfiguration configuration { get; set; }
        [Inject]
        public HttpClient httpClient { get; set; }
        [Parameter]
        public bool IsReadOnlyMode { get; set; }
        public SeatLocationType SeatLocation { get; set; }

        private bool CanDeleteReceiptVoucher { get; set; }
        private bool CanCreateOrUpdateReceiptVoucher { get; set; }

        [Parameter]
        public CashVoucherDto FormReceiptVoucher { get; set; }

        private Validations FormReceiptVoucherValidations { get; set; } = new();

        private Guid EditingReceiptVoucherId { get; set; }

        [Parameter]
        public Func<CashVoucherDto, Task> OnReceiptVoucherLoaded { get; set; }

        public Func<Task<bool>> OnReset { get; set; }

        public bool IsReceiptVoucherLoadedProp { get; private set; }

        public LoadingIndicator loadingIndicator;

        private string LastStatusBy { get; set; }
        private string LastStatusDateTime { get; set; }
        private IReadOnlyList<CurrencyDto> CurrenciesCollection { get; set; } = new List<CurrencyDto>();
        private IReadOnlyList<ServicePointDto> ServicePointsCollection { get; set; } = new List<ServicePointDto>();
        private IReadOnlyList<CentralVaultDto> CentralVaultCollection { get; set; } = new List<CentralVaultDto>();

        public bool DisabledChangeCashierType { get; set; }

        public StaffSeatDto? StaffSeat { get; set; } = new();
        private IReadOnlyList<StaffDto> CashiersCollection { get; set; } = new List<StaffDto>();
        private string fileValidationError = string.Empty;

        private string ScannedImageBase64 = string.Empty;
        private Modal ScanDocumentModal;
        private string ScannedFileName { get; set; }

        private async Task GetCashiersLookupAsync(Guid servicePointId)
        {
            CashiersCollection = await ReceiptCashVouchersAppService.GetAllCashierInServicePointAsync(servicePointId);
        }
        private async Task GetCurrenciesLookupAsync()
        {
            CurrenciesCollection = await CurrenciesAppService.GetActiveAsync(true);
            CurrenciesCollection.Where(x => x.CurrencyType == CurrencyType.Local).ToList();
            if (CurrenciesCollection.Count == 0) throw new UserFriendlyException(L["CurrenciesCollectionHaveNoLocalCurrency"]);
            await OnChangeCurrency(CurrenciesCollection.First().Id);

        }
        private async Task GetServicePointsLookupAsync()
        {
            ServicePointsCollection = await ServicePointsAppService.GetAllActiveServicePointsLookupAsync();
        }
        private async Task GetCentralVaultsLookupAsync()
        {
            CentralVaultCollection = await CentralVaultsAppService.GetActiveVaults();
        }

        private void SetLastStatus()
        {

            LastStatusBy = "";
            LastStatusDateTime = "";
            if (FormReceiptVoucher == null) return;
            if (FormReceiptVoucher.IssueUserId != null && FormReceiptVoucher.IssueUserId != Guid.Empty)
            {
                LastStatusBy = L["CreatedByUserName"] + ": " + FormReceiptVoucher.IssueUserName;

                LastStatusDateTime = L["CreatedDate"] + ": " + FormReceiptVoucher.IssueDateTime;

            }
            if (FormReceiptVoucher.IsApproved)
            {
                LastStatusBy = L["ApproveByUserName"] + ": " + FormReceiptVoucher.ApprovedByUserName;
                LastStatusDateTime = L["ApproveDate"] + ": " + FormReceiptVoucher.ApprovalDateTime;
            }



            if (FormReceiptVoucher.IsExecute)
            {
                LastStatusBy = L["ReceiptByUserName"] + ": " + FormReceiptVoucher.ExecutedByUserName;
                LastStatusDateTime = L["ReceiptDate"] + ": " + FormReceiptVoucher.ExecuteDateTime; ;
            }



        }
        public ReceiptCashVoucherForm()
        {
            FormReceiptVoucher = new();
        }
        protected override async Task OnInitializedAsync()
        {

            await SetPermissionsAsync();
            await GetCurrenciesLookupAsync();
            await GetServicePointsLookupAsync();
            StaffSeat = await GetStaff();

        }
        private async Task<StaffSeatDto?> GetStaff()
        {
            var StaffSeat = await ReceiptCashVouchersAppService.GetStaffSeatAsync();
            if (StaffSeat != null)
            {
                if (StaffSeat.IsSeatForCentralVault)
                {

                    CentralVaultCollection= CentralVaultCollection.Where(x => x.Id == StaffSeat.AssignedLocationId).ToList();
                    await GetCashiersLookupAsync(CentralVaultCollection.First().Id);

                    DisabledChangeCashierType = true;
                    SeatLocation = SeatLocationType.CentralVault;
                    FormReceiptVoucher.SeatLocationId = StaffSeat.AssignedLocationId;

                }
                if (StaffSeat.IsSeatForServicePoint)
                {
                    DisabledChangeCashierType = true;
                    ServicePointsCollection = ServicePointsCollection.Where(x => x.Id == StaffSeat.AssignedLocationId).ToList();
                    await GetCashiersLookupAsync(ServicePointsCollection.First().Id);
                    SeatLocation = SeatLocationType.ServicePoint;
                    FormReceiptVoucher.SeatLocationId=StaffSeat.AssignedLocationId;
                }
                else
                {
                    SeatLocation = SeatLocationType.Company;
                    DisabledChangeCashierType = false;

                }

            }

            await ChangeCashierSeat(SeatLocation);
            return StaffSeat;
        }
        private async Task SetPermissionsAsync()
        {
            CanCreateOrUpdateReceiptVoucher = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Save);
            CanDeleteReceiptVoucher = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Delete);

        }
        public async Task<bool> ResetReceiptVoucher(bool forceReset = false)
        {
            if (!forceReset)
                if (!await Message.Confirm(L["ResetConfirmationMessage"])) return false;
            FormReceiptVoucher = new();
            SeatLocation = 0;

            await GetCurrenciesLookupAsync();
            await GetServicePointsLookupAsync();
            StaffSeat = await GetStaff();
            SetLastStatus();
            IsReceiptVoucherLoadedProp = false;
            StateHasChanged();
            return true;

        }
        public string? GetReceiptVoucherNo()
        {
            return FormReceiptVoucher.No;
        }
        public Guid GetReceiptVoucherId()
        {
            return FormReceiptVoucher.Id;
        }
        public bool IsReceiptVoucherLoaded()
        {
            return IsReceiptVoucherLoadedProp;

        }
        public bool IsFormReadOnly()
        {
            if (IsReadOnlyMode) return true;
            if (FormReceiptVoucher.IsExecute) return true;

            return false;
        }
        private async Task LoadReceiptVoucher()
        {

            var LoadResult = await GetLoadResult();
            if (LoadResult == null)
            {
                _ = Message.Error(L["ReceiptVoucherNotFound"]);
                return;
            }

            FormReceiptVoucher = LoadResult;
            SeatLocation = FormReceiptVoucher.AssignSeatLocation;
            if (SeatLocation != SeatLocationType.Company)
                await GetCashiersLookupAsync(FormReceiptVoucher.SeatLocationId!.Value);
            if (FormReceiptVoucher.BolbKey != null)
            {
                FormReceiptVoucher.BlobContent = await ReceiptCashVouchersAppService.GetBlobByNameAsync(FormReceiptVoucher.BolbKey);
            }

            OnReceiptVoucherLoaded?.Invoke(LoadResult);
            IsReceiptVoucherLoadedProp = true;

            SetLastStatus();
            StateHasChanged();

        }
        private async Task<CashVoucherDto?> GetLoadResult()
        {
            return await ReceiptCashVouchersAppService.LoadByNoAsync(FormReceiptVoucher.No!);
        }
        private async Task LoadModelByReceiptVoucherNo(KeyboardEventArgs e)
        {
            await Task.Delay(50);

            if (e.Key != "Enter") return;

            if (string.IsNullOrEmpty(FormReceiptVoucher.No)) return;


            await PerformLoadReceiptVoucher();

            await Task.CompletedTask;

        }
        public async Task PerformLoadReceiptVoucher()
        {

            try
            {
                await loadingIndicator.Show();

                await LoadReceiptVoucher();
                StateHasChanged();


            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await loadingIndicator.Hide();

            }
        }
        public void ChangeEntity(CashVoucherDto ReceiptVoucher)
        {
            this.FormReceiptVoucher = ReceiptVoucher;
            IsReceiptVoucherLoadedProp = true;
            SetLastStatus();
            StateHasChanged();
        }

        public async Task OnChangeCurrency(Guid? id)
        {
            FormReceiptVoucher.CurrencyId = id;
            if (FormReceiptVoucher.CurrencyId == null)
            {
                //rest all
            }
            //get avgCost for this currency 
            //method have 2 parameter currency id and cashier id 

            //avgCost = avgCost for currency
            StateHasChanged();

        }

        public async Task OnChangeServicePoint(Guid? id)
        {
            FormReceiptVoucher.SeatLocationId = id;
            if (FormReceiptVoucher.SeatLocationId == null)
            {
                CashiersCollection = new List<StaffDto>();
            }
            else
            {
                Console.WriteLine("spId :" + FormReceiptVoucher.SeatLocationId.ToString());
                await GetCashiersLookupAsync(FormReceiptVoucher.SeatLocationId.Value);
            }

            StateHasChanged();

        }
        public async Task OnChangeCentralVault(Guid? id)
        {
            FormReceiptVoucher.SeatLocationId = id;
            if (FormReceiptVoucher.SeatLocationId == null)
            {
                CashiersCollection = new List<StaffDto>();
            }
            else
            {
                await GetCashiersLookupAsync(FormReceiptVoucher.SeatLocationId!.Value);
            }

            StateHasChanged();

        }
        public async Task ChangeCashierSeat(SeatLocationType locationType)
        {
            // Update the seat location
            SeatLocation = locationType;

            // Clear/reset dependent fields when changing cashier type
            if (locationType == SeatLocationType.Company)
            {
                // Clear Service Point and Cashier selections when Company is selected
                FormReceiptVoucher.SeatLocationId = null;
                FormReceiptVoucher.ExecutedByStaffId = null;

                // Clear the collections to reset dropdowns
                CashiersCollection = new List<StaffDto>();
            }
            else if (locationType == SeatLocationType.ServicePoint)
            {
                // Clear only the cashier selection, keep service point available for selection
                FormReceiptVoucher.ExecutedByStaffId = null;
                CashiersCollection = new List<StaffDto>();

                // If a service point was previously selected, reload cashiers for that service point
                if (FormReceiptVoucher.SeatLocationId.HasValue)
                {
                    await GetCashiersLookupAsync(FormReceiptVoucher.SeatLocationId.Value);
                }
            }
            else if (locationType == SeatLocationType.CentralVault)
            {
                // Clear Service Point and Cashier selections for Central Vault
                FormReceiptVoucher.SeatLocationId = null;
                FormReceiptVoucher.ExecutedByStaffId = null;
                CashiersCollection = new List<StaffDto>();
            }

            // Trigger UI update
            StateHasChanged();
        }
        private async Task OnFileChanged(FileChangedEventArgs e)
        {
            fileValidationError = string.Empty;


            var file = e.Files.FirstOrDefault();
            if (file != null)
            {
                //if (file.Type != "application/pdf")
                //{
                //    fileValidationError = "Only PDF files are allowed";
                //    return;
                //}
                using (var stream = new MemoryStream())
                {
                    await file.WriteToStreamAsync(stream);
                    stream.Position = 0;
                    StreamReader StreamReader = new StreamReader(stream, Encoding.ASCII);
                    char[] ReadValue = new char[stream.Length];

                    FormReceiptVoucher.BlobContent = stream.ToArray();
                    FormReceiptVoucher.FileName = file.Name;
                    FormReceiptVoucher.FileSize = file.Size.ToString();
                    FormReceiptVoucher.FileExtension = Path.GetExtension(file.Name);
                    StreamReader.Close();
                    StateHasChanged();
                }
            }

        }

        private async Task DisplayFileInNewBrowserTab()
        {
            if (FormReceiptVoucher.FileExtension == null)
            {
                return;
            }
            string filetype = string.Empty;

            if (FormReceiptVoucher.FileExtension.ToUpper().Contains("PDF"))
            {
                filetype = "pdf";
            }
            else
            {
                filetype = "image";

            }
            await JSRuntime.InvokeAsync<object>("openInNewTab", FormReceiptVoucher.BlobContent, filetype, FormReceiptVoucher.FileExtension, FormReceiptVoucher.FileName);

        }
        void OnDeleteAttachmentClicked()
        {
            FormReceiptVoucher.BlobContent = null;
            FormReceiptVoucher.FileName = null;
            FormReceiptVoucher.FileSize = null;
            FormReceiptVoucher.FileExtension = null;
            StateHasChanged();
        }
        private async Task FetchAndDisplayImage()
        {
            try
            {
                await loadingIndicator.Show();

                var _baseUrl = configuration["MISAgent:BaseUrl"];
                Console.WriteLine("baseUrl: " + _baseUrl);
                //var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/Agent/ScanAsync");
                var request = new HttpRequestMessage(HttpMethod.Get, $"{"https://localhost:7000"}/Barcode/ScanAsync");

                using var response = await httpClient.SendAsync(request);
                Console.WriteLine($"response: {response}");

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Response Data: {responseData}"); // Log the raw response

                    var jsonResponse = JsonSerializer.Deserialize<JsonElement>(responseData);

                    // Check if the request was successful
                    if (jsonResponse.GetProperty("success").GetBoolean())
                    {
                        // Extract the base64 image data
                        var base64ImageData = jsonResponse.GetProperty("data").GetString();

                        if (!string.IsNullOrWhiteSpace(base64ImageData))
                        {
                            // Set the ScannedImageBase64 to the extracted base64 data
                            ScannedImageBase64 = base64ImageData;
                            StateHasChanged();
                            await ShowScanModal();
                            Console.WriteLine($"Base64 Image Data: {ScannedImageBase64.Substring(0, 50)}..."); // Log first 50 chars of the base64 data
                        }
                        else
                        {
                            Console.WriteLine("Base64 image data is empty.");
                        }
                    }
                    else
                    {
                        // Handle the case where the request was not successful
                        Console.WriteLine("Failed to get image data.");
                    }
                }
                else
                {
                    Console.WriteLine($"Error: {response.StatusCode}");
                }
                await loadingIndicator.Hide();
            }
            catch (Exception ex)
            {
                await loadingIndicator.Hide();
                return;
            }

        }
        private Task ShowScanModal()
        {
            ScannedFileName = string.Empty;
            return ScanDocumentModal.Show();
        }
        private Task CloseScanDocumentModal()
        {
            return ScanDocumentModal.Hide();
        }

        private async Task AcceptDocumentScanned()
        {
            if (ScannedFileName.IsNullOrWhiteSpace())
                throw new UserFriendlyException(L["ScannedFileNameIsRequired"]);
            FormReceiptVoucher.BlobContent = ConvertBase64ToByteArray(ScannedImageBase64);
            FormReceiptVoucher.FileSize = FormReceiptVoucher.BlobContent.Length.ToString();
            FormReceiptVoucher.FileExtension = "png";
            FormReceiptVoucher.FileName = $"{ScannedFileName}.png";
            await ScanDocumentModal.Hide();
            StateHasChanged();
        }

        private byte[] ConvertBase64ToByteArray(string base64String)
        {
            // Remove the data URL prefix if present
            if (base64String.Contains(","))
            {
                base64String = base64String.Substring(base64String.IndexOf(",") + 1);
            }

            return Convert.FromBase64String(base64String);
        }
    }
}
