﻿using EMPS.CompanyService.Currencies;
using EMPS.CompanyService.ServicePoints;
using EMPS.CompanyService.ServicePointSeats;
using EMPS.CompanyService.Staffs;
using EMPS.MoneyExchangeTransfersService.Permissions;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Identity;

namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
{
    [Authorize(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Default)]

    public class CashierToCashierTransferService : MoneyExchangeTransferBase, ICashierToCashierTransferService
    {
        public CashierToCashierTransferService(IMoneyExchangeTransferCurrencyRepository moneyExchangeTransferCurrencyRepository, IStaffsAppService staffsAppService, IServicePointSeatsAppService servicePointSeatsAppService, IOrganizationUnitAppService organizationUnitAppService, IServicePointsAppService servicePointsAppService, ICurrenciesAppService currencyAppService, IIdentityUserAppService identityUserAppService) : base(moneyExchangeTransferCurrencyRepository, staffsAppService, servicePointSeatsAppService, organizationUnitAppService, servicePointsAppService, currencyAppService, identityUserAppService)
        {
        }

        protected override async Task<string?> GenerateNewNumberForMoneyExchangeTransfer()
        {
            return await _moneyExchangeTransferCurrencyRepository.GenerateNewNoForMoneyExchangeTransferCurrencyByTransferType(MoneyExchangeTransferType.CashierToCashier);
        }

        protected override async Task<MoneyExchangeTransferCurrency?> GetRequiredMoneyExchangeTransfer(string no)
        {

            var query = (await _moneyExchangeTransferCurrencyRepository.GetQueryableAsync())
                  .Where(x => x.No == no && x.TransferType == MoneyExchangeTransferType.CashierToCashier)
                  .OrderByDescending(x => x.CreationTime).FirstOrDefault();
            return query;
        }

        protected override async Task<MoneyExchangeTransferCurrency> SetBusinessLogicFromInheritanceClassesForSaveAsync(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency, MoneyExchangeTransferCurrencyCreateDto moneyExchange)
        {
            var Staff = await CheckStaffInformationAsync();
            var StaffSeat = await CheckSeatInformationAsync(Staff);
            if (!moneyExchange.ExecutedByStaffId.HasValue) throw new UserFriendlyException(L["TheReciverStaffMustBeSelected"]);
            var reciverStaff = await CheckStaffInformationAsync(moneyExchange.ExecutedByStaffId);
            var reciverStaffSeat = await CheckSeatInformationAsync(reciverStaff);
            if (reciverStaff.Staff.Id == Staff.Staff.Id) throw new UserFriendlyException(L["TheReciverStaffMustBeAnotherThanSenderStaff"]);
            if (reciverStaffSeat.ServicePointId != StaffSeat.ServicePointId) throw new UserFriendlyException(L["TwoCashirIsNotInSameServicePoint"]);
            CheckServicePointInformation(Staff.ServicePoint);
            moneyExchangeTransferCurrency!.FillServicePointInfo(Staff.ServicePoint.Name, Staff.ServicePoint.Id);

            moneyExchangeTransferCurrency.ExecutedByStaffId = reciverStaff.Staff.Id;
            moneyExchangeTransferCurrency.TransferType = MoneyExchangeTransferType.CashierToCashier;
            moneyExchangeTransferCurrency.FillTransactionIssueInfo(GetFullUserName(), GeTUserId(), $"{Staff.Staff.Name} {Staff.Staff.Surname}", Staff.Staff.Id, StaffSeat.Id);
            return moneyExchangeTransferCurrency;


        }
        protected override Task<MoneyExchangeTransferCurrency> SetBusinessLogicFromInheritanceClassesForApproveAsync(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency)
        {
            throw new NotImplementedException();
        }

        protected override async Task<MoneyExchangeTransferCurrency> SetBusinessLogicFromInheritanceClassesForExecuteAsync(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency)
        {
            var ReciverStaff = await CheckStaffInformationAsync();
            var ReciverStaffSeat = await CheckSeatInformationAsync(ReciverStaff);
            if (ReciverStaff.Seat.StaffId != moneyExchangeTransferCurrency.ExecutedByStaffId) throw new UserFriendlyException(L["TheReciverStaffIsNotTheUserThatAddedInIssueToExecute"]);
            if (ReciverStaffSeat.ServicePointId != moneyExchangeTransferCurrency.ServicePointId) throw new UserFriendlyException(L["ThisOrderNotReleasedFromSameServicePoint"]);

            var SenderStaff = await CheckStaffInformationAsync(moneyExchangeTransferCurrency.TransactionIssueStaffId);
            var SenderStaffSeat = await CheckSeatInformationAsync(SenderStaff);
            if (SenderStaff.Staff.Id == ReciverStaff.Staff.Id) throw new UserFriendlyException(L["TheReciverStaffMustBeAnotherThanSenderStaff"]);
            if (SenderStaffSeat.ServicePointId != ReciverStaffSeat.ServicePointId) throw new UserFriendlyException(L["TwoCashirIsNotInSameServicePoint"]);
            //Check BalanceTraking if currency is LC (SYP)
            //Check Leger if currency is FC
            //Assert on BalanceTraking if currency is LC (SYP)
            //Assert on Leger if currency is FC
            moneyExchangeTransferCurrency.FillExecutionInfo(GetFullUserName(), GeTUserId(), $"{ReciverStaff.Staff.Name} {ReciverStaff.Staff.Surname}", ReciverStaff.Staff.Id, ReciverStaffSeat.Id);
            return moneyExchangeTransferCurrency;
        }

        protected override async Task<MoneyExchangeTransferCurrency?> CheckRequiredBusinessLogicInUpdatingMode(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency)
        {
            var Staff = await CheckStaffInformationAsync();
            var StaffSeat = await CheckSeatInformationAsync(Staff);
            if (moneyExchangeTransferCurrency.TransactionIssueSeatId != StaffSeat.Id) throw new UserFriendlyException(L["ThisCashierIsNotTheOneWhoCreateTheOrder"]);

            return moneyExchangeTransferCurrency;
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Save)]
        public override Task<MoneyExchangeTransferCurrencyDto> SaveAsync(MoneyExchangeTransferCurrencyCreateDto input)
        {
            return base.SaveAsync(input);
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Execute)]
        public override Task<MoneyExchangeTransferCurrencyDto> ExecuteByNoAsync(string no)
        {
            return base.ExecuteByNoAsync(no);
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Delete)]
        public override Task DeletedByNoAsync(string no)
        {
            return base.DeletedByNoAsync(no);
        }

        public async Task<StaffWithNavigationPropertiesDto> GetStaffWithNavigationPropertiesAsync()
        {
            var Staff = await CheckStaffInformationAsync();
            var StaffSeat = await CheckSeatInformationAsync(Staff);
            return Staff;
        }
        public override async Task<MoneyExchangeTransferCurrencyDto?> LoadByNoAsync(string no)
        {
            var Staff = await CheckStaffInformationAsync();
            var StaffSeat = await CheckSeatInformationAsync(Staff);
            var entity = await base.LoadByNoAsync(no);
            if (entity != null)
                if (entity.ServicePointId != Staff.ServicePoint.Id) throw new UserFriendlyException(L["ThisOrderNotReleasedFromSameServicePoint"]);

            return entity;


        }

        [Authorize(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.ViewToExecute)]
        public async Task<PagedResultDto<MoneyExchangeTransferCurrencyDto>> GetTransfersReadyToExecuteForCashier()
        {
            var Staff = await CheckStaffInformationAsync();
            var StaffSeat = await CheckSeatInformationAsync(Staff);
            var query = (await _moneyExchangeTransferCurrencyRepository.GetQueryableAsync())
                .Where(x =>
                    x.ServicePointId == Staff.ServicePoint.Id &&
                    x.IsExecute == false &&
                    x.ExecutedByStaffId == Staff.Staff.Id &&
                    (
                        (x.TransferType == MoneyExchangeTransferType.CompanyToServicePoint && x.IsApproved) ||
                        (x.TransferType == MoneyExchangeTransferType.CashierToCashier) 
                    )
                )
                .ToList();

            return new PagedResultDto<MoneyExchangeTransferCurrencyDto>
            {
                Items = ObjectMapper.Map<List<MoneyExchangeTransferCurrency>, List<MoneyExchangeTransferCurrencyDto>>(query.ToList()),
                TotalCount = query.Count(),
            };
        }

    }
}
