﻿using Bogus.Extensions.Extras;
using EMPS.CompanyService.Currencies;
using EMPS.CompanyService.ServicePoints;
using EMPS.CompanyService.ServicePointSeats;
using EMPS.CompanyService.Staffs;
using EMPS.MoneyExchangeLedgerService.MoneyExchangeBalanceTrackings;
using EMPS.MoneyExchangeLedgerService.MoneyExchangeCasherLedgers;
using EMPS.MoneyExchangeLedgerService.MoneyExchangeLedger;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.Shared.Enum.ExchangeLedger;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Identity;

namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
{
    [Authorize(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Default)]

    public class CashierToCashierTransferService : MoneyExchangeTransferBase, ICashierToCashierTransferService
    {
        public CashierToCashierTransferService(IMoneyExchangeTransferCurrencyRepository moneyExchangeTransferCurrencyRepository, IStaffsAppService staffsAppService, IServicePointSeatsAppService servicePointSeatsAppService, IOrganizationUnitAppService organizationUnitAppService, IServicePointsAppService servicePointsAppService, ICurrenciesAppService currencyAppService, IIdentityUserAppService identityUserAppService, IMoneyExchangeCasherLedgersAppService iMoneyExchangeCasherLedgersApp, IMoneyExchangeBalanceTrackingsAppService moneyExchangeBalanceTrackingsAppService) : base(moneyExchangeTransferCurrencyRepository, staffsAppService, servicePointSeatsAppService, organizationUnitAppService, servicePointsAppService, currencyAppService, identityUserAppService, iMoneyExchangeCasherLedgersApp, moneyExchangeBalanceTrackingsAppService)
        {
        }

        protected override async Task<string?> GenerateNewNumberForMoneyExchangeTransfer()
        {
            return await _moneyExchangeTransferCurrencyRepository.GenerateNewNoForMoneyExchangeTransferCurrencyByTransferType(MoneyExchangeTransferType.CashierToCashier);
        }

        protected override async Task<MoneyExchangeTransferCurrency?> GetRequiredMoneyExchangeTransfer(string no)
        {

            var query = (await _moneyExchangeTransferCurrencyRepository.GetQueryableAsync())
                  .Where(x => x.No == no && x.TransferType == MoneyExchangeTransferType.CashierToCashier)
                  .OrderByDescending(x => x.CreationTime).FirstOrDefault();
            return query;
        }

        protected override async Task<MoneyExchangeTransferCurrency> SetBusinessLogicFromInheritanceClassesForSaveAsync(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency, MoneyExchangeTransferCurrencyCreateDto moneyExchange, CurrencyDto currency)
        {
            var Staff = await CheckStaffInformationAsync();
            var StaffSeat = await CheckSeatInformationAsync(Staff);
            if (!moneyExchange.ExecutedByStaffId.HasValue) throw new UserFriendlyException(L["TheReciverStaffMustBeSelected"]);
            var reciverStaff = await CheckStaffInformationAsync(moneyExchange.ExecutedByStaffId);
            var reciverStaffSeat = await CheckSeatInformationAsync(reciverStaff);
            if (reciverStaff.Staff.Id == Staff.Staff.Id) throw new UserFriendlyException(L["TheReciverStaffMustBeAnotherThanSenderStaff"]);
            if (reciverStaffSeat.ServicePointId != StaffSeat.ServicePointId) throw new UserFriendlyException(L["TwoCashirIsNotInSameServicePoint"]);
            CheckServicePointInformation(Staff.ServicePoint);
            moneyExchangeTransferCurrency!.FillServicePointInfo(Staff.ServicePoint.Name, Staff.ServicePoint.Id);
            if(currency.CurrencyType!=CurrencyType.Local)
                 moneyExchangeTransferCurrency.AverageCost = await GetAverageCostForCashier(moneyExchangeTransferCurrency.CurrencyId!.Value, Staff.ServicePoint.Id);
            moneyExchangeTransferCurrency.ExecutedByStaffId = reciverStaff.Staff.Id;
            moneyExchangeTransferCurrency.TransferType = MoneyExchangeTransferType.CashierToCashier;
            moneyExchangeTransferCurrency.FillTransactionIssueInfo(GetFullUserName(), GeTUserId(), $"{Staff.Staff.Name} {Staff.Staff.Surname}", Staff.Staff.Id, Staff.Seat.Id);
            return moneyExchangeTransferCurrency;


        }
        protected override Task<MoneyExchangeTransferCurrency> SetBusinessLogicFromInheritanceClassesForApproveAsync(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency)
        {
            throw new NotImplementedException();
        }

        protected override async Task<MoneyExchangeTransferCurrency> SetBusinessLogicFromInheritanceClassesForExecuteAsync(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency, CurrencyDto currency)
        {
            var ReciverStaff = await CheckStaffInformationAsync();
            var ReciverStaffSeat = await CheckSeatInformationAsync(ReciverStaff);
            if (ReciverStaff.Seat.StaffId != moneyExchangeTransferCurrency.ExecutedByStaffId) throw new UserFriendlyException(L["TheReciverStaffIsNotTheUserThatAddedInIssueToExecute"]);
            if (ReciverStaffSeat.ServicePointId != moneyExchangeTransferCurrency.ServicePointId) throw new UserFriendlyException(L["ThisOrderNotReleasedFromSameServicePoint"]);

            var SenderStaff = await CheckStaffInformationAsync(moneyExchangeTransferCurrency.TransactionIssueStaffId);
            var SenderStaffSeat = await CheckSeatInformationAsync(SenderStaff);
            if (SenderStaff.Staff.Id == ReciverStaff.Staff.Id) throw new UserFriendlyException(L["TheReciverStaffMustBeAnotherThanSenderStaff"]);
            if (SenderStaffSeat.ServicePointId != ReciverStaffSeat.ServicePointId) throw new UserFriendlyException(L["TwoCashirIsNotInSameServicePoint"]);
            await CheckBalanceForCashierCurrency(SenderStaff.Seat.Id, SenderStaffSeat.ServicePointId, moneyExchangeTransferCurrency.CurrencyId!.Value, moneyExchangeTransferCurrency.Amount);
            if (currency.CurrencyType == CurrencyType.Local)
            {
                var dto = new TransfareBalanceDTO
                {
                    CurrencyCode = moneyExchangeTransferCurrency.CurrencyCode!,
                    CurrencyID= moneyExchangeTransferCurrency.CurrencyId.Value,
                    operationAmount = moneyExchangeTransferCurrency.Amount,
                    SenderCasherID= SenderStaff.Seat.Id,
                    ReciverCasherID = ReciverStaff.Seat.Id,
                    servicePointID= SenderStaff.ServicePoint.Id,
                    servicePointName = SenderStaff.ServicePoint.Name,
                    TransactionSource= ExchangeLedgerTransactionSource.CasherToCasher
                };
                await _moneyExchangeBalanceTrackingsAppService.MoveBalanceBetweenTwoCasherAsync(dto);
            }
            else
            {

                var dto = new MoneyExchangeLedgerInsertDto
                {
                    OperationTime = DateTime.Now,
                    ServicePointID = SenderStaff.ServicePoint.Id,
                    ServicePointName = SenderStaff.ServicePoint.Name,
                    CasherID = SenderStaff.Seat.Id,
                    ReciverCasherID = ReciverStaff.Seat.Id,
                    TransactionSource = ExchangeLedgerTransactionSource.CasherToCasher,
                    TransactionNo = moneyExchangeTransferCurrency.No!,
                    BaseCurrencyID = moneyExchangeTransferCurrency.CurrencyId.Value,
                    BaseCurrencyCode = moneyExchangeTransferCurrency.CurrencyCode!,
                    MoneyExchangeCurrencyLedgerInsertDtos = new List<MoneyExchangeCurrencyLedgerInsertDto> { new MoneyExchangeCurrencyLedgerInsertDto { Amount = moneyExchangeTransferCurrency.Amount, CurrencyID = moneyExchangeTransferCurrency.CurrencyId.Value, CurrencyCode = moneyExchangeTransferCurrency.CurrencyCode!, Rate = moneyExchangeTransferCurrency.AverageCost } }
                };

                await InsertToLegerAndAssertOnBalance(dto);
            }
            moneyExchangeTransferCurrency.FillExecutionInfo(GetFullUserName(), GeTUserId(), $"{ReciverStaff.Staff.Name} {ReciverStaff.Staff.Surname}", ReciverStaff.Staff.Id, ReciverStaff.Seat.Id);
            return moneyExchangeTransferCurrency;
        }

        protected override async Task<MoneyExchangeTransferCurrency?> CheckRequiredBusinessLogicInUpdatingMode(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency)
        {
            var Staff = await CheckStaffInformationAsync();
            var StaffSeat = await CheckSeatInformationAsync(Staff);
            if (moneyExchangeTransferCurrency.TransactionIssueSeatId != StaffSeat.Id) throw new UserFriendlyException(L["ThisCashierIsNotTheOneWhoCreateTheOrder"]);

            return moneyExchangeTransferCurrency;
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Save)]
        public override Task<MoneyExchangeTransferCurrencyDto> SaveAsync(MoneyExchangeTransferCurrencyCreateDto input)
        {
            return base.SaveAsync(input);
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Execute)]
        public override Task<MoneyExchangeTransferCurrencyDto> ExecuteByNoAsync(string no)
        {
            return base.ExecuteByNoAsync(no);
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Delete)]
        public override Task DeletedByNoAsync(string no)
        {
            return base.DeletedByNoAsync(no);
        }

        public async Task<StaffWithNavigationPropertiesDto> GetStaffWithNavigationPropertiesAsync()
        {
            var Staff = await CheckStaffInformationAsync();
            var StaffSeat = await CheckSeatInformationAsync(Staff);
            return Staff;
        }
        public override async Task<MoneyExchangeTransferCurrencyDto?> LoadByNoAsync(string no)
        {
            var Staff = await CheckStaffInformationAsync();
            var StaffSeat = await CheckSeatInformationAsync(Staff);
            var entity = await base.LoadByNoAsync(no);
            if (entity != null)
                if (entity.ServicePointId != Staff.ServicePoint.Id) throw new UserFriendlyException(L["ThisOrderNotReleasedFromSameServicePoint"]);

            return entity;


        }

        [Authorize(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.ViewToExecute)]
        public async Task<PagedResultDto<MoneyExchangeTransferCurrencyDto>> GetTransfersReadyToExecuteForCashier()
        {
            var Staff = await CheckStaffInformationAsync();
            var StaffSeat = await CheckSeatInformationAsync(Staff);
            var query = (await _moneyExchangeTransferCurrencyRepository.GetQueryableAsync())
                .Where(x =>
                    x.ServicePointId == Staff.ServicePoint.Id &&
                    x.IsExecute == false &&
                    x.ExecutedByStaffId == Staff.Staff.Id &&
                    (
                        (x.TransferType == MoneyExchangeTransferType.CompanyToServicePoint && x.IsApproved) ||
                        (x.TransferType == MoneyExchangeTransferType.CashierToCashier)
                    )
                )
                .ToList();

            return new PagedResultDto<MoneyExchangeTransferCurrencyDto>
            {
                Items = ObjectMapper.Map<List<MoneyExchangeTransferCurrency>, List<MoneyExchangeTransferCurrencyDto>>(query.ToList()),
                TotalCount = query.Count(),
            };
        }

        protected override MoneyExchangeTransferType GetTransferType()
        {
            return MoneyExchangeTransferType.CashierToCashier;
        }
    }
}
