@page "/MoneyExchangeTransfersService/CompanyToServicePointTransferApprove"



@attribute [Authorize(MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Approve)]
@using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
@using EMPS.MoneyExchangeTransfersService.Localization
@using EMPS.MoneyExchangeTransfersService.Shared
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Components.Web
@using Blazorise
@using Blazorise.Components
@using Blazorise.DataGrid
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using EMPS.MoneyExchangeTransfersService.Permissions



@inherits MoneyExchangeTransfersServiceComponentBase
@inject IUiMessageService UiMessageService


@using EMPS.MoneyExchangeTransfersService

<PageHeader Title="@L["CompanyToServicePointTransferApprove"]" BreadcrumbItems="BreadcrumbItems" Toolbar="Toolbar" />

<DataGrid TItem="string" Data="stringList" ReadData="OnDataGridReadAsync" hidden />



<Div @onkeydown="HandleKeyDown">
    <EMPS.MoneyExchangeTransfersService.Blazor.Components.CompanyToServicePointForm @ref="CompanyToServicePointFormRef"
                                                                                    OnCompanyToServicePointLoaded="HandleTransfareLoad" IsReadOnlyMode="IsReadOnly" />
</Div>


