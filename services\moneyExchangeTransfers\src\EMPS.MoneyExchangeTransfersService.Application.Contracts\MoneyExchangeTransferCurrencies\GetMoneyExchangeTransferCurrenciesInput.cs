using Volo.Abp.Application.Dtos;
using System;

namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
{
    public class GetMoneyExchangeTransferCurrenciesInput : PagedAndSortedResultRequestDto
    {
        public string? FilterText { get; set; }

        public string? No { get; set; }
        public double? AmountMin { get; set; }
        public double? AmountMax { get; set; }
        public string? CurrencyCode { get; set; }
        public Guid? CurrencyId { get; set; }
        public string? ServicePointName { get; set; }
        public Guid? ServicePointId { get; set; }
        public string? TransactionIssueUserName { get; set; }
        public Guid? TransactionIssueUserId { get; set; }
        public string? TransactionIssueStaffName { get; set; }
        public Guid? TransactionIssueStaffId { get; set; }
        public Guid? TransactionIssueSeatId { get; set; }
        public bool? IsApproved { get; set; }
        public string? ApprovedByUserName { get; set; }
        public Guid? ApprovedByUserId { get; set; }
        public DateTime? ApprovalDateMin { get; set; }
        public DateTime? ApprovalDateMax { get; set; }
        public DateTime? ApprovalDateTimeMin { get; set; }
        public DateTime? ApprovalDateTimeMax { get; set; }
        public string? Notes { get; set; }
        public bool? IsExecute { get; set; }
        public string? ExecutedByUserName { get; set; }
        public Guid? ExecutedByUserId { get; set; }
        public string? ExecutedByStaffName { get; set; }
        public Guid? ExecutedByStaffId { get; set; }
        public Guid? ExecutedBySeatId { get; set; }
        public DateTime? ExecuteDateMin { get; set; }
        public DateTime? ExecuteDateMax { get; set; }
        public DateTime? ExecuteDateTimeMin { get; set; }
        public DateTime? ExecuteDateTimeMax { get; set; }
        public MoneyExchangeTransferStatus? TransferStatus { get; set; }
        public MoneyExchangeTransferType? TransferType { get; set; }

        public GetMoneyExchangeTransferCurrenciesInput()
        {

        }
    }
}