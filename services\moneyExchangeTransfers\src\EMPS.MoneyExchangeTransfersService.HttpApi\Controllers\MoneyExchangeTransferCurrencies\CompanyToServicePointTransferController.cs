using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using System.Collections.Generic;
using EMPS.CompanyService.Currencies;
using EMPS.CompanyService.Staffs;
using EMPS.CompanyService.ServicePoints;

namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
{
    [RemoteService(Name = "MoneyExchangeTransfersService")]
    [Area("moneyExchangeTransfersService")]
    [ControllerName("CompanyToServicePointTransferController")]
    [Route("api/money-exchange-transfers-service/CompanyToServicePointTransferService")]
    public class CompanyToServicePointTransferController : AbpController, ICompanyToServicePointTransferService
    {
        public ICompanyToServicePointTransferService _companyToServicePointTransferService;

        public CompanyToServicePointTransferController(ICompanyToServicePointTransferService companyToServicePointTransferService)
        {
            _companyToServicePointTransferService = companyToServicePointTransferService;
        }
        [HttpPut]
        [Route("ApproveByNoAsync")]
        public Task<MoneyExchangeTransferCurrencyDto> ApproveByNoAsync(string no)
        {
            return _companyToServicePointTransferService.ApproveByNoAsync(no);
        }

        [HttpDelete]
        [Route("DeletedByNoAsync")]
        public Task DeletedByNoAsync(string no)
        {
            return _companyToServicePointTransferService.DeletedByNoAsync(no);
        }
        [HttpPut]
        [Route("ExecuteByNoAsync")]
        public Task<MoneyExchangeTransferCurrencyDto> ExecuteByNoAsync(string no)
        {
            return _companyToServicePointTransferService.ExecuteByNoAsync(no);
        }
        [HttpGet]
        [Route("GetAllActiveCurrenciesAsync")]
        public Task<List<CurrencyDto>> GetAllActiveCurrenciesAsync()
        {
            return _companyToServicePointTransferService.GetAllActiveCurrenciesAsync();
        }
        [HttpGet]
        [Route("GetAllActiveServicePointAsync")]
        public Task<List<ServicePointDto>> GetAllActiveServicePointAsync()
        {
            return _companyToServicePointTransferService.GetAllActiveServicePointAsync();
        }
        [HttpGet]
        [Route("GetAllCashierInServicePointAsync")]
        public Task<List<StaffDto>> GetAllCashierInServicePointAsync(Guid ServicePointId)
        {
            return _companyToServicePointTransferService.GetAllCashierInServicePointAsync(ServicePointId);
        }
        [HttpGet]
        [Route("LoadByNoAsync")]
        public Task<MoneyExchangeTransferCurrencyDto?> LoadByNoAsync(string no)
        {
            return _companyToServicePointTransferService.LoadByNoAsync(no);
        }
        [HttpPost]
        [Route("SaveAsync")]
        public Task<MoneyExchangeTransferCurrencyDto> SaveAsync(MoneyExchangeTransferCurrencyCreateDto input)
        {
            return _companyToServicePointTransferService.SaveAsync(input);
        }
    }
}