﻿using AutoMapper.Internal.Mappers;
using EMPS.CompanyService.Currencies;
using EMPS.CompanyService.ServicePoints;
using EMPS.CompanyService.ServicePointSeats;
using EMPS.CompanyService.Staffs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;


namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
{
    public abstract class MoneyExchangeTransferBase : MoneyExchangeTransfersServiceAppService
    {
        protected readonly IMoneyExchangeTransferCurrencyRepository _moneyExchangeTransferCurrencyRepository;
        protected readonly IStaffsAppService _staffsAppService;
        protected readonly IServicePointSeatsAppService _servicePointSeatsAppService;
        protected readonly IOrganizationUnitAppService _organizationUnitAppService;
        protected readonly IServicePointsAppService _servicePointsAppService;
        protected readonly ICurrenciesAppService _currencyAppService; 
        protected readonly IIdentityUserAppService _IdentityUserAppService;

        protected MoneyExchangeTransferBase(IMoneyExchangeTransferCurrencyRepository moneyExchangeTransferCurrencyRepository, IStaffsAppService staffsAppService, IServicePointSeatsAppService servicePointSeatsAppService, IOrganizationUnitAppService organizationUnitAppService, IServicePointsAppService servicePointsAppService, ICurrenciesAppService currencyAppService, IIdentityUserAppService identityUserAppService)
        {
            _moneyExchangeTransferCurrencyRepository = moneyExchangeTransferCurrencyRepository;
            _staffsAppService = staffsAppService;
            _servicePointSeatsAppService = servicePointSeatsAppService;
            _organizationUnitAppService = organizationUnitAppService;
            _servicePointsAppService = servicePointsAppService;
            _currencyAppService = currencyAppService;
            _IdentityUserAppService = identityUserAppService;
        }

        public virtual async Task<MoneyExchangeTransferCurrencyDto> SaveAsync(MoneyExchangeTransferCurrencyCreateDto input)
        {
            bool IsUpdating = await DetectUpdateModeByNoAsync(input.No);

            CheckRequiredInputs(input);

            var moneyExchangeTransferCurrency = new MoneyExchangeTransferCurrency();

            moneyExchangeTransferCurrency.Amount = input.Amount;

            var currency = await GetCurrencyAsync(input.CurrencyId!.Value);
            var servicePoint = await GetServicePointAsync(input.ServicePointId!.Value);
            CheckServicePointInformation(servicePoint);
            if (IsUpdating)
            {
                moneyExchangeTransferCurrency = await GetRequiredMoneyExchangeTransfer(input.No!);
                CheckIfNotInStatusPending(moneyExchangeTransferCurrency!);
                moneyExchangeTransferCurrency = await CheckRequiredBusinessLogicInUpdatingMode(moneyExchangeTransferCurrency!);

            }
            else
            {
                moneyExchangeTransferCurrency.No = await GenerateNewNumberForMoneyExchangeTransfer();
                if (moneyExchangeTransferCurrency.No == "-1") throw new UserFriendlyException(L["Error:SomeDataInDatabaseEncrypted"]);

            }
            moneyExchangeTransferCurrency!.FillServicePointInfo(servicePoint.Name, servicePoint.Id);
            moneyExchangeTransferCurrency.FillCurrencyInfo(currency.Code, currency.Id);
            moneyExchangeTransferCurrency.FillCurrencyInfo(currency.Code, currency.Id);
            moneyExchangeTransferCurrency.TransferStatus = MoneyExchangeTransferStatus.Pending;
            moneyExchangeTransferCurrency = await SetBusinessLogicFromInheritanceClassesForSaveAsync(moneyExchangeTransferCurrency, input);

            moneyExchangeTransferCurrency = await SaveOrUpdateMoneyExchangeTransferCurrency(IsUpdating, moneyExchangeTransferCurrency);
            return ObjectMapper.Map<MoneyExchangeTransferCurrency, MoneyExchangeTransferCurrencyDto>(moneyExchangeTransferCurrency);

        }

        protected abstract Task<MoneyExchangeTransferCurrency?> CheckRequiredBusinessLogicInUpdatingMode(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency);

        private async Task<MoneyExchangeTransferCurrency> SaveOrUpdateMoneyExchangeTransferCurrency(bool IsUpdating, MoneyExchangeTransferCurrency moneyExchangeTransferCurrency)
        {
            return IsUpdating ? await _moneyExchangeTransferCurrencyRepository.UpdateAsync(moneyExchangeTransferCurrency) :
                await _moneyExchangeTransferCurrencyRepository.InsertAsync(moneyExchangeTransferCurrency);
        }
        protected abstract Task<string?> GenerateNewNumberForMoneyExchangeTransfer();

        private async Task<bool> DetectUpdateModeByNoAsync(string? no)
        {
            return await _moneyExchangeTransferCurrencyRepository.AnyAsync(x => x.No == no);
        }

        public virtual async Task<MoneyExchangeTransferCurrencyDto> ApproveByNoAsync(string no)
        {

            var moneyExchangeTransferCurrency = await GetRequiredMoneyExchangeTransfer(no) ?? throw new UserFriendlyException(L["Error:ThisNoIsNotExist"]);

            CheckIfApproved(moneyExchangeTransferCurrency!);
            CheckIfExecute(moneyExchangeTransferCurrency!);
            var currency = await GetCurrencyAsync(moneyExchangeTransferCurrency.CurrencyId!.Value);
            var servicePoint = await GetServicePointAsync(moneyExchangeTransferCurrency.ServicePointId!.Value);
            moneyExchangeTransferCurrency = await SetBusinessLogicFromInheritanceClassesForApproveAsync(moneyExchangeTransferCurrency);
            moneyExchangeTransferCurrency.FillApprovalInfo(GetFullUserName(), GeTUserId());
            moneyExchangeTransferCurrency.TransferStatus = MoneyExchangeTransferStatus.Approved;
            moneyExchangeTransferCurrency = await SaveOrUpdateMoneyExchangeTransferCurrency(true, moneyExchangeTransferCurrency);
            return ObjectMapper.Map<MoneyExchangeTransferCurrency, MoneyExchangeTransferCurrencyDto>(moneyExchangeTransferCurrency);


        }
        public virtual async Task<MoneyExchangeTransferCurrencyDto> ExecuteByNoAsync(string no)
        {

            var moneyExchangeTransferCurrency = await GetRequiredMoneyExchangeTransfer(no) ?? throw new UserFriendlyException(L["Error:ThisNoIsNotExist"]);

            CheckIfExecute(moneyExchangeTransferCurrency!);
            var currency = await GetCurrencyAsync(moneyExchangeTransferCurrency.CurrencyId!.Value);
            var servicePoint = await GetServicePointAsync(moneyExchangeTransferCurrency.ServicePointId!.Value);
            moneyExchangeTransferCurrency = await SetBusinessLogicFromInheritanceClassesForExecuteAsync(moneyExchangeTransferCurrency);
            moneyExchangeTransferCurrency.TransferStatus = MoneyExchangeTransferStatus.Executed;
            moneyExchangeTransferCurrency = await SaveOrUpdateMoneyExchangeTransferCurrency(true, moneyExchangeTransferCurrency);
            return ObjectMapper.Map<MoneyExchangeTransferCurrency, MoneyExchangeTransferCurrencyDto>(moneyExchangeTransferCurrency);


        }
        public virtual async Task DeletedByNoAsync(string no)
        {

            var moneyExchangeTransferCurrency = await GetRequiredMoneyExchangeTransfer(no) ?? throw new UserFriendlyException(L["Error:ThisNoIsNotExist"]);

            CheckIfNotInStatusPending(moneyExchangeTransferCurrency!);
            var currency = await GetCurrencyAsync(moneyExchangeTransferCurrency.CurrencyId!.Value);
            var servicePoint = await GetServicePointAsync(moneyExchangeTransferCurrency.ServicePointId!.Value);
            moneyExchangeTransferCurrency.TransferStatus = MoneyExchangeTransferStatus.Canceled;
            await _moneyExchangeTransferCurrencyRepository.DeleteAsync(moneyExchangeTransferCurrency);


        }

        private void CheckIfNotInStatusPending(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency)
        {
            if (moneyExchangeTransferCurrency.TransferStatus != MoneyExchangeTransferStatus.Pending)
                throw new UserFriendlyException(L["Error:CannotMakeAnyOpration"]);
        }

        protected abstract Task<MoneyExchangeTransferCurrency> SetBusinessLogicFromInheritanceClassesForExecuteAsync(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency);
        protected abstract Task<MoneyExchangeTransferCurrency> SetBusinessLogicFromInheritanceClassesForApproveAsync(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency);

        private void CheckIfExecute(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency)
        {
            if (moneyExchangeTransferCurrency.IsExecute) throw new UserFriendlyException(L["Error:MoneyExchangeTransferIsExecuteAlready"]);
        }

        private void CheckIfApproved(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency)
        {
            if (moneyExchangeTransferCurrency.IsApproved) throw new UserFriendlyException(L["Error:MoneyExchangeTransferIsApprovedAlready"]);
        }

        public virtual async Task<MoneyExchangeTransferCurrencyDto?> LoadByNoAsync(string no)
        {
            var transaction = await GetRequiredMoneyExchangeTransfer(no) ;
            if (transaction == null) return null;
            var transactionDto = ObjectMapper.Map<MoneyExchangeTransferCurrency, MoneyExchangeTransferCurrencyDto>(transaction);
            return transactionDto;
        }

        protected abstract Task<MoneyExchangeTransferCurrency?> GetRequiredMoneyExchangeTransfer(string no);
        protected abstract Task<MoneyExchangeTransferCurrency> SetBusinessLogicFromInheritanceClassesForSaveAsync(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency, MoneyExchangeTransferCurrencyCreateDto moneyExchange);

        protected void CheckRequiredInputs(MoneyExchangeTransferCurrencyCreateDto input)
        {

            if (input.Amount <= 0)
                throw new UserFriendlyException(L["TheAmountMustBeGreaterThanZero"]);
            if (input.CurrencyId == null || input.CurrencyId == Guid.Empty)
                throw new UserFriendlyException(L["CurrencyMustBeSelected"]);
            if (input.ServicePointId == null || input.ServicePointId == Guid.Empty)
                throw new UserFriendlyException(L["ServicePointMustBeSelected"]);
        }

        protected string GetFullUserName()
        {
            return $"{CurrentUser.Name} {CurrentUser.SurName}";
        }
        protected Guid? GeTUserId()
        {
            return CurrentUser.Id;
        }
        /// <summary>
        /// this method can be return null  for the management operation 
        /// </summary>
        /// <returns></returns>
        /// <exception cref="UserFriendlyException"></exception>
        protected async Task<StaffDto?> CheckStaffInformation()
        {
            var res = await _staffsAppService.GetListAsync(new GetStaffsInput { UserID = CurrentUser.Id });

            var Staff = res.Items.FirstOrDefault();
            if (Staff == null)
            {
                return null;
            }
            else
            {
                if (Staff != null && !Staff.Staff.IsActive)
                    throw new UserFriendlyException(L["Error:StaffIsNotActive"]);
                return Staff!.Staff;
            }
        }
        /// <summary>
        /// this method can be return null  for the management operation 
        /// </summary>
        /// <param name="staff"></param>
        /// <returns></returns>
        /// <exception cref="UserFriendlyException"></exception>
        protected async Task<ServicePointSeatWithNavigationPropertiesDto> CheckSeatInformation(StaffDto staff)
        {

            var SeatList = (await _servicePointSeatsAppService.GetListAsync(new GetServicePointSeatsInput { StaffId = staff.Id })).Items;


            if (SeatList.Count == 0) throw new UserFriendlyException(L["Error:StaffNotExistInSeat"]);

            if (SeatList.Count > 1)
                throw new UserFriendlyException(L["Error:MultipleSates"]);

            return SeatList.First();

        }
        protected ServicePointDto CheckServicePointInformation(ServicePointDto? servicePoint)
        {
            if (servicePoint == null)
                throw new UserFriendlyException(L["ServicePointIsNotExist"]);
            if (!servicePoint.IsActive)
                throw new UserFriendlyException(L["thisServicePointNotActive"]);

            return servicePoint;
        }
        public async Task<List<StaffDto>> GetAllCashierInServicePointAsync(Guid ServicePointId)
        {
            var servicePoint = await GetServicePointAsync(ServicePointId);
            servicePoint = CheckServicePointInformation(servicePoint);
            return await _servicePointSeatsAppService.GetAllCashierByServicePointAsync(new GetServicePointSeatsInput() { ServicePointId = ServicePointId });

        }
        public async Task<ServicePointDto> GetServicePointAsync(Guid ServicePointId)
        {
            return await _servicePointsAppService.GetAsync(ServicePointId);
        }
        public async Task<List<ServicePointDto>> GetAllActiveServicePointAsync()
        {
            return await _servicePointsAppService.GetAllActiveServicePointsLookupAsync();

        }
        public async Task<List<CurrencyDto>> GetAllActiveCurrenciesAsync()
        {
            return await _currencyAppService.GetActiveAsync();
        }
        protected async Task<CurrencyDto> GetCurrencyAsync(Guid CurrencyID)
        {
            try
            {
                var Currency = await _currencyAppService.GetAsync(CurrencyID);

                if (!Currency.Status)
                    throw new UserFriendlyException(L["ThisCurrencyIsNotActive"]);
                return Currency;

            }
            catch (Exception ex)
            {
                if (ex is EntityNotFoundException)
                    throw new UserFriendlyException(L["CurrencyNotFound"]);
                throw;
            }

        }
        /// <summary>
        /// this method to check if the user is active or not,
        /// throw UserFriendlyException if the user is not active
        /// </summary>
        /// <returns></returns>
        /// <exception cref="UserFriendlyException"></exception>
        protected async Task CheckIfTheUserIsActive()
        {
            // Asynchronously fetch the user information using the _identityUserAppService
            // by passing the current user's ID, which is cast to a Guid.
            if (CurrentUser.Id == null) throw new UserFriendlyException(L["thisIsNotUser"]);
            var user = await _IdentityUserAppService.GetAsync((Guid)CurrentUser.Id);

            // Check if the user's IsActive property is false.
            if (user.IsActive == false)
            {
                // If the user is not active, throw an exception with the message "thisUserIsNotActive".
                throw new UserFriendlyException(L["thisUserIsNotActive"]);
            }
        }
        /// <summary>
        /// this method cannot be return null  it use only for staff operations 
        /// </summary>
        /// <param name="staffId"></param>
        /// <returns></returns>
        /// <exception cref="UserFriendlyException"></exception>
        protected async Task<StaffWithNavigationPropertiesDto> CheckStaffInformationAsync(Guid? staffId = null)
        {
            // Validate input if staffId is provided
            if (staffId.HasValue && staffId == Guid.Empty)
            {
                throw new UserFriendlyException(L["Staff ID cannot be empty"]);
            }

            StaffWithNavigationPropertiesDto staff;

            try
            {
                if (staffId.HasValue)
                {
                    staff = await _staffsAppService.GetWithNavigationPropertiesAsync(staffId.Value);
                }
                else
                {
                    var staffList = await _staffsAppService.GetListAsync(new GetStaffsInput
                    {
                        UserID = CurrentUser.Id
                    });

                    staff = staffList.Items.FirstOrDefault();
                }

                // Check if staff exists
                if (staff?.Staff == null)
                {
                    throw new UserFriendlyException(L["thisUserNotStaff"]);
                }

                // Check if staff is active
                if (!staff.Staff.IsActive)
                {
                    throw new UserFriendlyException(L["thisStaffNotActive"]);
                }

                return staff;
            }
            catch (Exception ex) when (ex is not UserFriendlyException)
            {
                // Handle unexpected exceptions and wrap them if needed
                throw new UserFriendlyException(L["staffInformationCheckFailed"]);
            }
        }
        /// <summary>
        /// this method cannot be return null  it use only for staff operations 
        /// </summary>
        /// <param name="staff"></param>
        /// <returns></returns>
        /// <exception cref="UserFriendlyException"></exception>
        protected async Task<ServicePointSeatDto> CheckSeatInformationAsync(StaffWithNavigationPropertiesDto staff)
        {
            // get firse seat you find it to this staff 
            var staffSeats = (await _servicePointSeatsAppService.GetListAsync(new GetServicePointSeatsInput { StaffId = staff.Staff.Id })).Items;
            if (staffSeats.Count() == 0)
                throw new UserFriendlyException(L["thisStaffNotExistInSeat"]);
            if (staffSeats.Count() > 1)
                throw new UserFriendlyException(L["MultipleSeates"]);
            var staffSeat = staffSeats.FirstOrDefault().ServicePointSeat;
            if (staffSeat == null)
                throw new UserFriendlyException(L["thisStaffNotExistInSeat"]);

            var OuSeat = await _organizationUnitAppService.GetAsync(staffSeat.OuId);
            if (OuSeat == null || !OuSeat.IsCash)
                throw new UserFriendlyException(L["ThisSeatNoCashier"]);

            return staffSeat;
        }

     

    }
}
