using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
{
    public interface IMoneyExchangeTransferCurrenciesAppService : IApplicationService
    {
        Task<PagedResultDto<MoneyExchangeTransferCurrencyDto>> GetListAsync(GetMoneyExchangeTransferCurrenciesInput input);

        Task<MoneyExchangeTransferCurrencyDto> GetAsync(Guid id);

        Task DeleteAsync(Guid id);

        Task<MoneyExchangeTransferCurrencyDto> CreateAsync(MoneyExchangeTransferCurrencyCreateDto input);

        Task<MoneyExchangeTransferCurrencyDto> UpdateAsync(Guid id, MoneyExchangeTransferCurrencyUpdateDto input);

    }
}