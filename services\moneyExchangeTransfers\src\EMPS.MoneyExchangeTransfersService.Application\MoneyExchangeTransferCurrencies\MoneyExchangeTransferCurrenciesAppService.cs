using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq.Dynamic.Core;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;

namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
{

    [Authorize(MoneyExchangeTransfersServicePermissions.MoneyExchangeTransferCurrencies.Default)]
    public class MoneyExchangeTransferCurrenciesAppService : ApplicationService, IMoneyExchangeTransferCurrenciesAppService
    {

        private readonly IMoneyExchangeTransferCurrencyRepository _moneyExchangeTransferCurrencyRepository;
        private readonly MoneyExchangeTransferCurrencyManager _moneyExchangeTransferCurrencyManager;

        public MoneyExchangeTransferCurrenciesAppService(IMoneyExchangeTransferCurrencyRepository moneyExchangeTransferCurrencyRepository, MoneyExchangeTransferCurrencyManager moneyExchangeTransferCurrencyManager)
        {

            _moneyExchangeTransferCurrencyRepository = moneyExchangeTransferCurrencyRepository;
            _moneyExchangeTransferCurrencyManager = moneyExchangeTransferCurrencyManager;
        }

        public virtual async Task<PagedResultDto<MoneyExchangeTransferCurrencyDto>> GetListAsync(GetMoneyExchangeTransferCurrenciesInput input)
        {
            var totalCount = await _moneyExchangeTransferCurrencyRepository.GetCountAsync(input.FilterText, input.No, input.AmountMin, input.AmountMax, input.CurrencyCode, input.CurrencyId, input.ServicePointName, input.ServicePointId, input.TransactionIssueUserName, input.TransactionIssueUserId, input.TransactionIssueStaffName, input.TransactionIssueStaffId, input.TransactionIssueSeatId, input.IsApproved, input.ApprovedByUserName, input.ApprovedByUserId, input.ApprovalDateMin, input.ApprovalDateMax, input.ApprovalDateTimeMin, input.ApprovalDateTimeMax, input.Notes, input.IsExecute, input.ExecutedByUserName, input.ExecutedByUserId, input.ExecutedByStaffName, input.ExecutedByStaffId, input.ExecutedBySeatId, input.ExecuteDateMin, input.ExecuteDateMax, input.ExecuteDateTimeMin, input.ExecuteDateTimeMax, input.TransferStatus, input.TransferType);
            var items = await _moneyExchangeTransferCurrencyRepository.GetListAsync(input.FilterText, input.No, input.AmountMin, input.AmountMax, input.CurrencyCode, input.CurrencyId, input.ServicePointName, input.ServicePointId, input.TransactionIssueUserName, input.TransactionIssueUserId, input.TransactionIssueStaffName, input.TransactionIssueStaffId, input.TransactionIssueSeatId, input.IsApproved, input.ApprovedByUserName, input.ApprovedByUserId, input.ApprovalDateMin, input.ApprovalDateMax, input.ApprovalDateTimeMin, input.ApprovalDateTimeMax, input.Notes, input.IsExecute, input.ExecutedByUserName, input.ExecutedByUserId, input.ExecutedByStaffName, input.ExecutedByStaffId, input.ExecutedBySeatId, input.ExecuteDateMin, input.ExecuteDateMax, input.ExecuteDateTimeMin, input.ExecuteDateTimeMax, input.TransferStatus, input.TransferType, input.Sorting, input.MaxResultCount, input.SkipCount);

            return new PagedResultDto<MoneyExchangeTransferCurrencyDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<MoneyExchangeTransferCurrency>, List<MoneyExchangeTransferCurrencyDto>>(items)
            };
        }

        public virtual async Task<MoneyExchangeTransferCurrencyDto> GetAsync(Guid id)
        {
            return ObjectMapper.Map<MoneyExchangeTransferCurrency, MoneyExchangeTransferCurrencyDto>(await _moneyExchangeTransferCurrencyRepository.GetAsync(id));
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.MoneyExchangeTransferCurrencies.Delete)]
        public virtual async Task DeleteAsync(Guid id)
        {
            await _moneyExchangeTransferCurrencyRepository.DeleteAsync(id);
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.MoneyExchangeTransferCurrencies.Create)]
        public virtual async Task<MoneyExchangeTransferCurrencyDto> CreateAsync(MoneyExchangeTransferCurrencyCreateDto input)
        {

            var moneyExchangeTransferCurrency = await _moneyExchangeTransferCurrencyManager.CreateAsync(
            input.No, input.Amount, input.CurrencyCode, input.ServicePointName, input.TransactionIssueUserName, input.TransactionIssueStaffName, input.IsApproved, input.ApprovedByUserName, input.Notes, input.IsExecute, input.ExecutedByUserName, input.ExecutedByStaffName, input.TransferStatus, input.TransferType, input.CurrencyId, input.ServicePointId, input.TransactionIssueUserId, input.TransactionIssueStaffId, input.TransactionIssueSeatId, input.ApprovedByUserId, input.ApprovalDate, input.ApprovalDateTime, input.ExecutedByUserId, input.ExecutedByStaffId, input.ExecutedBySeatId, input.ExecuteDate, input.ExecuteDateTime
            );

            return ObjectMapper.Map<MoneyExchangeTransferCurrency, MoneyExchangeTransferCurrencyDto>(moneyExchangeTransferCurrency);
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.MoneyExchangeTransferCurrencies.Edit)]
        public virtual async Task<MoneyExchangeTransferCurrencyDto> UpdateAsync(Guid id, MoneyExchangeTransferCurrencyUpdateDto input)
        {

            var moneyExchangeTransferCurrency = await _moneyExchangeTransferCurrencyManager.UpdateAsync(
            id,
            input.No, input.Amount, input.CurrencyCode, input.ServicePointName, input.TransactionIssueUserName, input.TransactionIssueStaffName, input.IsApproved, input.ApprovedByUserName, input.Notes, input.IsExecute, input.ExecutedByUserName, input.ExecutedByStaffName, input.TransferStatus, input.TransferType, input.CurrencyId, input.ServicePointId, input.TransactionIssueUserId, input.TransactionIssueStaffId, input.TransactionIssueSeatId, input.ApprovedByUserId, input.ApprovalDate, input.ApprovalDateTime, input.ExecutedByUserId, input.ExecutedByStaffId, input.ExecutedBySeatId, input.ExecuteDate, input.ExecuteDateTime, input.ConcurrencyStamp
            );

            return ObjectMapper.Map<MoneyExchangeTransferCurrency, MoneyExchangeTransferCurrencyDto>(moneyExchangeTransferCurrency);
        }
    }
}