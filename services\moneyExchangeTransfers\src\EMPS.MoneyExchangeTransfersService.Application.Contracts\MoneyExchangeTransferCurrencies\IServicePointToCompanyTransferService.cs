﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
{
    public interface IServicePointToCompanyTransferService : IMoneyExchangeTransferBase
    {
        Task<MoneyExchangeTransferCurrencyDto> ApproveByNoAsync(string no);
        Task<PagedResultDto<MoneyExchangeTransferCurrencyDto>> GetReadyTransfersToExecuteForCompany();

    }
}
