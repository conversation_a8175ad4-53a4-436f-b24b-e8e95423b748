@using Blazorise
@using Blazorise.Components
@using EMPS.MoneyExchangeTransfersService.Blazor.Pages.MoneyExchangeTransfersService
@using EMPS.MoneyExchangeTransfersService.CashVouchers
@using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
@using EMPS.Shared.Enum
@using Microsoft.Extensions.Localization
@using EMPS.FeeService.Localization
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using Microsoft.AspNetCore.Components
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Http.Client
@using Blazorise.LoadingIndicator

@inherits MoneyExchangeTransfersServiceComponentBase
@inject NavigationManager NavigationManager
@inject IRemoteServiceConfigurationProvider RemoteServiceConfigurationProvider

<style>
    .inline-input-label {
        display: flex;
        justify-content: space-between;
        margin-bottom: 11px;
        align-items: center;
    }

    .button-group {
        display: flex;
        position: relative;
        vertical-align: middle;
        justify-content: space-between;
    }

    .custom-outline-btn {
        border-color: #f72585 !important;
        /* Outline color */
        color: #f72585 !important;
        /* Text color */
    }

        .custom-outline-btn:hover {
            background-color: #f72585 !important;
            /* Hover background */
            color: white !important;
            /* Hover text color */
            border-color: #f72585 !important;
            /* Hover outline color */
        }

        .custom-outline-btn:active,
        .custom-outline-btn:focus {
            box-shadow: 0 0 0 0.2rem rgba(247, 37, 133, 0.25) !important;
            /* Focus glow */
        }
</style>

@code {
    string FormWidthClass = "col-9";
}

<LoadingIndicator @ref="loadingIndicator">

    <Form Model="@FormReceiptVoucher">
        <Validations @ref=FormReceiptVoucherValidations Model="@FormReceiptVoucher" Mode="ValidationMode.Auto">
            <Card>
                <CardBody>

                    <Row>
                        <Column ColumnSize="ColumnSize.Is4" class="px-4 ">

                            <Div class="inline-input-label">
                                <FieldLabel>@L["No"]</FieldLabel>
                                <Div class="@FormWidthClass">
                                    <TextEdit @bind-Text="@FormReceiptVoucher.No" Immediate="true"
                                              @onkeydown="@LoadModelByReceiptVoucherNo" Autofocus="true" TabIndex='1' />

                                </Div>

                            </Div>

                            <Div class="inline-input-label">
                                <FieldLabel>@L["IssueDateTime"]</FieldLabel>
                                <Div class="@FormWidthClass">

                                    <DateEdit Style="padding-block: 7px;" TValue="DateTime?"
                                              InputMode="DateInputMode.DateTime"
                                              @bind-Date="@FormReceiptVoucher.IssueDateTime" ReadOnly TabIndex='-1' />
                                </Div>
                            </Div>

                            <Div class="inline-input-label">
                                <FieldLabel>@L["FinancialAccount"]</FieldLabel>
                                <Div class="@FormWidthClass">
                                    <Select TValue="Guid" @bind-SelectedValue="@FormReceiptVoucher.FinancialAccountId"
                                            Disabled="IsFormReadOnly()" TabIndex='2'>
                                        <SelectItem TValue="Guid?" Value="@null">

                                        </SelectItem>

                                    </Select>

                                </Div>
                            </Div>
                            <Div class="inline-input-label">
                                <FieldLabel>@L["FinancialPeriod"]</FieldLabel>
                                <Div class="@FormWidthClass">
                                    <Select TValue="Guid" @bind-SelectedValue="@FormReceiptVoucher.FinancialPeriodId"
                                            Disabled="IsFormReadOnly()" TabIndex='2'>
                                        <SelectItem TValue="Guid?" Value="@null">

                                        </SelectItem>

                                    </Select>

                                </Div>
                            </Div>
                            <Div class="inline-input-label">
                                <FieldLabel>@L["CostCenter"]</FieldLabel>
                                <Div class="@FormWidthClass">
                                    <Select TValue="Guid" @bind-SelectedValue="@FormReceiptVoucher.CostCenterId"
                                            Disabled="IsFormReadOnly()" TabIndex='2'>
                                        <SelectItem TValue="Guid?" Value="@null">

                                        </SelectItem>

                                    </Select>

                                </Div>
                            </Div>




                        </Column>

                        <Column ColumnSize="ColumnSize.Is4" class="px-4 ">
                            <Div class="inline-input-label ">
                                <FieldLabel>@L["Amount"]</FieldLabel>
                                <Div Style="justify-content:space-between;" class="@FormWidthClass"
                                     Flex="Flex.AlignContent.Start">
                                    <Div class="col-9">
                                        <NumericPicker @bind-Value="@FormReceiptVoucher.Amount" GroupSeparator=","
                                                       Disabled="IsFormReadOnly()" TabIndex='3' />
                                    </Div>
                                    <Div>
                                        <Select TValue="Guid?" SelectedValue="@FormReceiptVoucher.CurrencyId"
                                                SelectedValueChanged="@(async (Guid? id)=>{ await OnChangeCurrency(id);})"
                                                Style="padding-left:30px;padding-right:10px" Disabled="true" TabIndex='4'>

                                            @foreach (var Currency in CurrenciesCollection)
                                            {
                                                <SelectItem TValue="Guid" Value="@Currency.Id">
                                                    @Currency.Code
                                                </SelectItem>
                                            }
                                        </Select>
                                    </Div>
                                </Div>


                            </Div>

                            <Div class="inline-input-label">
                                <FieldLabel>@L["CashierSeatLocation"]</FieldLabel>
                                <Div class="@FormWidthClass">
                                    <Div Class="button-group">

                                        @foreach (SeatLocationType item in Enum.GetValues(typeof(SeatLocationType)))
                                        {
                                            if (item != SeatLocationType.CentralVault)
                                            {
                                                <Button Clicked="@(() => ChangeCashierSeat(item))"
                                                        Class="@((SeatLocation == item ? "custom-outline-btn" : "") + "text-center")"
                                                        Outline="@(SeatLocation != item)" Disabled="IsFormReadOnly()||DisabledChangeCashierType">
                                                    @L[$"Enum:SeatLocationType.{(int)item}"]
                                                </Button>
                                            }

                                        }
                                    </Div>
                                </Div>

                            </Div>

                            @if (SeatLocation != SeatLocationType.Company && SeatLocation != 0)
                            {
                                @if (SeatLocation == SeatLocationType.ServicePoint)
                                {
                                    <Div class="inline-input-label">
                                        <FieldLabel>@L["ServicePoints"]</FieldLabel>
                                        <Div class="@FormWidthClass">
                                            <Select TValue="Guid?" SelectedValue="@FormReceiptVoucher.SeatLocationId"
                                                    SelectedValueChanged="@(async (Guid? id)=>{ await OnChangeServicePoint(id);})"
                                                    class="text-center" Disabled="IsFormReadOnly()||DisabledChangeCashierType" TabIndex='2'>
                                                <SelectItem TValue="Guid?" Value="@null">

                                                </SelectItem>
                                                @foreach (var servicePoint in ServicePointsCollection)
                                                {
                                                    <SelectItem TValue="Guid" Value="@servicePoint.Id">
                                                        @servicePoint.Name
                                                    </SelectItem>
                                                }
                                            </Select>

                                        </Div>
                                    </Div>
                                }
                                @if (SeatLocation == SeatLocationType.CentralVault)
                                {
                                    <Div class="inline-input-label">
                                        <FieldLabel>@L["CentralVault"]</FieldLabel>
                                        <Div class="@FormWidthClass">
                                            <Select TValue="Guid?" SelectedValue="@FormReceiptVoucher.SeatLocationId"
                                                    SelectedValueChanged="@(async (Guid? id)=>{ await OnChangeCentralVault(id);})"
                                                    class="text-center" Disabled="IsFormReadOnly()" TabIndex='2'>
                                                <SelectItem TValue="Guid?" Value="@null">

                                                </SelectItem>
                                                @foreach (var centralVault in CentralVaultCollection)
                                                {
                                                    <SelectItem TValue="Guid" Value="@centralVault.Id">
                                                        @centralVault.Name
                                                    </SelectItem>
                                                }
                                            </Select>

                                        </Div>
                                    </Div>
                                }


                                <Div class="inline-input-label">
                                    <FieldLabel>@L["Cashier"]</FieldLabel>
                                    <Div class="@FormWidthClass">
                                        <Select TValue="Guid?" class="text-center"
                                                @bind-SelectedValue="@FormReceiptVoucher.CashierId"
                                                Disabled="IsFormReadOnly()" TabIndex='2'>
                                            <SelectItem TValue="Guid?" Value="@null">

                                            </SelectItem>
                                            @foreach (var cashier in CashiersCollection)
                                            {
                                                <SelectItem TValue="Guid" Value="@cashier.Id">
                                                    @cashier.Name
                                                </SelectItem>
                                            }
                                        </Select>

                                    </Div>
                                </Div>
                            }
                        </Column>

                        <Column ColumnSize="ColumnSize.Is4" class="px-4">


                            <Div class="inline-input-label">
                                <FieldLabel>@L["UploadAttachmentFile"]</FieldLabel>
                                <Div class="@FormWidthClass" Style=" display: flex; gap: 4px;">
                                    <FileEdit TabIndex='6' Disabled="IsFormReadOnly()"
                                              Changed="@(async (args) => { await OnFileChanged(args); })" />
                                    @if (!string.IsNullOrEmpty(fileValidationError))
                                    {
                                        <ValidationError>@fileValidationError</ValidationError>
                                    }
                                    @if (FormReceiptVoucher.BlobContent != null)
                                    {
                                        <Button Class="btn btn-sm btn-outline-Light new-btn" Color="Color.Light" Outline
                                                Clicked="@(async() => {await DisplayFileInNewBrowserTab();})">
                                            <Icon Name="@IconName.Download" />
                                        </Button>
                                        <Button Class="btn btn-sm btn-outline-danger new-btn"
                                                Clicked="() => OnDeleteAttachmentClicked()">
                                            <Icon Name="@IconName.Times" />
                                        </Button>
                                    }
                                    else
                                    {
                                        <Button Class="btn btn-sm btn-outline-info new-btn" Disabled="IsFormReadOnly()"
                                                Clicked="() => FetchAndDisplayImage()">
                                            <Tooltip Text="@L["ScanneFile"]">
                                                <Icon Name="@IconName.FileAlt" />
                                            </Tooltip>
                                        </Button>
                                    }
                                </Div>
                            </Div>








                            <Div class="inline-input-label">
                                <FieldLabel>@L["FileName"]</FieldLabel>
                                <Div class="@FormWidthClass">
                                    <TextEdit Text="@FormReceiptVoucher.FileName" Disabled="true" TabIndex='-1' />

                                </Div>

                            </Div>
                            <Div class="inline-input-label">
                                <FieldLabel>@L["FileSize"]</FieldLabel>
                                <Div class="@FormWidthClass">
                                    <TextEdit Text="@FormReceiptVoucher.FileSize" Disabled="true" TabIndex='-1' />

                                </Div>

                            </Div>
                            <Div class="inline-input-label">
                                <FieldLabel>@L["FileExtension"]</FieldLabel>
                                <Div class="@FormWidthClass">
                                    <TextEdit Text="@FormReceiptVoucher.FileExtension" Disabled="true" TabIndex='-1' />

                                </Div>

                            </Div>
                            <Div class="inline-input-label">
                                <FieldLabel>@L["Notes"]</FieldLabel>
                                <Div class="@FormWidthClass">
                                    <MemoEdit @bind-Text="@FormReceiptVoucher.Notes" Disabled="IsFormReadOnly()"
                                              TabIndex='5'>
                                    </MemoEdit>
                                </Div>

                            </Div>
                        </Column>

                    </Row>
                </CardBody>
            </Card>

            <Card class="mt-2">
                <CardBody>



                    <Row>
                        <CardFooter>
                            <Div Style="display: flex;justify-content: space-between; padding-top:30px;">
                                <Span Style="margin-right: auto;order: 1; width: 50%;color: red;margin-right:20px;">@LastStatusBy</Span>
                                <Span Style="margin-left: auto;order: 2; width: 50%;color: red;margin-right:20px;">@LastStatusDateTime</Span>
                            </Div>
                        </CardFooter>
                    </Row>



                </CardBody>



            </Card>

        </Validations>
    </Form>

    @* ************************* Scan MODAL ************************* *@

    <Modal @ref="ScanDocumentModal" Closing="@ScanDocumentModal.CancelClosingModalWhenFocusLost">
        <ModalContent Centered Size="ModalSize.Large">
            <ModalHeader>
                <ModalTitle>@L["Scanned Document"]</ModalTitle>
                <CloseButton Clicked="CloseScanDocumentModal" />
            </ModalHeader>
            <ModalBody>

                <Field>
                    <FieldLabel>@L["ScannedFileName"]*</FieldLabel>
                    <TextEdit @bind-Text="@ScannedFileName">
                    </TextEdit>
                </Field>

                @if (!string.IsNullOrEmpty(ScannedImageBase64))
                {
                    <img src="data:image/png;base64,@ScannedImageBase64" alt="Scanned Document"
                         Style="max-width: 100%; max-height: 100%;" />
                }
                else
                {
                    <p>No image available.</p>
                }
            </ModalBody>
            <ModalFooter>
                <Button Color="Color.Secondary" Clicked="CloseScanDocumentModal">
                    @L["Close"]
                </Button>
                <Button Color="Color.Success" Clicked="AcceptDocumentScanned">
                    @L["Accept"]
                </Button>
            </ModalFooter>
        </ModalContent>
    </Modal>
</LoadingIndicator>
