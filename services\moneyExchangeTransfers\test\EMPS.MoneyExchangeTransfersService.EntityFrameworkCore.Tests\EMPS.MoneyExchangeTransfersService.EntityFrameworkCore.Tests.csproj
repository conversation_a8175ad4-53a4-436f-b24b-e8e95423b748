<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>EMPS.MoneyExchangeTransfersService</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.2.0" />
    <PackageReference Include="Microsoft.Data.Sqlite" Version="7.0.0" />
    <PackageReference Include="Volo.Abp.EntityFrameworkCore.Sqlite" Version="7.3.2" />
    <ProjectReference Include="..\EMPS.MoneyExchangeTransfersService.TestBase\EMPS.MoneyExchangeTransfersService.TestBase.csproj" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\EMPS.MoneyExchangeTransfersService.EntityFrameworkCore\EMPS.MoneyExchangeTransfersService.EntityFrameworkCore.csproj" />
  </ItemGroup>

</Project>
