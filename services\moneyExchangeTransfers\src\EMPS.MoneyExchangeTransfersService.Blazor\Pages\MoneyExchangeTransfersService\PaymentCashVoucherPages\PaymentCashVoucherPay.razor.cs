using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazorise;
using Blazorise.DataGrid;
using Volo.Abp.BlazoriseUI.Components;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.MoneyExchangeTransfersService.Shared;
using AutoMapper.Internal.Mappers;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.ServiceModel.Channels;
using Volo.Abp;
using EMPS.MoneyExchangeTransfersService.Blazor.Components;
using EMPS.MoneyExchangeTransfersService.CashVouchers;

namespace EMPS.MoneyExchangeTransfersService.Blazor.Pages.MoneyExchangeTransfersService.PaymentCashVoucherPages
{
    public partial class PaymentCashVoucherPay
    {

        [Inject]
        private IPaymentCashVouchersAppService _PaymentVoucherService { get; set; }
        


        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar { get; } = new PageToolbar();


        private bool CanPayPaymentVoucherDraft { get; set; }

        private bool IsReadOnly { get; set; }


        private PaymentCashVoucherForm PaymentVoucherFormRef { get; set; }


        public PaymentCashVoucherPay()
        {
        }

        protected override async Task OnInitializedAsync()
        {

            await SetToolbarButtons(null);
            await SetBreadcrumbItemsAsync();
            await SetPermissionsAsync();
        }


        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:PaymentCashVoucherPay"]));


            return ValueTask.CompletedTask;
        }


        private async void ResetPaymentVoucher(bool forceReset = false)
        {
            if (await PaymentVoucherFormRef.ResetPaymentVoucher(forceReset))
            {
                await SetToolbarButtons(null);
                SetFormIsReadOnly(null);
                StateHasChanged();

            }


        }

    
        private async Task SetPermissionsAsync()
        {
            CanPayPaymentVoucherDraft = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.PaymentCashVouchers.Pay);

        }




    



        private IReadOnlyList<string> stringList { get; set; }

        private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<string> e)
        {
        }

        private async Task HandleTransfareLoad(CashVoucherDto LoadedPaymentVoucher)
        {
            await SetToolbarButtons(LoadedPaymentVoucher);
            SetFormIsReadOnly(LoadedPaymentVoucher);
            StateHasChanged();

        }

     

        private async Task SetToolbarButtons(CashVoucherDto? accountPayment)
        {
            Toolbar.Contributors.Clear();


            Toolbar.AddButton(
                L["NewPaymentVoucher"], () =>
                {
                    ResetPaymentVoucher();

                    return Task.CompletedTask;

                }, IconName.Add, Color.Warning


            );
            Toolbar.AddButton(
           L["Pay"], async () =>
           {
               await PerformPay();
           }, IconName.Save, requiredPolicyName:
           MoneyExchangeTransfersServicePermissions.PaymentCashVouchers.Pay, color: Color.Success, disabled: !CanPaymentVoucherBePay(accountPayment)
       );


            StateHasChanged();


        }


        private bool CanPaymentVoucherBePay(CashVoucherDto ?accountPayment)
        {
            if(accountPayment == null) return false;
            if (accountPayment != null)
                if (accountPayment.IsExecute||! accountPayment.IsApproved) return false;

            return true;
        }

        private void SetFormIsReadOnly(CashVoucherDto? accountPayment)
        {

            this.IsReadOnly = false;
            if (accountPayment == null) this.IsReadOnly = true; 
            else

            if (accountPayment.IsExecute|| !accountPayment.IsApproved)
            {
                this.IsReadOnly = true;
            }
        }

        private async Task PerformPay()
        {
            await Task.Delay(800);
            if (!PaymentVoucherFormRef.IsPaymentVoucherLoadedProp)
            {
                await Message.Error(L["Error:LoadPaymentVoucherFirst"]);
                return;
            }

            if (!CanPaymentVoucherBePay(PaymentVoucherFormRef.FormPaymentVoucher))
            {
                await Message.Error(L["Error:ThisPaymentVoucherCannotBePayd"]);
                return;
            }

            CashVoucherDto? newPaymentVoucher = new();
            var PaymentVoucher = PaymentVoucherFormRef.FormPaymentVoucher;


            var confirmation = await Message.Confirm(L["PayPaymentVoucherConfirmationMessage"] + PaymentVoucherFormRef.GetPaymentVoucherNo());
            if (!confirmation) return;
            await PaymentVoucherFormRef.loadingIndicator.Show();

            try
            {
                newPaymentVoucher = await PayPaymentVoucher();

                if (newPaymentVoucher != null)
                {
                    await SetToolbarButtons(newPaymentVoucher);

                    await Message.Success(L["PaySuccess"]);
                }


            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await PaymentVoucherFormRef.loadingIndicator.Hide();

            }
        }
        private async Task<CashVoucherDto> PayPaymentVoucher()
        {
            var PaydTransaction = await _PaymentVoucherService.PayByNoAsync(PaymentVoucherFormRef.GetPaymentVoucherNo());
            PaymentVoucherFormRef.ChangeEntity(PaydTransaction);
            return PaydTransaction;
        }
        private async Task PerformNewTransfare()
        {
            ResetPaymentVoucher();

        }
        public async Task HandleKeyDown(KeyboardEventArgs e)
        {

            ExecuteJS("event.stopPropagation();");
            if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyA"))
            {
                await PerformPay();
            } else
            if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyR"))
            {
                await PerformNewTransfare();
            }
            else if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyG"))
            {
                await Task.Delay(50);
                await PaymentVoucherFormRef.PerformLoadPaymentVoucher();
            }
             


        }
        private void ExecuteJS(string script)
        {
            JSRuntime.InvokeVoidAsync("eval", script);
        }
        [Inject] IJSRuntime JSRuntime { get; set; }
        protected override void OnAfterRender(bool firstRender)
        {
            base.OnAfterRender(firstRender);
            if (firstRender)
            {
                // See warning about memory above in the article
                var dotNetReference = DotNetObjectReference.Create(this);
                JSRuntime.InvokeVoidAsync("GetDotnetInstance", dotNetReference);
            }
        }






    }
}
