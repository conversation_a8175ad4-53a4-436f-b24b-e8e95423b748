﻿using EMPS.CompanyService.ServicePoints;
using EMPS.CompanyService.Staffs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
{
    public interface ICashierToCashierTransferService: IMoneyExchangeTransferBase
    {
        Task<List<StaffDto>> GetAllCashierInServicePointAsync(Guid ServicePointId);
        Task<StaffWithNavigationPropertiesDto> GetStaffWithNavigationPropertiesAsync();
        Task<PagedResultDto<MoneyExchangeTransferCurrencyDto>> GetTransfersReadyToExecuteForCashier();



    }
}
