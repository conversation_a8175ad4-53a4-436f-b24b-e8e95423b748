using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazorise;
using Blazorise.DataGrid;
using Volo.Abp.BlazoriseUI.Components;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.MoneyExchangeTransfersService.Shared;
using AutoMapper.Internal.Mappers;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.ServiceModel.Channels;
using Volo.Abp;
using EMPS.MoneyExchangeTransfersService.Blazor.Components;

namespace EMPS.MoneyExchangeTransfersService.Blazor.Pages.MoneyExchangeTransfersService.CompanyToServicePointTransferPages
{
    public partial class CompanyToServicePointTransferApprove
    {

        [Inject]
        private ICompanyToServicePointTransferService _CompanyToServicePointTransferService { get; set; }



        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar { get; } = new PageToolbar();


        private bool CanApproveCompanyToServicePointTransferDraft { get; set; }

        private bool IsReadOnly { get; set; }


        private CompanyToServicePointForm CompanyToServicePointFormRef { get; set; }


        public CompanyToServicePointTransferApprove()
        {
        }

        protected override async Task OnInitializedAsync()
        {

            await SetToolbarButtons(null);
            await SetBreadcrumbItemsAsync();
            await SetPermissionsAsync();
        }


        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:CompanyToServicePointTransferApprove"]));


            return ValueTask.CompletedTask;
        }


        private async void ResetCompanyToServicePointTransfer(bool forceReset = false)
        {
            if (await CompanyToServicePointFormRef.ResetCompanyToServicePoint(forceReset))
            {
                await SetToolbarButtons(null);
                SetFormIsReadOnly(null);
                StateHasChanged();

            }


        }


        private async Task SetPermissionsAsync()
        {

            CanApproveCompanyToServicePointTransferDraft = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Approve);
        }




        private void Log(string message)
        {
            Console.WriteLine($"MY-LOGGING: {message}");
        }



        private IReadOnlyList<string> stringList { get; set; }

        private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<string> e)
        {
        }

        private async Task HandleTransfareLoad(MoneyExchangeTransferCurrencyDto LoadedCompanyToServicePointTransfer)
        {
            await SetToolbarButtons(LoadedCompanyToServicePointTransfer);
            SetFormIsReadOnly(LoadedCompanyToServicePointTransfer);
            Log(IsReadOnly.ToString());
            StateHasChanged();

        }



        private async Task SetToolbarButtons(MoneyExchangeTransferCurrencyDto? accountPayment)
        {
            Toolbar.Contributors.Clear();




            Toolbar.AddButton(
         L["NewCompanyToServicePointTransfer"], () =>
         {
             ResetCompanyToServicePointTransfer();

             return Task.CompletedTask;

         }, IconName.Add, Color.Warning


     );

            Toolbar.AddButton(
                L["ApproveCompanyToServicePointTransfer"], async () =>
                {
                    await PerformApprove();
                }, IconName.Save, requiredPolicyName:
                MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Approve, color: Color.Success, disabled: !CanCompanyToServicePointTransferBeApprove(accountPayment)
            );

            StateHasChanged();


        }

        private async Task PerformApprove()
        {
            await Task.Delay(300);


            if (CompanyToServicePointFormRef.GetCompanyToServicePointId() == Guid.Empty)
            {
                await Message.Error(L["Error:LoadCompanyToServicePointTransferFirst"]);
                return;
            }
            if (!CanCompanyToServicePointTransferBeApprove(CompanyToServicePointFormRef.FormCompanyToServicePoint))
            {
                await Message.Error(L["Error:ThisCompanyToServicePointTransferCannotBeApproved"]);
                return;
            }

            var confirmation = await Message.Confirm(L["ApproveCompanyToServicePointTransferConfirmationMessage"] + CompanyToServicePointFormRef.GetCompanyToServicePointNo()!);
            if (!confirmation) return;

            await CompanyToServicePointFormRef.loadingIndicator.Show();

            try
            {

                var transfare = await _CompanyToServicePointTransferService.ApproveByNoAsync(CompanyToServicePointFormRef.GetCompanyToServicePointNo()!);

                await Message.Success(L["ApprovedSuccessfully"]);


                CompanyToServicePointFormRef.ChangeEntity(transfare);
                await SetToolbarButtons(transfare);
            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await CompanyToServicePointFormRef.loadingIndicator.Hide();

            }

        }

        private bool CanCompanyToServicePointTransferBeApprove(MoneyExchangeTransferCurrencyDto accountPayment)
        {



            if (accountPayment == null) return false;
            if (accountPayment.Id == Guid.Empty) return false;
            if (accountPayment.IsApproved) return false;
            if (accountPayment.IsExecute) return false;


            return true;
        }

        private void SetFormIsReadOnly(MoneyExchangeTransferCurrencyDto? accountPayment)
        {

            this.IsReadOnly = false;
            if (accountPayment == null) return;
            if (accountPayment.IsApproved)
            {
                this.IsReadOnly = true;
            }
        }

        private async Task PerformNewTransfare()
        {
            ResetCompanyToServicePointTransfer();

        }
        public async Task HandleKeyDown(KeyboardEventArgs e)
        {

            ApproveJS("event.stopPropagation();");

            if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyA"))
            {
                await PerformApprove();
            }
            else if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyR"))
            {
                await PerformNewTransfare();
            }
            else if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyG"))
            {
                await Task.Delay(1000);

                await CompanyToServicePointFormRef.PerformLoadCompanyToServicePoint();
            }



        }
        private void ApproveJS(string script)
        {
            JSRuntime.InvokeVoidAsync("eval", script);
        }
        [Inject] IJSRuntime JSRuntime { get; set; }
        protected override void OnAfterRender(bool firstRender)
        {
            base.OnAfterRender(firstRender);
            if (firstRender)
            {
                // See warning about memory above in the article
                var dotNetReference = DotNetObjectReference.Create(this);
                JSRuntime.InvokeVoidAsync("GetDotnetInstance", dotNetReference);
            }
        }






    }
}
