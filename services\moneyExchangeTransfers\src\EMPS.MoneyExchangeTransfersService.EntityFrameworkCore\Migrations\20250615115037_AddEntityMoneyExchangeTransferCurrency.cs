﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMPS.MoneyExchangeTransfersService.Migrations
{
    /// <inheritdoc />
    public partial class AddEntityMoneyExchangeTransferCurrency : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MoneyExchangeTransferCurrencies",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    No = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Amount = table.Column<double>(type: "float", nullable: false),
                    AverageCost = table.Column<double>(type: "float", nullable: false),
                    CurrencyCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CurrencyId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ServicePointName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ServicePointId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    TransactionIssueUserName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TransactionIssueUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    TransactionIssueStaffName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TransactionIssueStaffId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    TransactionDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    TransactionIssueSeatId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsApproved = table.Column<bool>(type: "bit", nullable: false),
                    ApprovedByUserName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ApprovedByUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ApprovalDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ApprovalDateTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsExecute = table.Column<bool>(type: "bit", nullable: false),
                    ExecutedByUserName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ExecutedByUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ExecutedByStaffName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ExecutedByStaffId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ExecutedBySeatId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ExecuteDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ExecuteDateTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    TransferStatus = table.Column<int>(type: "int", nullable: false),
                    TransferType = table.Column<int>(type: "int", nullable: false),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyExchangeTransferCurrencies", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MoneyExchangeTransferCurrencies");
        }
    }
}
