{"Culture": "en", "Texts": {"Enum:MoneyExchangeTransferStatus.1": "Pending", "Enum:MoneyExchangeTransferStatus.2": "Accepted", "Enum:MoneyExchangeTransferStatus.3": "Canceled", "Enum:MoneyExchangeTransferType.1": "Cashier to cashier", "Enum:MoneyExchangeTransferType.2": "Company to service point", "Enum:MoneyExchangeTransferType.3": "Service point to company", "Permission:MoneyExchangeTransferCurrencies": "Money Exchange Transfer Currencies", "Permission:MoneyExchangeTransfersService": "Money Exchange Transfers Service", "Permission:Create": "Create", "Permission:Edit": "Edit", "Permission:Delete": "Delete", "No": "No", "Amount": "Amount", "CurrencyCode": "Currency Code", "CurrencyId": "<PERSON><PERSON><PERSON><PERSON> Id", "ServicePointName": "Service Point Name", "ServicePointId": "Service Point Id", "TransactionIssueUserName": "Transaction Issue User Name", "TransactionIssueUserId": "Transaction Issue User Id", "TransactionIssueStaffName": "Transaction Issue Staff Name", "TransactionIssueStaffId": "Transaction Issue Staff Id", "TransactionIssueSeatId": "Transaction Issue Seat Id", "IsApproved": "Is Approved", "ApprovedByUserName": "Approved By User Name", "ApprovedByUserId": "Approved By User Id", "ApprovalDate": "Approval Date", "ApprovalDateTime": "Approval Date Time", "Notes": "Notes", "IsExecute": "Is Execute", "ExecutedByUserName": "Executed By User Name", "ExecutedByUserId": "Executed By User Id", "ExecutedByStaffName": "Executed By Staff Name", "ExecutedByStaffId": "Executed By Staff Id", "ExecutedBySeatId": "Executed By Seat Id", "ExecuteDate": "Execute Date", "ExecuteDateTime": "Execute Date Time", "TransferStatus": "Transfer Status", "TransferType": "Transfer Type", "MinAmount": "<PERSON>", "MinApprovalDate": "<PERSON> Approval Date", "MinApprovalDateTime": "Min Approval Date Time", "MinExecuteDate": "Min Execute Date", "MinExecuteDateTime": "Min Execute Date Time", "MaxAmount": "<PERSON>", "MaxApprovalDate": "<PERSON> Approval Date", "MaxApprovalDateTime": "Max Approval Date Time", "MaxExecuteDate": "Max Execute Date", "MaxExecuteDateTime": "Max Execute Date Time", "MoneyExchangeTransferCurrencies": "Money Exchange Transfer Currencies", "NewMoneyExchangeTransferCurrency": "New Money Exchange Transfer Currency", "Actions": "Actions", "SuccessfullyDeleted": "Successfully deleted", "DeleteConfirmationMessage": "Are you sure you want to delete this record?", "Search": "Search", "Pick": "Pick", "SeeAdvancedFilters": "Advanced filters", "ItemAlreadyAdded": "This item is already added.", "ExportToExcel": "Export to Excel", "Menu:MoneyExchangeTransferCurrencies": "Money Exchange Transfer Currencies", "Menu:MoneyExchangeTransfersService": "Money Exchange Transfers Service", "ServicePointToCompanyTransferIssue": "Service Point To Company Transfer Issue", "ServicePointToCompanyTransferExecute": "Service Point To Company Transfer Execute", "ServicePointToCompanyTransferApprove": "Service Point To Company Transfer Approve", "Menu:ServicePointToCompanyTransferIssue": "Service Point To Company Transfer Issue", "Menu:ServicePointToCompanyTransferExecute": "Service Point To Company Transfer Execute", "Menu:ServicePointToCompanyTransferApprove": "Service Point To Company Transfer Approve", "NewServicePointToCompanyTransfer": "New Service Point To Company Transfer", "ExecuteServicePointToCompanyTransfer": "Execute Service Point To Company Transfer", "ApproveServicePointToCompanyTransfer": "Approve Service Point To Company Transfer", "ServicePointToCompanyNotFound": "Service Point To Company Transfer Not Found", "Permission:ServicePointToCompanyTransfer": "Service Point To Company Transfer", "Permission:Save": "Save", "Permission:Execute": "Execute", "Permission:Approve": "Approve", "Permission:ViewToExecute": "View To Execute", "ResetConfirmationMessage": "Are you sure you want to reset this form?", "ExecuteConfirmationMessage": "Are you sure you want to execute this transfer?", "ApproveConfirmationMessage": "Are you sure you want to approve this transfer?", "ApprovedByUser": "Approved By User", "DateTime": "Date Time", "ServicePoints": "Service Points", "Permission:CashVouchers": "Cash Vouchers", "CashVouchers": "Cash Vouchers", "NewCashVoucher": "New Cash Voucher", "Menu:CashVouchers": "Cash Vouchers", "Enum:AccountingMode.1": "Payment", "Enum:AccountingMode.2": "Receipt", "Enum:SeatLocationType.1": "Company", "Enum:SeatLocationType.2": "Service point", "Enum:SeatLocationType.3": "Central vault", "SeatLocationName": "Seat Location Name", "SeatLocationId": "Seat Location Id", "IssueUserName": "Issue User Name", "IssueUserId": "Issue User Id", "IssueStaffName": "Issue Staff Name", "IssueStaffId": "Issue Staff Id", "IssueSeatId": "Issue Seat Id", "ApproveByStaffName": "Approve By Staff Name", "ApproveByStaffId": "Approve By Staff Id", "ApproveBySeatId": "Approve By Seat Id", "AccountingType": "Accounting Type", "BolbKey": "Bo<PERSON>b Key", "FileName": "File Name", "FileExtension": "File Extension", "FileSize": "File Size", "FinancialPeriodId": "Financial Period Id", "FinancialPeriodName": "Financial Period Name", "FinancialAccountId": "Financial Account Id", "FinancialAccountName": "Financial Account Name", "AssignSeatLocation": "Assign Seat Location", "CostCenterId": "Cost Center Id", "CostCenterName": "Cost Center Name"}}