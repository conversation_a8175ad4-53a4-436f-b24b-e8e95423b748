using EMPS.MoneyExchangeTransfersService.Permissions;
using System.Collections.Generic;
using Microsoft.AspNetCore.Authorization;
using System.Threading.Tasks;
using EMPS.MoneyExchangeTransfersService.Localization;
using Volo.Abp.UI.Navigation;

namespace EMPS.MoneyExchangeTransfersService.Blazor.Menus;

public class MoneyExchangeTransfersServiceMenuContributor : IMenuContributor
{
    public async Task ConfigureMenuAsync(MenuConfigurationContext context)
    {
        if (context.Menu.Name == StandardMenus.Main)
        {
            await ConfigureMainMenuAsync(context);
        }

        var moduleMenu = AddModuleMenuItem(context);
        AddMenuItemMoneyExchangeTransferCurrencies(context, moduleMenu);
    }

    private static Task ConfigureMainMenuAsync(MenuConfigurationContext context)
    {
        var l = context.GetLocalizer<MoneyExchangeTransfersServiceResource>();
        return Task.CompletedTask;
    }

    private static ApplicationMenuItem AddModuleMenuItem(MenuConfigurationContext context)
    {
        var moduleMenu = new ApplicationMenuItem(
            MoneyExchangeTransfersServiceMenus.Prefix,
            context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:MoneyExchangeTransfersService"],
            icon: "fa fa-folder"
        );

        context.Menu.Items.AddIfNotContains(moduleMenu);
        return moduleMenu;
    }
    private static void AddMenuItemMoneyExchangeTransferCurrencies(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
            new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.MoneyExchangeTransferCurrencies,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:MoneyExchangeTransferCurrencies"],
                "/MoneyExchangeTransferCurrencies",
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeTransfersServicePermissions.MoneyExchangeTransferCurrencies.Default
            )
        );
    }
}