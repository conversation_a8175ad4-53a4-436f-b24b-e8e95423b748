using EMPS.MoneyExchangeTransfersService.Permissions;
using System.Collections.Generic;
using Microsoft.AspNetCore.Authorization;
using System.Threading.Tasks;
using EMPS.MoneyExchangeTransfersService.Localization;
using Volo.Abp.UI.Navigation;

namespace EMPS.MoneyExchangeTransfersService.Blazor.Menus;

public class MoneyExchangeTransfersServiceMenuContributor : IMenuContributor
{
    public async Task ConfigureMenuAsync(MenuConfigurationContext context)
    {
        if (context.Menu.Name == StandardMenus.Main)
        {
            await ConfigureMainMenuAsync(context);
        }

        var moduleMenu = AddModuleMenuItem(context);
        AddMenuItemPaymentCashVouchers(context, moduleMenu);
        AddMenuItemReceiptCashVouchers(context, moduleMenu);
        AddMenuItemCashierToCashierTransfer(context, moduleMenu);
        AddMenuItemCompanyToServicePointTransfer(context, moduleMenu);
        AddMenuItemServicePointToCompanyTransfer(context, moduleMenu);
    }

    private static Task ConfigureMainMenuAsync(MenuConfigurationContext context)
    {
        var l = context.GetLocalizer<MoneyExchangeTransfersServiceResource>();
        return Task.CompletedTask;
    }

    private static ApplicationMenuItem AddModuleMenuItem(MenuConfigurationContext context)
    {
        var moduleMenu = new ApplicationMenuItem(
            MoneyExchangeTransfersServiceMenus.Prefix,
            context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:MoneyExchangeTransfersService"],
            icon: "fa fa-folder"
        );

        context.Menu.Items.AddIfNotContains(moduleMenu);
        return moduleMenu;
    }
    private static void AddMenuItemMoneyExchangeTransferCurrencies(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
            new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.MoneyExchangeTransferCurrencies,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:MoneyExchangeTransferCurrencies"],
                "/MoneyExchangeTransferCurrencies",
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeTransfersServicePermissions.MoneyExchangeTransferCurrencies.Default
            )
        );
    }

    private static void AddMenuItemCashierToCashierTransfer(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        var theBigOne = new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.CashierToCashierTransfer,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:CashierToCashierTransfer"],
                icon: "fa fa-file-alt",

                requiredPermissionName: MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Default);

        parentMenu.AddItem(theBigOne);

        AddMenuItemCashierToCashierTransferDraft(context, theBigOne);
        AddMenuItemCashierToCashierTransferExecute(context, theBigOne);

    }

    private static void AddMenuItemCashierToCashierTransferDraft(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
           new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.CashierToCashierTransfer,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:CashierToCashierTransfer"],
                url: "/MoneyExchangeTransfersService/CashierToCashierTransferIssue",
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Default));
    }

    private static void AddMenuItemCashierToCashierTransferExecute(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
           new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.CashierToCashierTransfer,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:ExcuteCashierToCashierTransfer"],
                url: "/MoneyExchangeTransfersService/CashierToCashierTransferExecute",
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Execute));
    }

    private static void AddMenuItemCompanyToServicePointTransfer(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        var theBigOne = new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.CompanyToServicePointTransfer,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:CompanyToServicePointTransfer"],
                icon: "fa fa-file-alt",

                requiredPermissionName: MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Default);

        parentMenu.AddItem(theBigOne);

        AddMenuItemCompanyToServicePointTransferDraft(context, theBigOne);
        AddMenuItemCompanyToServicePointTransferApprove(context, theBigOne);
        AddMenuItemCompanyToServicePointTransferExecute(context, theBigOne);

    }

    private static void AddMenuItemCompanyToServicePointTransferDraft(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
           new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.CompanyToServicePointTransfer,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:CompanyToServicePointTransfer"],
                url: "/MoneyExchangeTransfersService/CompanyToServicePointTransferIssue",
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Default));
    }
    private static void AddMenuItemCompanyToServicePointTransferApprove(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
           new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.CompanyToServicePointTransfer,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:ApproveCompanyToServicePointTransfer"],
                url: "/MoneyExchangeTransfersService/CompanyToServicePointTransferApprove",
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Approve));
    }
    private static void AddMenuItemCompanyToServicePointTransferExecute(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
           new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.CompanyToServicePointTransfer,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:ExcuteCompanyToServicePointTransfer"],
                url: "/MoneyExchangeTransfersService/CompanyToServicePointTransferExecute",
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Execute));
    }

    private static void AddMenuItemServicePointToCompanyTransfer(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        var theBigOne = new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.ServicePointToCompanyTransfer,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:ServicePointToCompanyTransfer"],
                icon: "fa fa-file-alt",

                requiredPermissionName: MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Default);

        parentMenu.AddItem(theBigOne);

        AddMenuItemServicePointToCompanyTransferDraft(context, theBigOne);
        AddMenuItemServicePointToCompanyTransferApprove(context, theBigOne);
        AddMenuItemServicePointToCompanyTransferExecute(context, theBigOne);

    }

    private static void AddMenuItemServicePointToCompanyTransferDraft(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
           new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.ServicePointToCompanyTransfer,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:ServicePointToCompanyTransfer"],
                url: "/ServicePointToCompanyTransferIssue",
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Default));
    }
    private static void AddMenuItemServicePointToCompanyTransferApprove(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
           new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.ServicePointToCompanyTransfer,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:ApproveServicePointToCompanyTransfer"],
                url: "/ServicePointToCompanyTransferApprove",
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Approve));
    }
    private static void AddMenuItemServicePointToCompanyTransferExecute(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
           new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.ServicePointToCompanyTransfer,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:ExcuteServicePointToCompanyTransfer"],
                url: "/ServicePointToCompanyTransferExecute",
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Execute));
    }

    private static void  AddMenuItemPaymentCashVouchers(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        var theBigOne =
           new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.CashVouchers,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:PaymentCashVouchers"],
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeTransfersServicePermissions.PaymentCashVouchers.Default
            );
        parentMenu.AddItem(theBigOne);

        AddMenuItemIssuePaymentCashVouchers(context, theBigOne);
        AddMenuApproveItemPaymentCashVouchers(context, theBigOne);
        AddMenuPayItemPaymentCashVouchers(context, theBigOne);




    }
    private static void AddMenuItemIssuePaymentCashVouchers(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
            new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.CashVouchers,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:IssuePaymentCashVouchers"],
                "/IssuePaymentCashVouchers",
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeTransfersServicePermissions.PaymentCashVouchers.Default
            )
        );
    }
    private static void AddMenuApproveItemPaymentCashVouchers(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
            new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.CashVouchers,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:ApprovePaymentCashVouchers"],
                "/ApprovePaymentCashVouchers",
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeTransfersServicePermissions.PaymentCashVouchers.Approve
            )
        );
    }
    private static void AddMenuPayItemPaymentCashVouchers(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
            new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.CashVouchers,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:PayPaymentCashVouchers"],
                "/PayPaymentCashVouchers",
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeTransfersServicePermissions.PaymentCashVouchers.Pay
            )
        );
    }


    private static void AddMenuItemReceiptCashVouchers(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {

        var theBigOne = new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.CashVouchers,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:ReceiptCashVouchers"],
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Default
            );
        parentMenu.AddItem(theBigOne);

        AddMenuItemIssueReceiptCashVouchers(context, theBigOne);
        AddMenuItemApproveReceiptCashVouchers(context,theBigOne);
        AddMenuItemRecReceiptCashVouchers(context, theBigOne);
    }
    private static void AddMenuItemIssueReceiptCashVouchers(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
            new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.CashVouchers,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:IssueReceiptCashVouchers"],
                "/IssueReceiptCashVouchers",
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Default
            )
        );
    }
    private static void AddMenuItemApproveReceiptCashVouchers(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
            new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.CashVouchers,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:ApproveReceiptCashVouchers"],
                "/ApproveReceiptCashVouchers",
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Approve
            )
        );
    }
    private static void AddMenuItemRecReceiptCashVouchers(MenuConfigurationContext context, ApplicationMenuItem parentMenu)
    {
        parentMenu.AddItem(
            new ApplicationMenuItem(
                Menus.MoneyExchangeTransfersServiceMenus.CashVouchers,
                context.GetLocalizer<MoneyExchangeTransfersServiceResource>()["Menu:ReceiptCashVouchers"],
                "/RecReceiptCashVouchers",
                icon: "fa fa-file-alt",
                requiredPermissionName: MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Receipt
            )
        );
    }
}