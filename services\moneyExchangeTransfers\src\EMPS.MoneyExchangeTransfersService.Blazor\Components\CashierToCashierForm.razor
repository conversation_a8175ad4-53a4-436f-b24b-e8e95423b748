@using Blazorise
@using Blazorise.Components
@using EMPS.MoneyExchangeTransfersService.Blazor.Pages.MoneyExchangeTransfersService
@using EMPS.Shared.Enum
@using Microsoft.Extensions.Localization
@using EMPS.FeeService.Localization
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using Microsoft.AspNetCore.Components
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Http.Client
@using Blazorise.LoadingIndicator

@inherits MoneyExchangeTransfersServiceComponentBase
@inject NavigationManager NavigationManager

<style>
    .inline-input-label {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .form-control:disabled,
    .form-control[readonly],
    select[readonly] {
        background-color: #b4bbc2;
        opacity: 1;
    }

    .input-group-text {
        display: flex;
        align-items: center;
        padding: 0;
        font-size: 0;
    }

    .b-numeric-handler-wrap {
        position: fixed;
    }

    .form-group,
    .form-control,
    .form-select,
    .form-label {
        margin-bottom: 5px;
        font-size: 8pt;
        height: 30px;
        text-align: right;
        font-weight: bold;
        width: 100%;
    }



    .custom-card {
        width: 30%;
    }

    .card .card-body {
        padding: 0.5rem;
        margin-bottom: 0;
    }

    .card-header {
        padding: 0;
    }

    .table-responsive {
        min-height: 30%;
        background-color: var(--lpx-card-bg);
        box-shadow: var(--lpx-shadow);
        border-radius: var(--lpx-radius);
        margin-bottom: 1rem;
        padding-top: 30px !important;
    }

    .form-control:hover,
    .form-control:focus,
    .form-select:hover,
    .form-select:focus {
        box-shadow: aliceblue;
        border-color: #032b4d;
        color: #043e68;
    }

    .form-control,
    .form-select {
        background-color: #f0f4f7;
        border-radius: 0.5rem;
        padding: 0;
        transition: background-color 0.2s, border-color 0.4s;
        border-color: #f5f5f5;
        color: #00080d;
        width: 100%;
    }

    .card .card-header .btn {
        font-size: 8pt;
        padding: 0.375rem 0.5rem;
    }


    .b-table .btn {
        padding: 0.375rem 0.75rem !important;
        font-size: 8pt;
        width: auto;
    }

    .card-header {
        padding: 0;
    }

    .tab-content {
        border-radius: var(--lpx-radius);
        background: var(--lpx-card-bg);
        box-shadow: var(--lpx-shadow);
        padding: 1rem;
    }

    .card {
        color: #325168 !important;
        margin-bottom: 0;
    }

    .tab-content {
        border-radius: var(--lpx-radius);
        background: var(--lpx-card-bg);
        box-shadow: var(--lpx-shadow);
        padding: 0;
    }


    h4,
    .h4 {
        font-size: 13pt;
    }



    .form-label {
    @*width:40%!important;*@
    }

    .table > :not(caption) > * > * {
        padding: 0.5rem 5.5rem;
        ;
        background-color: var(--bs-table-bg);
        border-bottom-width: 0px;
        box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg);
    }



    textarea {
        height: 60px !important;
    }


    .form-check-input:focus {
        border-color: #86b7fe;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(227, 62, 132, 0.5);
    }
</style>

@code {
    string FormWidthClass = "col-9";
}

<LoadingIndicator @ref="loadingIndicator">

    <Form Model="@FormCashierToCashierTransfer">
        <Validations @ref=FormCashierToCashierTransferValidations Model="@FormCashierToCashierTransfer"
                     Mode="ValidationMode.Auto">
            <Card>
                <CardBody>

                    <Row>
                        <Column ColumnSize="ColumnSize.Is4" class="px-4 ">

                            <Div class="inline-input-label">
                                <FieldLabel>@L["No"]</FieldLabel>
                                <Div class="@FormWidthClass">
                                    <TextEdit @bind-Text="@FormCashierToCashierTransfer.No" Immediate="true"
                                              @onkeydown="@LoadModelByCashierToCashierTransferNo" Autofocus="true" TabIndex='1' />

                                </Div>

                            </Div>

                            <Div class="inline-input-label">
                                <FieldLabel>@L["TransferDateTime"]</FieldLabel>
                                <Div class="@FormWidthClass">

                                    <DateEdit Style="padding-block: 7px;" TValue="DateTime"
                                              InputMode="DateInputMode.DateTime"
                                              @bind-Date="@FormCashierToCashierTransfer.CreationTime" ReadOnly TabIndex='-1' />
                                </Div>
                            </Div>

                            <Div Style="margin-bottom: 20px;">
                                <FieldLabel>@L["ReciverCashier"]</FieldLabel>
                                <Div class="@FormWidthClass">
                                    <Select TValue="Guid?" @bind-SelectedValue="@FormCashierToCashierTransfer.ExecutedByUserId"
                                            class="text-center" Disabled="IsFormReadOnly()" TabIndex='4'>
                                        <SelectItem TValue="Guid?" Value="@null">

                                        </SelectItem>
                                        @foreach (var cashier in CashiersCollection)
                                        {
                                            <SelectItem TValue="Guid" Value="@cashier.Id">
                                                @cashier.Name
                                            </SelectItem>
                                        }
                                    </Select>

                                </Div>
                            </Div>

                        

                        </Column>

                        <Column ColumnSize="ColumnSize.Is4" class="px-4 ">

                            <Div class="inline-input-label ">
                                <FieldLabel>@L["Amount"]</FieldLabel>
                                <Div class="@FormWidthClass" Flex="Flex.AlignContent.Start">
                                    <Div class="col-10">
                                        <NumericPicker Value="@FormCashierToCashierTransfer.Amount" GroupSeparator=","
                                                       ReadOnly TabIndex='-1' />
                                    </Div>
                                    <Div>
                                        <Select TValue="Guid?" 
                                            SelectedValue="@FormCashierToCashierTransfer.CurrencyId"
                                            SelectedValueChanged="@(async (Guid? id)=>{ await OnChangeCurrency(id);})"
                                            Class="text-center px-3"
                                                Disabled="IsFormReadOnly()" TabIndex='4'>
                                            <SelectItem TValue="Guid?" Value="@null">

                                            </SelectItem>
                                            @foreach (var Currency in CurrenciesCollection)
                                            {
                                                <SelectItem TValue="Guid" Value="@Currency.Id">
                                                    @Currency.Code
                                                </SelectItem>
                                            }
                                        </Select>
                                    </Div>
                                </Div>


                            </Div>

                        </Column>

                        <Column ColumnSize="ColumnSize.Is4" class="px-4">


                            <Div class="inline-input-label">
                                <FieldLabel>@L["Notes"]</FieldLabel>
                                <Div class="@FormWidthClass">
                                    <MemoEdit @bind-Text="@FormCashierToCashierTransfer.Notes" Disabled="IsFormReadOnly()"
                                              TabIndex='10'>
                                    </MemoEdit>
                                </Div>

                            </Div>

                        </Column>

                    </Row>
                </CardBody>
            </Card>

            <Card class="mt-2">
                <CardBody>

 

                    <Row>
                        <CardFooter>
                            <Div Style="display: flex;justify-content: space-between; padding-top:30px;">
                                <Span Style="margin-right: auto;order: 1; width: 50%;color: red;margin-right:20px;">@LastStatusBy</Span>
                                <Span Style="margin-left: auto;order: 2; width: 50%;color: red;margin-right:20px;">@LastStatusDateTime</Span>
                            </Div>
                        </CardFooter>
                    </Row>



                </CardBody>



            </Card>

        </Validations>
    </Form>


</LoadingIndicator>
