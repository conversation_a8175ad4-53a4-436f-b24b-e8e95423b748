

// Ignore Spelling: Blazor

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Blazorise;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.JSInterop;
using Blazorise.LoadingIndicator;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.CompanyService.Currencies;
using EMPS.CompanyService.ServicePoints;
using EMPS.CompanyService.Staffs;
using System.Linq;
using EMPS.MoneyExchangeTransfersService.CashVouchers;
using EMPS.CompanyService.Application.Contracts.Dtos.Staffs;
using System.IO;
using System.Text;
using System.Net.Http;
using System.Text.Json;
using Volo.Abp;
using Microsoft.Extensions.Configuration;
using EMPS.CompanyService.CentralVaults;




namespace EMPS.MoneyExchangeTransfersService.Blazor.Components
{
    public partial class PaymentCashVoucherForm
    {

        [Inject]
        private IJSRuntime JSRuntime { get; set; }

        [Inject]
        private IPaymentCashVouchersAppService PaymentCashVouchersAppService { get; set; }
        [Inject]
        private ICurrenciesAppService CurrenciesAppService { get; set; }

        [Inject]
        private IServicePointsAppService ServicePointsAppService { get; set; }
        [Inject]
        private ICentralVaultsAppService CentralVaultsAppService { get; set; }

        [Inject]
        public IConfiguration configuration { get; set; }
        [Inject]
        public HttpClient httpClient { get; set; }
        [Parameter]
        public bool IsReadOnlyMode { get; set; }
        public SeatLocationType SeatLocation { get; set; }

        private bool CanDeletePaymentVoucher { get; set; }
        private bool CanCreateOrUpdatePaymentVoucher { get; set; }

        [Parameter]
        public CashVoucherDto FormPaymentVoucher { get; set; }

        private Validations FormPaymentVoucherValidations { get; set; } = new();

        private Guid EditingPaymentVoucherId { get; set; }

        [Parameter]
        public Func<CashVoucherDto, Task> OnPaymentVoucherLoaded { get; set; }

        public Func<Task<bool>> OnReset { get; set; }

        public bool IsPaymentVoucherLoadedProp { get; private set; }

        public LoadingIndicator loadingIndicator;

        private string LastStatusBy { get; set; }
        private string LastStatusDateTime { get; set; }
        private IReadOnlyList<CurrencyDto> CurrenciesCollection { get; set; } = new List<CurrencyDto>();
        private IReadOnlyList<ServicePointDto> ServicePointsCollection { get; set; } = new List<ServicePointDto>();
        private IReadOnlyList<CentralVaultDto> CentralVaultCollection { get; set; } = new List<CentralVaultDto>();

        public bool DisabledChangeCashierType { get; set; }

        public StaffSeatDto? StaffSeat { get; set; } = new();
        private IReadOnlyList<StaffDto> CashiersCollection { get; set; } = new List<StaffDto>();
        private string fileValidationError = string.Empty;

        private string ScannedImageBase64 = string.Empty;
        private Modal ScanDocumentModal;
        private string ScannedFileName { get; set; }

        private async Task GetCashiersLookupAsync(Guid servicePointId)
        {
            CashiersCollection = await PaymentCashVouchersAppService.GetAllCashierInServicePointAsync(servicePointId);
        }
        private async Task GetCurrenciesLookupAsync()
        {
            CurrenciesCollection = await CurrenciesAppService.GetActiveAsync(true);
            CurrenciesCollection.Where(x => x.CurrencyType == CurrencyType.Local).ToList();

        }
        private async Task GetServicePointsLookupAsync()
        {
            ServicePointsCollection = await ServicePointsAppService.GetAllActiveServicePointsLookupAsync();
        }
        private async Task GetCentralVaultsLookupAsync()
        {
            CentralVaultCollection = await CentralVaultsAppService.GetActiveVaults();
        }

        private void SetLastStatus()
        {

            LastStatusBy = "";
            LastStatusDateTime = "";
            if (FormPaymentVoucher == null) return;
            if (FormPaymentVoucher.IssueUserId != null && FormPaymentVoucher.IssueUserId != Guid.Empty)
            {
                LastStatusBy = L["CreatedByUserName"] + ": " + FormPaymentVoucher.IssueUserName;

                LastStatusDateTime = L["CreatedDate"] + ": " + FormPaymentVoucher.IssueDateTime;

            }
            if (FormPaymentVoucher.IsExecute)
            {
                LastStatusBy = L["ExecutedByUserName"] + ": " + FormPaymentVoucher.ExecutedByUserName;
                LastStatusDateTime = L["ExecuteDate"] + ": " + FormPaymentVoucher.ExecuteDateTime; ;
            }


            if (FormPaymentVoucher.IsExecute)
            {
                LastStatusBy = L["PayByUserName"] + ": " + FormPaymentVoucher.ExecutedByUserName;
                LastStatusDateTime = L["PayDate"] + ": " + FormPaymentVoucher.ExecuteDateTime; ;
            }



        }
        public PaymentCashVoucherForm()
        {
            FormPaymentVoucher = new();
        }
        protected override async Task OnInitializedAsync()
        {

            await SetPermissionsAsync();
            await GetCurrenciesLookupAsync();
            await GetServicePointsLookupAsync();
            StaffSeat = await GetStaff();

        }
        private async Task<StaffSeatDto?> GetStaff()
        {
            var StaffSeat = await PaymentCashVouchersAppService.GetStaffSeatAsync();
            if (StaffSeat != null)
            {
                if (StaffSeat.IsSeatForCentralVault)
                {

                    CentralVaultCollection= CentralVaultCollection.Where(x => x.Id == StaffSeat.AssignedLocationId).ToList();
                    await GetCashiersLookupAsync(CentralVaultCollection.First().Id);

                    DisabledChangeCashierType = true;
                    SeatLocation = SeatLocationType.CentralVault;
                    FormPaymentVoucher.SeatLocationId = StaffSeat.AssignedLocationId;

                }
                if (StaffSeat.IsSeatForServicePoint)
                {
                    DisabledChangeCashierType = true;
                    ServicePointsCollection = ServicePointsCollection.Where(x => x.Id == StaffSeat.AssignedLocationId).ToList();
                    await GetCashiersLookupAsync(ServicePointsCollection.First().Id);
                    SeatLocation = SeatLocationType.ServicePoint;
                    FormPaymentVoucher.SeatLocationId=StaffSeat.AssignedLocationId;
                }
                else
                {
                    SeatLocation = SeatLocationType.Company;
                    DisabledChangeCashierType = false;

                }

            }

            await ChangeCashierSeat(SeatLocation);
            return StaffSeat;
        }
        private async Task SetPermissionsAsync()
        {
            CanCreateOrUpdatePaymentVoucher = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.PaymentCashVouchers.Save);
            CanDeletePaymentVoucher = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.PaymentCashVouchers.Delete);

        }
        public async Task<bool> ResetPaymentVoucher(bool forceReset = false)
        {
            if (!forceReset)
                if (!await Message.Confirm(L["ResetConfirmationMessage"])) return false;
            FormPaymentVoucher = new();
            StaffSeat = await GetStaff();
            SetLastStatus();
            StateHasChanged();
            IsPaymentVoucherLoadedProp = false;
            return true;

        }
        public string? GetPaymentVoucherNo()
        {
            return FormPaymentVoucher.No;
        }
        public Guid GetPaymentVoucherId()
        {
            return FormPaymentVoucher.Id;
        }
        public bool IsPaymentVoucherLoaded()
        {
            return IsPaymentVoucherLoadedProp;

        }
        public bool IsFormReadOnly()
        {
            if (IsReadOnlyMode) return true;
            if (FormPaymentVoucher.IsExecute) return true;

            return false;
        }
        private async Task LoadPaymentVoucher()
        {

            var LoadResult = await GetLoadResult();
            if (LoadResult == null)
            {
                _ = Message.Error(L["PaymentVoucherNotFound"]);
                return;
            }

            FormPaymentVoucher = LoadResult;


            OnPaymentVoucherLoaded?.Invoke(LoadResult);
            IsPaymentVoucherLoadedProp = true;

            SetLastStatus();
            StateHasChanged();

        }
        private async Task<CashVoucherDto?> GetLoadResult()
        {
            return await PaymentCashVouchersAppService.LoadByNoAsync(FormPaymentVoucher.No!);
        }
        private async Task LoadModelByPaymentVoucherNo(KeyboardEventArgs e)
        {
            await Task.Delay(50);

            if (e.Key != "Enter") return;

            if (string.IsNullOrEmpty(FormPaymentVoucher.No)) return;


            await PerformLoadPaymentVoucher();

            await Task.CompletedTask;

        }
        public async Task PerformLoadPaymentVoucher()
        {

            try
            {
                await loadingIndicator.Show();

                await LoadPaymentVoucher();
                StateHasChanged();


            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await loadingIndicator.Hide();

            }
        }
        public void ChangeEntity(CashVoucherDto PaymentVoucher)
        {
            this.FormPaymentVoucher = PaymentVoucher;
            IsPaymentVoucherLoadedProp = true;
            SetLastStatus();
            StateHasChanged();
        }

        public async Task OnChangeCurrency(Guid? id)
        {
            FormPaymentVoucher.CurrencyId = id;
            if (FormPaymentVoucher.CurrencyId == null)
            {
                //rest all
            }
            //get avgCost for this currency 
            //method have 2 parameter currency id and cashier id 

            //avgCost = avgCost for currency
            StateHasChanged();

        }

        public async Task OnChangeServicePoint(Guid? id)
        {
            FormPaymentVoucher.SeatLocationId = id;
            if (FormPaymentVoucher.SeatLocationId == null)
            {
                CashiersCollection = new List<StaffDto>();
            }
            else
            {
                Console.WriteLine("spId :" + FormPaymentVoucher.SeatLocationId.ToString());
                await GetCashiersLookupAsync(FormPaymentVoucher.SeatLocationId.Value);
            }

            StateHasChanged();

        }
        public async Task OnChangeCentralVault(Guid? id)
        {
            FormPaymentVoucher.SeatLocationId = id;
            if (FormPaymentVoucher.SeatLocationId == null)
            {
                CashiersCollection = new List<StaffDto>();
            }
            else
            {
                await GetCashiersLookupAsync(FormPaymentVoucher.SeatLocationId!.Value);
            }

            StateHasChanged();

        }
        public async Task ChangeCashierSeat(SeatLocationType locationType)
        {
            // Update the seat location
            SeatLocation = locationType;

            // Clear/reset dependent fields when changing cashier type
            if (locationType == SeatLocationType.Company)
            {
                // Clear Service Point and Cashier selections when Company is selected
                FormPaymentVoucher.SeatLocationId = null;
                FormPaymentVoucher.ExecutedByStaffId = null;

                // Clear the collections to reset dropdowns
                CashiersCollection = new List<StaffDto>();
            }
            else if (locationType == SeatLocationType.ServicePoint)
            {
                // Clear only the cashier selection, keep service point available for selection
                FormPaymentVoucher.ExecutedByStaffId = null;
                CashiersCollection = new List<StaffDto>();

                // If a service point was previously selected, reload cashiers for that service point
                if (FormPaymentVoucher.SeatLocationId.HasValue)
                {
                    await GetCashiersLookupAsync(FormPaymentVoucher.SeatLocationId.Value);
                }
            }
            else if (locationType == SeatLocationType.CentralVault)
            {
                // Clear Service Point and Cashier selections for Central Vault
                FormPaymentVoucher.SeatLocationId = null;
                FormPaymentVoucher.ExecutedByStaffId = null;
                CashiersCollection = new List<StaffDto>();
            }

            // Trigger UI update
            StateHasChanged();
        }
        private async Task OnFileChanged(FileChangedEventArgs e)
        {
            fileValidationError = string.Empty;


            var file = e.Files.FirstOrDefault();
            if (file != null)
            {
                //if (file.Type != "application/pdf")
                //{
                //    fileValidationError = "Only PDF files are allowed";
                //    return;
                //}
                using (var stream = new MemoryStream())
                {
                    await file.WriteToStreamAsync(stream);
                    stream.Position = 0;
                    StreamReader StreamReader = new StreamReader(stream, Encoding.ASCII);
                    char[] ReadValue = new char[stream.Length];

                    FormPaymentVoucher.BlobContent = stream.ToArray();
                    FormPaymentVoucher.FileName = file.Name;
                    FormPaymentVoucher.FileSize = file.Size.ToString();
                    FormPaymentVoucher.FileExtension = Path.GetExtension(file.Name);
                    StreamReader.Close();
                    StateHasChanged();
                }
            }

        }

        private async Task DisplayFileInNewBrowserTab()
        {
            if (FormPaymentVoucher.FileExtension == null)
            {
                return;
            }
            string filetype = string.Empty;

            if (FormPaymentVoucher.FileExtension.ToUpper().Contains("PDF"))
            {
                filetype = "pdf";
            }
            else
            {
                filetype = "image";

            }
            await JSRuntime.InvokeAsync<object>("openInNewTab", FormPaymentVoucher.BlobContent, filetype, FormPaymentVoucher.FileExtension, FormPaymentVoucher.FileName);

        }
        void OnDeleteAttachmentClicked()
        {
            FormPaymentVoucher.BlobContent = null;
            FormPaymentVoucher.FileName = null;
            FormPaymentVoucher.FileSize = null;
            FormPaymentVoucher.FileExtension = null;
            StateHasChanged();
        }
        private async Task FetchAndDisplayImage()
        {
            try
            {
                await loadingIndicator.Show();

                var _baseUrl = configuration["MISAgent:BaseUrl"];
                Console.WriteLine("baseUrl: " + _baseUrl);
                //var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/Agent/ScanAsync");
                var request = new HttpRequestMessage(HttpMethod.Get, $"{"https://localhost:7000"}/Barcode/ScanAsync");

                using var response = await httpClient.SendAsync(request);
                Console.WriteLine($"response: {response}");

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Response Data: {responseData}"); // Log the raw response

                    var jsonResponse = JsonSerializer.Deserialize<JsonElement>(responseData);

                    // Check if the request was successful
                    if (jsonResponse.GetProperty("success").GetBoolean())
                    {
                        // Extract the base64 image data
                        var base64ImageData = jsonResponse.GetProperty("data").GetString();

                        if (!string.IsNullOrWhiteSpace(base64ImageData))
                        {
                            // Set the ScannedImageBase64 to the extracted base64 data
                            ScannedImageBase64 = base64ImageData;
                            StateHasChanged();
                            await ShowScanModal();
                            Console.WriteLine($"Base64 Image Data: {ScannedImageBase64.Substring(0, 50)}..."); // Log first 50 chars of the base64 data
                        }
                        else
                        {
                            Console.WriteLine("Base64 image data is empty.");
                        }
                    }
                    else
                    {
                        // Handle the case where the request was not successful
                        Console.WriteLine("Failed to get image data.");
                    }
                }
                else
                {
                    Console.WriteLine($"Error: {response.StatusCode}");
                }
                await loadingIndicator.Hide();
            }
            catch (Exception ex)
            {
                await loadingIndicator.Hide();
                return;
            }

        }
        private Task ShowScanModal()
        {
            ScannedFileName = string.Empty;
            return ScanDocumentModal.Show();
        }
        private Task CloseScanDocumentModal()
        {
            return ScanDocumentModal.Hide();
        }

        private async Task AcceptDocumentScanned()
        {
            if (ScannedFileName.IsNullOrWhiteSpace())
                throw new UserFriendlyException(L["ScannedFileNameIsRequired"]);
            FormPaymentVoucher.BlobContent = ConvertBase64ToByteArray(ScannedImageBase64);
            FormPaymentVoucher.FileSize = FormPaymentVoucher.BlobContent.Length.ToString();
            FormPaymentVoucher.FileExtension = "png";
            FormPaymentVoucher.FileName = $"{ScannedFileName}.png";
            await ScanDocumentModal.Hide();
            StateHasChanged();
        }

        private byte[] ConvertBase64ToByteArray(string base64String)
        {
            // Remove the data URL prefix if present
            if (base64String.Contains(","))
            {
                base64String = base64String.Substring(base64String.IndexOf(",") + 1);
            }

            return Convert.FromBase64String(base64String);
        }
    }
}
