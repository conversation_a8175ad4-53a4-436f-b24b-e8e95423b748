﻿using EMPS.CompanyService.Currencies;
using EMPS.CompanyService.ServicePoints;
using EMPS.CompanyService.ServicePointSeats;
using EMPS.CompanyService.Staffs;
using EMPS.MoneyExchangeTransfersService.Permissions;
using Microsoft.AspNetCore.Authorization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Identity;

namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
{
    [Authorize(MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Default)]

    public class ServicePointToCompanyTransferService : MoneyExchangeTransferBase, IServicePointToCompanyTransferService
    {
        public ServicePointToCompanyTransferService(IMoneyExchangeTransferCurrencyRepository moneyExchangeTransferCurrencyRepository, IStaffsAppService staffsAppService, IServicePointSeatsAppService servicePointSeatsAppService, IOrganizationUnitAppService organizationUnitAppService, IServicePointsAppService servicePointsAppService, ICurrenciesAppService currencyAppService, IIdentityUserAppService identityUserAppService) : base(moneyExchangeTransferCurrencyRepository, staffsAppService, servicePointSeatsAppService, organizationUnitAppService, servicePointsAppService, currencyAppService, identityUserAppService)
        {
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Approve)]
        public override Task<MoneyExchangeTransferCurrencyDto> ApproveByNoAsync(string no)
        {
            return base.ApproveByNoAsync(no);
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Delete)]
        public override Task DeletedByNoAsync(string no)
        {
            return base.DeletedByNoAsync(no);
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Execute)]
        public override Task<MoneyExchangeTransferCurrencyDto> ExecuteByNoAsync(string no)
        {
            return base.ExecuteByNoAsync(no);
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Save)]
        public override Task<MoneyExchangeTransferCurrencyDto> SaveAsync(MoneyExchangeTransferCurrencyCreateDto input)
        {
            return base.SaveAsync(input);
        }

        [Authorize(MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.ViewToExecute)]
        public async Task<PagedResultDto<MoneyExchangeTransferCurrencyDto>> GetReadyTransfersToExecuteForCompany()
        {

            var query = (await _moneyExchangeTransferCurrencyRepository.GetQueryableAsync()).Where(x => x.TransferType == MoneyExchangeTransferType.ServicePointToCompany && x.IsApproved && x.IsExecute == false);
            return new PagedResultDto<MoneyExchangeTransferCurrencyDto>
            {
                Items = ObjectMapper.Map<List<MoneyExchangeTransferCurrency>, List<MoneyExchangeTransferCurrencyDto>>(query.ToList()),
                TotalCount = query.Count(),
            };
        }
        protected override async Task<MoneyExchangeTransferCurrency?> CheckRequiredBusinessLogicInUpdatingMode(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency)
        {

            var Staff = await CheckStaffInformationAsync();
            var StaffSeat = await CheckSeatInformationAsync(Staff);
            if (moneyExchangeTransferCurrency.TransactionIssueSeatId != StaffSeat.Id) throw new UserFriendlyException(L["ThisCashierIsNotTheOneWhoCreateTheOrder"]);
            return moneyExchangeTransferCurrency;
        }

        protected override async Task<string?> GenerateNewNumberForMoneyExchangeTransfer()
        {
            return await _moneyExchangeTransferCurrencyRepository.GenerateNewNoForMoneyExchangeTransferCurrencyByTransferType(MoneyExchangeTransferType.ServicePointToCompany);
        }

        protected override async Task<MoneyExchangeTransferCurrency?> GetRequiredMoneyExchangeTransfer(string no)
        {
            var query = (await _moneyExchangeTransferCurrencyRepository.GetQueryableAsync())
                   .Where(x => x.No == no && x.TransferType == MoneyExchangeTransferType.ServicePointToCompany).OrderByDescending(x => x.CreationTime).FirstOrDefault();
            return query;
        }

        protected override async  Task<MoneyExchangeTransferCurrency> SetBusinessLogicFromInheritanceClassesForApproveAsync(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency)
        {
            var Staff = await CheckStaffInformationAsync();
            var StaffSeat = await CheckSeatInformationAsync(Staff);
            if (StaffSeat.ServicePointId != moneyExchangeTransferCurrency.ServicePointId) throw new UserFriendlyException(L["ThisOrderNotReleasedFromSameServicePoint"]);
            moneyExchangeTransferCurrency.FillApprovalInfo(GetFullUserName(), GeTUserId());
            return moneyExchangeTransferCurrency;
        }

        protected override async Task<MoneyExchangeTransferCurrency> SetBusinessLogicFromInheritanceClassesForExecuteAsync(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency)
        {
            //Check BalanceTraking if currency is LC (SYP)
            //Check Leger if currency is FC
            //Assert on BalanceTraking if currency is LC (SYP)
            //Assert on Leger if currency is FC
            moneyExchangeTransferCurrency.FillExecutionInfo(GetFullUserName(), GeTUserId());
            return moneyExchangeTransferCurrency;
        }

        protected override async  Task<MoneyExchangeTransferCurrency> SetBusinessLogicFromInheritanceClassesForSaveAsync(MoneyExchangeTransferCurrency moneyExchangeTransferCurrency, MoneyExchangeTransferCurrencyCreateDto moneyExchange)
        {
            var Staff = await CheckStaffInformationAsync();
            var StaffSeat = await CheckSeatInformationAsync(Staff);
            if (StaffSeat.ServicePointId != moneyExchangeTransferCurrency.ServicePointId) throw new UserFriendlyException(L["ThisOrderNotReleasedFromSameServicePoint"]);
            moneyExchange.TransferType = MoneyExchangeTransferType.ServicePointToCompany;
            moneyExchangeTransferCurrency.FillTransactionIssueInfo(GetFullUserName(), GeTUserId(), $"{Staff.Staff.Name} {Staff.Staff.Surname}", Staff.Staff.Id, StaffSeat.Id);
            return moneyExchangeTransferCurrency;
        }

    }
}
