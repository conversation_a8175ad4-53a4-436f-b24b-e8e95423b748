﻿using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Http.Client;
using Volo.Abp.Modularity;
using Volo.Abp.VirtualFileSystem;

namespace EMPS.MoneyExchangeTransfersService;

[DependsOn(
    typeof(MoneyExchangeTransfersServiceApplicationContractsModule),
    typeof(AbpHttpClientModule))]
public class MoneyExchangeTransfersServiceHttpApiClientModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddHttpClientProxies(typeof(MoneyExchangeTransfersServiceApplicationContractsModule).Assembly,
            MoneyExchangeTransfersServiceRemoteServiceConsts.RemoteServiceName);

        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<MoneyExchangeTransfersServiceHttpApiClientModule>();
        });
    }
}
