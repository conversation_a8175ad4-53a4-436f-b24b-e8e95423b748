// This file is automatically generated by ABP framework to use MVC Controllers from CSharp
using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Http.Client;
using Volo.Abp.Http.Modeling;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Http.Client.ClientProxying;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using Volo.Abp.Content;
using EMPS.MoneyExchangeTransfersService.Shared;

// ReSharper disable once CheckNamespace
namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.ClientProxies;

[Dependency(ReplaceServices = true)]
[ExposeServices(typeof(IMoneyExchangeTransferCurrenciesAppService), typeof(MoneyExchangeTransferCurrenciesClientProxy))]
public partial class MoneyExchangeTransferCurrenciesClientProxy : ClientProxyBase<IMoneyExchangeTransferCurrenciesAppService>, IMoneyExchangeTransferCurrenciesAppService
{
    public virtual async Task<PagedResultDto<MoneyExchangeTransferCurrencyDto>> GetListAsync(GetMoneyExchangeTransferCurrenciesInput input)
    {
        return await RequestAsync<PagedResultDto<MoneyExchangeTransferCurrencyDto>>(nameof(GetListAsync), new ClientProxyRequestTypeValue
        {
            { typeof(GetMoneyExchangeTransferCurrenciesInput), input }
        });
    }

    public virtual async Task<MoneyExchangeTransferCurrencyDto> GetAsync(Guid id)
    {
        return await RequestAsync<MoneyExchangeTransferCurrencyDto>(nameof(GetAsync), new ClientProxyRequestTypeValue
        {
            { typeof(Guid), id }
        });
    }

    public virtual async Task DeleteAsync(Guid id)
    {
        await RequestAsync(nameof(DeleteAsync), new ClientProxyRequestTypeValue
        {
            { typeof(Guid), id }
        });
    }

    public virtual async Task<MoneyExchangeTransferCurrencyDto> CreateAsync(MoneyExchangeTransferCurrencyCreateDto input)
    {
        return await RequestAsync<MoneyExchangeTransferCurrencyDto>(nameof(CreateAsync), new ClientProxyRequestTypeValue
        {
            { typeof(MoneyExchangeTransferCurrencyCreateDto), input }
        });
    }

    public virtual async Task<MoneyExchangeTransferCurrencyDto> UpdateAsync(Guid id, MoneyExchangeTransferCurrencyUpdateDto input)
    {
        return await RequestAsync<MoneyExchangeTransferCurrencyDto>(nameof(UpdateAsync), new ClientProxyRequestTypeValue
        {
            { typeof(Guid), id },
            { typeof(MoneyExchangeTransferCurrencyUpdateDto), input }
        });
    }
}