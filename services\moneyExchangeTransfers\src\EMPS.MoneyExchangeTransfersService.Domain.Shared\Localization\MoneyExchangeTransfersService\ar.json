{
  "Culture": "ar",
  "Texts": {
    "Enum:MoneyExchangeTransferStatus.1": "معلق",
    "Enum:MoneyExchangeTransferStatus.2": "مقبول",
    "Enum:MoneyExchangeTransferStatus.3": "ملغي",
    "Enum:MoneyExchangeTransferType.1": "من صراف إلى صراف",
    "Enum:MoneyExchangeTransferType.2": "من الشركة إلى نقطة الخدمة",
    "Enum:MoneyExchangeTransferType.3": "من نقطة الخدمة إلى الشركة",
    "Enum:SeatLocationType.1": "الشركة",
    "Enum:SeatLocationType.2": "نقطة الخدمة",
    "Enum:SeatLocationType.3": "الخزينة المركزية",
    "Permission:MoneyExchangeTransferCurrencies": "عملات تحويل الصرافة",
    "Permission:MoneyExchangeTransfersService": "خدمة تحويلات الصرافة",
    "Permission:Create": "إنشاء",
    "Permission:Edit": "تعديل",
    "Permission:Delete": "حذف",
    "No": "الرقم",
    "Amount": "المبلغ",
    "CurrencyCode": "رمز العملة",
    "CurrencyId": "معرف العملة",
    "ServicePointName": "اسم نقطة الخدمة",
    "ServicePointId": "معرف نقطة الخدمة",
    "TransactionIssueUserName": "اسم مستخدم إصدار المعاملة",
    "TransactionIssueUserId": "معرف مستخدم إصدار المعاملة",
    "TransactionIssueStaffName": "اسم موظف إصدار المعاملة",
    "TransactionIssueStaffId": "معرف موظف إصدار المعاملة",
    "TransactionIssueSeatId": "معرف مقعد إصدار المعاملة",
    "IsApproved": "مصدق",
    "ApprovedByUserName": "مصدق من قبل المستخدم",
    "ApprovedByUserId": "معرف المستخدم المصدق",
    "ApprovalDate": "تاريخ التصديق",
    "ApprovalDateTime": "تاريخ ووقت التصديق",
    "Notes": "ملاحظات",
    "IsExecute": "منفذ",
    "ExecutedByUserName": "منفذ من قبل المستخدم",
    "ExecutedByUserId": "معرف المستخدم المنفذ",
    "ExecutedByStaffName": "منفذ من قبل الموظف",
    "ExecutedByStaffId": "معرف الموظف المنفذ",
    "ExecutedBySeatId": "معرف مقعد التنفيذ",
    "ExecuteDate": "تاريخ التنفيذ",
    "ExecuteDateTime": "تاريخ ووقت التنفيذ",
    "TransferStatus": "حالة التحويل",
    "TransferType": "نوع التحويل",
    "MinAmount": "الحد الأدنى للمبلغ",
    "MinApprovalDate": "الحد الأدنى لتاريخ التصديق",
    "MinApprovalDateTime": "الحد الأدنى لتاريخ ووقت التصديق",
    "MinExecuteDate": "الحد الأدنى لتاريخ التنفيذ",
    "MinExecuteDateTime": "الحد الأدنى لتاريخ ووقت التنفيذ",
    "MaxAmount": "الحد الأقصى للمبلغ",
    "MaxApprovalDate": "الحد الأقصى لتاريخ التصديق",
    "MaxApprovalDateTime": "الحد الأقصى لتاريخ ووقت التصديق",
    "MaxExecuteDate": "الحد الأقصى لتاريخ التنفيذ",
    "MaxExecuteDateTime": "الحد الأقصى لتاريخ ووقت التنفيذ",
    "MoneyExchangeTransferCurrencies": "عملات تحويل الصرافة",
    "NewMoneyExchangeTransferCurrency": "عملة تحويل صرافة جديدة",
    "Actions": "الإجراءات",
    "SuccessfullyDeleted": "تم الحذف بنجاح",
    "DeleteConfirmationMessage": "هل أنت متأكد من أنك تريد حذف هذا السجل؟",
    "Search": "بحث",
    "Pick": "اختيار",
    "SeeAdvancedFilters": "مرشحات متقدمة",
    "ItemAlreadyAdded": "هذا العنصر مضاف بالفعل.",
    "ExportToExcel": "تصدير إلى Excel",
    "Menu:MoneyExchangeTransferCurrencies": "عملات تحويل الصرافة",
    "Menu:MoneyExchangeTransfersService": "خدمة تحويلات الصرافة",
    "ServicePointToCompanyTransferIssue": "إصدار تحويل من نقطة الخدمة إلى الشركة",
    "ServicePointToCompanyTransferExecute": "تنفيذ تحويل من نقطة الخدمة إلى الشركة",
    "ServicePointToCompanyTransferApprove": "تصديق تحويل من نقطة الخدمة إلى الشركة",
    "Menu:ServicePointToCompanyTransferIssue": "إصدار تحويل من نقطة الخدمة إلى الشركة",
    "Menu:ServicePointToCompanyTransferExecute": "تنفيذ تحويل من نقطة الخدمة إلى الشركة",
    "Menu:ServicePointToCompanyTransferApprove": "تصديق تحويل من نقطة الخدمة إلى الشركة",
    "NewServicePointToCompanyTransfer": "تحويل جديد من نقطة الخدمة إلى الشركة",
    "ExecuteServicePointToCompanyTransfer": "تنفيذ تحويل من نقطة الخدمة إلى الشركة",
    "ApproveServicePointToCompanyTransfer": "تصديق تحويل من نقطة الخدمة إلى الشركة",
    "ServicePointToCompanyNotFound": "تحويل من نقطة الخدمة إلى الشركة غير موجود",
    "Permission:ServicePointToCompanyTransfer": "تحويل من نقطة الخدمة إلى الشركة",
    "Permission:Save": "حفظ",
    "Permission:Execute": "تنفيذ",
    "Permission:Approve": "تصديق",
    "Permission:ViewToExecute": "عرض للتنفيذ",
    "ResetConfirmationMessage": "هل أنت متأكد من أنك تريد إعادة تعيين هذا النموذج؟",
    "ExecuteConfirmationMessage": "هل أنت متأكد من أنك تريد تنفيذ هذا التحويل؟",
    "ApproveConfirmationMessage": "هل أنت متأكد من أنك تريد تصديق هذا التحويل؟",
    "ApprovedByUser": "مصدق من قبل المستخدم",
    "DateTime": "التاريخ والوقت",
    "ServicePoints": "نقاط الخدمة",
    "Error:ThisNoIsNotExist": "خطأ: هذا الرقم غير موجود",
    "Error:CannotMakeAnyOpration": "خطأ: لا يمكن تنفيذ أي عملية",
    "Error:MoneyExchangeTransferIsExecuteAlready": "خطأ: تحويل الصرافة منفذ بالفعل",
    "Error:MoneyExchangeTransferIsApprovedAlready": "خطأ: تحويل الصرافة مصدق بالفعل",
    "TheAmountMustBeGreaterThanZero": "يجب أن يكون المبلغ أكبر من الصفر",
    "CurrencyMustBeSelected": "يجب اختيار العملة",
    "ServicePointIsNotExist": "نقطة الخدمة غير موجودة",
    "thisServicePointNotActive": "نقطة الخدمة هذه غير نشطة",
    "thisIsNotUser": "هذا ليس مستخدم",
    "CompanyToServicePointNotFound": "تحويل من الشركة إلى نقطة الخدمة غير موجود",
    "CashierToCashierTransferNotFound": "تحويل من صراف إلى صراف غير موجود",
    "CreatedByUserName": "أنشئ من قبل المستخدم",
    "CreatedDate": "تاريخ الإنشاء",
    "TransactionDate": "تاريخ المعاملة",
    "Menu:CashierToCashierTransfer": "تحويل من صراف إلى صراف",
    "Menu:ExcuteCashierToCashierTransfer": "تنفيذ تحويل من صراف إلى صراف",
    "Menu:CompanyToServicePointTransfer": "تحويل من الشركة إلى نقطة الخدمة",
    "Menu:ApproveCompanyToServicePointTransfer": "تصديق تحويل من الشركة إلى نقطة الخدمة",
    "Menu:ExcuteCompanyToServicePointTransfer": "تنفيذ تحويل من الشركة إلى نقطة الخدمة",
    "Menu:ServicePointToCompanyTransfer": "تحويل من نقطة الخدمة إلى الشركة",
    "Menu:ApproveServicePointToCompanyTransfer": "تصديق تحويل من نقطة الخدمة إلى الشركة",
    "Menu:ExcuteServicePointToCompanyTransfer": "تنفيذ تحويل من نقطة الخدمة إلى الشركة",
    "CashierToCashierTransferIssue": "إصدار تحويل من صراف إلى صراف",
    "CompanyToServicePointTransferIssue": "إصدار تحويل من الشركة إلى نقطة الخدمة",
    "CompanyToServicePointTransferExecute": "تنفيذ تحويل من الشركة إلى نقطة الخدمة",
    "CompanyToServicePointTransferApprove": "تصديق تحويل من الشركة إلى نقطة الخدمة",
    "Menu:CompanyToServicePointTransferIssue": "إصدار تحويل من الشركة إلى نقطة الخدمة",
    "Menu:CompanyToServicePointTransferExecute": "تنفيذ تحويل من الشركة إلى نقطة الخدمة",
    "Menu:CompanyToServicePointTransferApprove": "تصديق تحويل من الشركة إلى نقطة الخدمة",
    "ExecuteCashierToCashierTransferConfirmationMessage": "هل أنت متأكد من أنك تريد تنفيذ تحويل الصراف إلى الصراف رقم: ",
    "ExecuteCompanyToServicePointTransferConfirmationMessage": "هل أنت متأكد من أنك تريد تنفيذ تحويل الشركة إلى نقطة الخدمة رقم: ",
    "ApproveCompanyToServicePointTransferConfirmationMessage": "هل أنت متأكد من أنك تريد تصديق تحويل الشركة إلى نقطة الخدمة رقم: ",
    "ExecutedSuccessfully": "تم التنفيذ بنجاح",
    "ApprovedSuccessfully": "تم التصديق بنجاح",
    "SaveSuccess": "تم الحفظ بنجاح",
    "Error:ThisServicePointToCompanyTransferCannotBeUpdated": "خطأ: لا يمكن تحديث هذا التحويل من نقطة الخدمة إلى الشركة",
    "Error:LoadServicePointToCompanyTransferFirst": "خطأ: يرجى تحميل تحويل نقطة الخدمة إلى الشركة أولاً",
    "Error:ThisServicePointToCompanyTransferCannotBeDeleted": "خطأ: لا يمكن حذف هذا التحويل من نقطة الخدمة إلى الشركة",
    "Permission:CompanyToServicePointTransfer": "تحويل من الشركة إلى نقطة الخدمة",
    "Permission:CashierToCashierTransfer": "تحويل من صراف إلى صراف",
    "TheReciverStaffIsNotTheUserThatAddedInIssueToExecute": "الموظف الذي يقوم بالتنفيذ ليس الموظف المحدد",
    "CashierType": "نوع الصراف",
    "Cashier": "الصراف",
    "PaymentVoucherNotFound": "سند الدفع غير موجود",
    "PayByUserName": "دفع من قبل المستخدم",
    "PayDate": "تاريخ الدفع",
    "UploadAttachmentFile": "رفع ملف مرفق",
    "ScanneFile": "مسح الملف",
    "ScannedFileName": "اسم الملف الممسوح",
    "ScannedFileNameIsRequired": "اسم الملف الممسوح مطلوب",
    "Close": "إغلاق",
    "Accept": "قبول",
    "ThisOperationIsCreatedFromStaffCompany": "هذه العملية تم إنشاؤها من قبل موظف الشركة",
    "Error:ThisOperationIsCreatedFromAnotherServicePointForThisStaff": "خطأ: هذه العملية تم إنشاؤها من نقطة خدمة أخرى لهذا الموظف",
    "Error:ThisOperationIsCreatedFromAnotherCentralVaultForThisStaff": "خطأ: هذه العملية تم إنشاؤها من خزنة مركزية أخرى لهذا الموظف",
    "Error:SomeDataInDatabaseEncrypted": "خطأ: بعض البيانات في قاعدة البيانات مشفرة",
    "Error:CurrencyTypeMustBeLocal": "خطأ: نوع العملة يجب أن يكون محلياً",
    "Error:TheSelectedCashierIsNotSraff": "خطأ: أمين الصندوق المحدد ليس موظفاً",
    "Error:ThisStaffIsNotTheSameCashier": "خطأ: هذا الموظف ليس هو أمين الصندوق نفسه",
    "Error:TheSelectedCashierIsNotSraffInThisCentralVault": "خطأ: أمين الصندوق المحدد ليس موظفاً في هذه الخزنة المركزية",
    "Error:TheSelectedCashierIsNotSraffInThisServicePoint": "خطأ: أمين الصندوق المحدد ليس موظفاً في نقطة الخدمة هذه",
    "ThisSeatNoCashier": "هذا المقعد لا يوجد به أمين صندوق",
    "Error:CashVoucherIsApprovedAlready": "خطأ: سند الصرف تمت الموافقة عليه بالفعل",
    "Error:CashVoucherIsExecuteCannotBeApproved": "خطأ: لا يمكن الموافقة على سند الصرف المنفذ",
    "Error:CashVoucherIsExecuteAlready": "خطأ: سند الصرف تم تنفيذه بالفعل",
    "Error:CashVoucherIsNotApprovedCannotBeExecute": "خطأ: لا يمكن تنفيذ سند الصرف غير المعتمد",
    "Error:CashVoucherIsApprovedCannotBeDeleted": "خطأ: لا يمكن حذف سند الصرف المعتمد",
    "Error:CashVoucherIsExecuteCannotBeDeleted": "خطأ: لا يمكن حذف سند الصرف المنفذ",

    "Error:StaffIsNotActive": "خطأ: الموظف غير مفعل",
    "Error:CentralVaultIsNotExist": "خطأ: الخزنة المركزية غير موجودة",
    "Error:CentralVaultIsNotActive": "خطأ: الخزنة المركزية غير مفعلة",
    "Error:ServicePointIsNotExist": "خطأ: نقطة الخدمة غير موجودة",
    "Error:ServicePointIsNotActive": "خطأ: نقطة الخدمة غير مفعلة",
    "thisUserIsNotActive": "هذا المستخدم غير مفعل",
    "ThisCurrencyIsNotActive": "هذه العملة غير مفعلة",
    "CurrencyNotFound": "العملة غير موجودة",
    "SaveCashierToCashierTransfer": "حفظ",
    "NewCashierToCashierTransfer": "تحويل جديد",
    "TheReciverStaffMustBeSelected": "يجب اختيار الكاشير المستلم",
    "ExecuteCashierToCashierTransfer": "تنفيذ",
    "Delete": "حذف",
    "ReciverCashier": "المستلم",
    "TransferDateTime": "تاريخ النقل",
    "AvaregeCost": "التكلفة",
    "ThisCashierIsNotTheOneWhoCreateTheOrder": "المنشئ كاشير اخر",
    "SaveCompanyToServicePointTransfer": "حفظ",
    "NewCompanyToServicePointTransfer": "تحويل جديد",
    "ApproveCompanyToServicePointTransfer": "تصديق",
    "ExecuteCompanyToServicePointTransfer": "تنفيذ",
    "Save": "حفظ",
    "Error:CentralValutDontAccessTOExecute": "",
    "Error:ThisUserIsNotStaff": "",
    "Error:ThisStaffIsNotInSeat": "",
    "Error:ThisStaffIsNotSameSelectedCashier": "",
    "BalanceForCompanyIsLow": "",
    "FinancialAccount": "",
    "FinancialPeriod": "",
    "CostCenter": "",
    "CashierSeatLocation": "",
    "CentralVault": "",
    "FileName": "",
    "FileSize": "",
    "FileExtension": "",
    "Scanned Document": "",
    "ApproveByUserName": "",
    "ApproveDate": "",
    "CurrenciesCollectionHaveNoLocalCurrency": "",
    "ReceiptByUserName": "",
    "ReceiptDate": "",
    "ReceiptVoucherNotFound": "",
    "Menu:CashierToCashierTransferExecute": "",
    "Error:LoadCashierToCashierTransferFirst": "",
    "Error:ThisCashierToCashierTransferCannotBeExecuted": "",
    "CashierToCashierTransferExecute": "",
    "Menu:CashierToCashierTransferIssue": "",
    "AmountMustBeLargeThanZero": "",
    "CurrencyFeildMustBeSelected": "",
    "ReciverCashierFeildMustBeSelected": "",
    "Error:ThisCashierToCashierTransferCannotBeUpdated": "",
    "Error:ThisCashierToCashierTransferCannotBeDeleted": "",
    "Error:LoadCompanyToServicePointTransferFirst": "",
    "Error:ThisCompanyToServicePointTransferCannotBeApproved": "",
    "Error:ThisCompanyToServicePointTransferCannotBeExecuted": "",
    "Error:ThisCompanyToServicePointTransferCannotBeUpdated": "",
    "Error:ThisCompanyToServicePointTransferCannotBeDeleted": "",
    "PaymentCashVoucherApprove": "",
    "Menu:PaymentVoucherIssue": "",
    "NewPaymentVoucher": "",
    "Approve": "",
    "Error:LoadPaymentVoucherFirst": "",
    "Error:ThisPaymentVoucherCannotBeApproved": "",
    "ApprovePaymentVoucherConfirmationMessage": "",
    "ApproveSuccess": "",
    "PaymentCashVoucherIssue": "",
    "Menu:PaymentCashVoucherApprove": "",
    "CashierSeatLocationMustBeSelected": "",
    "ServicePointMustBeSelected": "",
    "CentralVaultMustBeSelected": "",
    "SavePaymentVoucher": "",
    "Error:ThisPaymentVoucherCannotBeUpdated": "",
    "Error:ThisPaymentVoucherCannotBeDeleted": "",
    "PaymentCashVoucherPay": "",
    "Menu:PaymentCashVoucherPay": "",
    "Pay": "",
    "Error:ThisPaymentVoucherCannotBePayd": "",
    "PayPaymentVoucherConfirmationMessage": "",
    "PaySuccess": "",
    "ReceiptCashVoucherApprove": "",
    "Menu:ReceiptCashVoucherApprove": "",

    "NewReceiptVoucher": "",
    "Error:LoadReceiptVoucherFirst": "",
    "Error:ThisReceiptVoucherCannotBeApproved": "",
    "ApproveReceiptVoucherConfirmationMessage": "",
    "ReceiptCashVoucherExecute": "",
    "Menu:ReceiptCashVoucherExecute": "",
    "Receipt": "",
    "Error:ThisReceiptVoucherCannotBeReceiptd": "",
    "ReceiptReceiptVoucherConfirmationMessage": "",
    "ReceiptSuccess": "",
    "ReceiptCashVoucherIssue": "",
    "Menu:ReceiptVoucherIssue": "",
    "SaveReceiptVoucher": "",
    "Error:ThisReceiptVoucherCannotBeUpdated": "",
    "Error:ThisReceiptVoucherCannotBeDeleted": "",
    "Menu:PaymentCashVouchers": "",
    "Menu:IssuePaymentCashVouchers": "",
    "Menu:ApprovePaymentCashVouchers": "",
    "Menu:PayPaymentCashVouchers": "",
    "Menu:ReceiptCashVouchers": "",
    "Menu:IssueReceiptCashVouchers": "",
    "Menu:ApproveReceiptCashVouchers": "",
  }


  }