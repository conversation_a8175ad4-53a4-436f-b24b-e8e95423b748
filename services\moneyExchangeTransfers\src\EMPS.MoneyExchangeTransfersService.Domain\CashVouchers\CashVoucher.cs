using EMPS.Shared.Enum;
using EMPS.MoneyExchangeTransfersService.CashVouchers;
using System;
using System.Linq;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;
using JetBrains.Annotations;

using Volo.Abp;

namespace EMPS.MoneyExchangeTransfersService.CashVouchers
{
    public class CashVoucher : FullAuditedAggregateRoot<Guid>
    {
        [CanBeNull]
        public virtual string? No { get; set; }

        public virtual double Amount { get; set; }

        [CanBeNull]
        public virtual string? CurrencyCode { get; set; }

        public virtual Guid? CurrencyId { get; set; }

        [CanBeNull]
        public virtual string? SeatLocationName { get; set; }

        public virtual Guid? SeatLocationId { get; set; }
        [CanBeNull]
        public virtual string? CashierName { get; set; }

        public virtual Guid? CashierId { get; set; }

        [CanBeNull]
        public virtual string? IssueUserName { get; set; }

        public virtual Guid? IssueUserId { get; set; }

        [CanBeNull]
        public virtual string? IssueStaffName { get; set; }

        public virtual Guid? IssueStaffId { get; set; }

        public virtual Guid? IssueSeatId { get; set; }
        public virtual DateTime? IssueDateTime { get; set; }


        public virtual bool IsApproved { get; set; }

        [CanBeNull]
        public virtual string? ApprovedByUserName { get; set; }

        public virtual Guid? ApprovedByUserId { get; set; }

        public virtual DateTime? ApprovalDate { get; set; }

        public virtual DateTime? ApprovalDateTime { get; set; }

        [CanBeNull]
        public virtual string? Notes { get; set; }

        public virtual bool IsExecute { get; set; }

        [CanBeNull]
        public virtual string? ExecutedByUserName { get; set; }

        public virtual Guid? ExecutedByUserId { get; set; }

        [CanBeNull]
        public virtual string? ExecutedByStaffName { get; set; }

        public virtual Guid? ExecutedByStaffId { get; set; }

        public virtual Guid? ExecutedBySeatId { get; set; }

        public virtual DateTime? ExecuteDate { get; set; }

        public virtual DateTime? ExecuteDateTime { get; set; }

        [CanBeNull]
        public virtual string? ApproveByStaffName { get; set; }

        public virtual Guid ApproveByStaffId { get; set; }

        public virtual Guid ApproveBySeatId { get; set; }

        public virtual AccountingMode AccountingType { get; set; }

        [CanBeNull]
        public virtual string? BolbKey { get; set; }

        [CanBeNull]
        public virtual string? FileName { get; set; }

        [CanBeNull]
        public virtual string? FileExtension { get; set; }

        [CanBeNull]
        public virtual string? FileSize { get; set; }

        public virtual Guid FinancialPeriodId { get; set; }

        [CanBeNull]
        public virtual string? FinancialPeriodName { get; set; }

        public virtual Guid FinancialAccountId { get; set; }

        [CanBeNull]
        public virtual string? FinancialAccountName { get; set; }

        public virtual SeatLocationType AssignSeatLocation { get; set; }

        public virtual Guid CostCenterId { get; set; }

        [CanBeNull]
        public virtual string? CostCenterName { get; set; }

        public CashVoucher()
        {

        }

        public CashVoucher(Guid id, string no, double amount, string currencyCode, string seatLocationName, string issueUserName, string issueStaffName, bool isApproved, string approvedByUserName, string notes, bool isExecute, string executedByUserName, string executedByStaffName, string approveByStaffName, Guid approveByStaffId, Guid approveBySeatId, AccountingMode accountingType, string bolbKey, string fileName, string fileExtension, string fileSize, Guid financialPeriodId, string financialPeriodName, Guid financialAccountId, string financialAccountName, SeatLocationType assignSeatLocation, Guid costCenterId, string costCenterName, Guid? currencyId = null, Guid? seatLocationId = null, Guid? issueUserId = null, Guid? issueStaffId = null, Guid? issueSeatId = null, Guid? approvedByUserId = null, DateTime? approvalDate = null, DateTime? approvalDateTime = null, Guid? executedByUserId = null, Guid? executedByStaffId = null, Guid? executedBySeatId = null, DateTime? executeDate = null, DateTime? executeDateTime = null)
        {

            Id = id;
            No = no;
            Amount = amount;
            CurrencyCode = currencyCode;
            SeatLocationName = seatLocationName;
            IssueUserName = issueUserName;
            IssueStaffName = issueStaffName;
            IsApproved = isApproved;
            ApprovedByUserName = approvedByUserName;
            Notes = notes;
            IsExecute = isExecute;
            ExecutedByUserName = executedByUserName;
            ExecutedByStaffName = executedByStaffName;
            ApproveByStaffName = approveByStaffName;
            ApproveByStaffId = approveByStaffId;
            ApproveBySeatId = approveBySeatId;
            AccountingType = accountingType;
            BolbKey = bolbKey;
            FileName = fileName;
            FileExtension = fileExtension;
            FileSize = fileSize;
            FinancialPeriodId = financialPeriodId;
            FinancialPeriodName = financialPeriodName;
            FinancialAccountId = financialAccountId;
            FinancialAccountName = financialAccountName;
            AssignSeatLocation = assignSeatLocation;
            CostCenterId = costCenterId;
            CostCenterName = costCenterName;
            CurrencyId = currencyId;
            SeatLocationId = seatLocationId;
            IssueUserId = issueUserId;
            IssueStaffId = issueStaffId;
            IssueSeatId = issueSeatId;
            ApprovedByUserId = approvedByUserId;
            ApprovalDate = approvalDate;
            ApprovalDateTime = approvalDateTime;
            ExecutedByUserId = executedByUserId;
            ExecutedByStaffId = executedByStaffId;
            ExecutedBySeatId = executedBySeatId;
            ExecuteDate = executeDate;
            ExecuteDateTime = executeDateTime;
        }

        public void FillCashierInfo(string? cashierName, Guid? cashierId)
        {
            CashierName = cashierName;
            CashierId = cashierId;
        }

        public void FillIssueInfo(
        string issueUserName,
        Guid? issueUserId = null,
        string? issueStaffName = null,
        Guid? issueStaffId = null,
        Guid? issueSeatId = null)
        {
            IssueUserName = issueUserName;
            IssueUserId = issueUserId;
            IssueStaffName = issueStaffName;
            IssueStaffId = issueStaffId;
            IssueSeatId = issueSeatId;
            IssueDateTime = DateTime.Now;
        }

        public void FillApprovalInfo(
            string? approvedByUserName,
            Guid? approvedByUserId = null,
            string? approveByStaffName = null,
            Guid? approveByStaffId = null,
            Guid? approveBySeatId = null)
        {
            ApprovedByUserName = approvedByUserName;
            ApprovedByUserId = approvedByUserId;
            ApproveByStaffName = approveByStaffName;
            ApproveByStaffId = approveByStaffId ?? Guid.Empty;
            ApproveBySeatId = approveBySeatId ?? Guid.Empty;
            ApprovalDate = DateTime.Today.Date;
            ApprovalDateTime = DateTime.Now;
            IsApproved = true;
        }

        public void FillExecutionInfo(
            string? executedByUserName,
            Guid? executedByUserId = null,
            string? executedByStaffName = null,
            Guid? executedByStaffId = null,
            Guid? executedBySeatId = null)
        {
            ExecutedByUserName = executedByUserName;
            ExecutedByUserId = executedByUserId;
            ExecutedByStaffName = executedByStaffName;
            ExecutedByStaffId = executedByStaffId;
            ExecutedBySeatId = executedBySeatId;
            ExecuteDate = DateTime.Today.Date;
            ExecuteDateTime = DateTime.Now;
            IsExecute = true;
        }

        public void FillCurrencyInfo(string currencyCode, Guid? currencyId = null)
        {
            CurrencyCode = currencyCode;
            CurrencyId = currencyId;
        }

        public void FillSeatLocationInfo(string? seatLocationName, Guid? seatLocationId = null)
        {
            SeatLocationName = seatLocationName;
            SeatLocationId = seatLocationId;
        }

        public void FillFileInfo( string? fileName, string? fileExtension, string? fileSize)
        {
            FileName = fileName;
            FileExtension = fileExtension;
            FileSize = fileSize;
        }

        public void FillFinancialInfo(
            Guid financialPeriodId,
            string? financialPeriodName,
            Guid financialAccountId,
            string? financialAccountName,
            Guid costCenterId,
            string? costCenterName)
        {
            FinancialPeriodId = financialPeriodId;
            FinancialPeriodName = financialPeriodName;
            FinancialAccountId = financialAccountId;
            FinancialAccountName = financialAccountName;
            CostCenterId = costCenterId;
            CostCenterName = costCenterName;
        }



    }
}