{"modules": {"moneyExchangeTransfersService": {"rootPath": "moneyExchangeTransfersService", "remoteServiceName": "<PERSON><PERSON><PERSON>", "controllers": {"EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrenciesAppService": {"controllerName": "MoneyExchangeTransferCurrencies", "controllerGroupName": "MoneyExchangeTransferCurrencies", "isRemoteService": true, "apiVersion": null, "type": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrenciesAppService", "interfaces": [{"type": "Volo.Abp.Auditing.IAuditingEnabled"}, {"type": "Volo.Abp.GlobalFeatures.IGlobalFeatureCheckingEnabled"}, {"type": "Volo.Abp.Validation.IValidationEnabled"}, {"type": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.IMoneyExchangeTransferCurrenciesAppService"}], "actions": {"GetListAsyncByInput": {"uniqueName": "GetListAsyncByInput", "name": "GetListAsync", "allowAnonymous": false, "httpMethod": "GET", "implementFrom": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.IMoneyExchangeTransferCurrenciesAppService", "url": "api/money-exchange-transfers-service/money-exchange-transfer-currencies", "supportedVersions": [], "parametersOnMethod": [{"name": "input", "typeAsString": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.GetMoneyExchangeTransferCurrenciesInput, EMPS.MoneyExchangeTransfersService.Application.Contracts", "type": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.GetMoneyExchangeTransferCurrenciesInput", "typeSimple": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.GetMoneyExchangeTransferCurrenciesInput", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "input", "name": "FilterText", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "Sorting", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "SkipCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "MaxResultCount", "jsonName": null, "type": "System.Int32", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "No", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "AmountMin", "jsonName": null, "type": "System.Double", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "AmountMax", "jsonName": null, "type": "System.Double", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "CurrencyCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "CurrencyId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ServicePointName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ServicePointId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "TransactionIssueUserName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "TransactionIssueUserId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "TransactionIssueStaffName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "TransactionIssueStaffId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "TransactionIssueSeatId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "IsApproved", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ApprovedByUserName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ApprovedByUserId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ApprovalDateMin", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ApprovalDateMax", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ApprovalDateTimeMin", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ApprovalDateTimeMax", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "Notes", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "IsExecute", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExecutedByUserName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExecutedByUserId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExecutedByStaffName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExecutedByStaffId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExecutedBySeatId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExecuteDateMin", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExecuteDateMax", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExecuteDateTimeMin", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExecuteDateTimeMax", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "TransferStatus", "jsonName": null, "type": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferStatus", "typeSimple": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferStatus", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "TransferType", "jsonName": null, "type": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferType", "typeSimple": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferType", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}], "returnValue": {"type": "Volo.Abp.Application.Dtos.PagedResultDto<EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyDto>", "typeSimple": "Volo.Abp.Application.Dtos.PagedResultDto<EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyDto>"}}, "GetAsyncById": {"uniqueName": "GetAsyncById", "name": "GetAsync", "allowAnonymous": false, "httpMethod": "GET", "implementFrom": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.IMoneyExchangeTransferCurrenciesAppService", "url": "api/money-exchange-transfers-service/money-exchange-transfer-currencies/{id}", "supportedVersions": [], "parametersOnMethod": [{"name": "id", "typeAsString": "System.Guid, System.Private.CoreLib", "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "id", "name": "id", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}], "returnValue": {"type": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyDto", "typeSimple": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyDto"}}, "DeleteAsyncById": {"uniqueName": "DeleteAsyncById", "name": "DeleteAsync", "allowAnonymous": false, "httpMethod": "DELETE", "implementFrom": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.IMoneyExchangeTransferCurrenciesAppService", "url": "api/money-exchange-transfers-service/money-exchange-transfer-currencies/{id}", "supportedVersions": [], "parametersOnMethod": [{"name": "id", "typeAsString": "System.Guid, System.Private.CoreLib", "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "id", "name": "id", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}], "returnValue": {"type": "System.Void", "typeSimple": "System.Void"}}, "CreateAsyncByInput": {"uniqueName": "CreateAsyncByInput", "name": "CreateAsync", "allowAnonymous": false, "httpMethod": "POST", "implementFrom": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.IMoneyExchangeTransferCurrenciesAppService", "url": "api/money-exchange-transfers-service/money-exchange-transfer-currencies", "supportedVersions": [], "parametersOnMethod": [{"name": "input", "typeAsString": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyCreateDto, EMPS.MoneyExchangeTransfersService.Application.Contracts", "type": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyCreateDto", "typeSimple": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyCreateDto", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "input", "name": "input", "jsonName": null, "type": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyCreateDto", "typeSimple": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyCreateDto", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "Body", "descriptorName": ""}], "returnValue": {"type": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyDto", "typeSimple": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyDto"}}, "UpdateAsyncByIdAndInput": {"uniqueName": "UpdateAsyncByIdAndInput", "name": "UpdateAsync", "allowAnonymous": false, "httpMethod": "PUT", "implementFrom": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.IMoneyExchangeTransferCurrenciesAppService", "url": "api/money-exchange-transfers-service/money-exchange-transfer-currencies/{id}", "supportedVersions": [], "parametersOnMethod": [{"name": "id", "typeAsString": "System.Guid, System.Private.CoreLib", "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null}, {"name": "input", "typeAsString": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyUpdateDto, EMPS.MoneyExchangeTransfersService.Application.Contracts", "type": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyUpdateDto", "typeSimple": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyUpdateDto", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "id", "name": "id", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": [], "bindingSourceId": "Path", "descriptorName": ""}, {"nameOnMethod": "input", "name": "input", "jsonName": null, "type": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyUpdateDto", "typeSimple": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyUpdateDto", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "Body", "descriptorName": ""}], "returnValue": {"type": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyDto", "typeSimple": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyDto"}}, "GetListAsExcelFileAsyncByInput": {"uniqueName": "GetListAsExcelFileAsyncByInput", "name": "GetListAsExcelFileAsync", "allowAnonymous": true, "httpMethod": "GET", "implementFrom": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.IMoneyExchangeTransferCurrenciesAppService", "url": "api/money-exchange-transfers-service/money-exchange-transfer-currencies/as-excel-file", "supportedVersions": [], "parametersOnMethod": [{"name": "input", "typeAsString": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyExcelDownloadDto, EMPS.MoneyExchangeTransfersService.Application.Contracts", "type": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyExcelDownloadDto", "typeSimple": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrencyExcelDownloadDto", "isOptional": false, "defaultValue": null}], "parameters": [{"nameOnMethod": "input", "name": "DownloadToken", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "FilterText", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "No", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "AmountMin", "jsonName": null, "type": "System.Double", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "AmountMax", "jsonName": null, "type": "System.Double", "typeSimple": "number", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "CurrencyCode", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "CurrencyId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ServicePointName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ServicePointId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "TransactionIssueUserName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "TransactionIssueUserId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "TransactionIssueStaffName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "TransactionIssueStaffId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "TransactionIssueSeatId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "IsApproved", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ApprovedByUserName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ApprovedByUserId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ApprovalDateMin", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ApprovalDateMax", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ApprovalDateTimeMin", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ApprovalDateTimeMax", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "Notes", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "IsExecute", "jsonName": null, "type": "System.Boolean", "typeSimple": "boolean", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExecutedByUserName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExecutedByUserId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExecutedByStaffName", "jsonName": null, "type": "System.String", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExecutedByStaffId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExecutedBySeatId", "jsonName": null, "type": "System.Guid", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExecuteDateMin", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExecuteDateMax", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExecuteDateTimeMin", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "ExecuteDateTimeMax", "jsonName": null, "type": "System.DateTime", "typeSimple": "string", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "TransferStatus", "jsonName": null, "type": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferStatus", "typeSimple": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferStatus", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}, {"nameOnMethod": "input", "name": "TransferType", "jsonName": null, "type": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferType", "typeSimple": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferType", "isOptional": false, "defaultValue": null, "constraintTypes": null, "bindingSourceId": "ModelBinding", "descriptorName": "input"}], "returnValue": {"type": "Volo.Abp.Content.IRemoteStreamContent", "typeSimple": "Volo.Abp.Content.IRemoteStreamContent"}}, "GetDownloadTokenAsync": {"uniqueName": "GetDownloadTokenAsync", "name": "GetDownloadTokenAsync", "allowAnonymous": false, "httpMethod": "GET", "implementFrom": "EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.IMoneyExchangeTransferCurrenciesAppService", "url": "api/money-exchange-transfers-service/money-exchange-transfer-currencies/download-token", "supportedVersions": [], "parametersOnMethod": [], "parameters": [], "returnValue": {"type": "EMPS.MoneyExchangeTransfersService.Shared.DownloadTokenResultDto", "typeSimple": "EMPS.MoneyExchangeTransfersService.Shared.DownloadTokenResultDto"}}}}}}}}