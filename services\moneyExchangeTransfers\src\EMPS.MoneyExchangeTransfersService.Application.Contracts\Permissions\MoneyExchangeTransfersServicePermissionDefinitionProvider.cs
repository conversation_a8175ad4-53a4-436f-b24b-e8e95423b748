using EMPS.MoneyExchangeTransfersService.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;

namespace EMPS.MoneyExchangeTransfersService.Permissions;

public class MoneyExchangeTransfersServicePermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(MoneyExchangeTransfersServicePermissions.GroupName, L("Permission:MoneyExchangeTransfersService"));

        //Define your own permissions here. Example:
        //myGroup.AddPermission(BookStorePermissions.MyPermission1, L("Permission:MyPermission1"));

        var moneyExchangeTransferCurrencyPermission = myGroup.AddPermission(MoneyExchangeTransfersServicePermissions.MoneyExchangeTransferCurrencies.Default, L("Permission:MoneyExchangeTransferCurrencies"));
        moneyExchangeTransferCurrencyPermission.AddChild(MoneyExchangeTransfersServicePermissions.MoneyExchangeTransferCurrencies.Create, L("Permission:Create"));
        moneyExchangeTransferCurrencyPermission.AddChild(MoneyExchangeTransfersServicePermissions.MoneyExchangeTransferCurrencies.Edit, L("Permission:Edit"));
        moneyExchangeTransferCurrencyPermission.AddChild(MoneyExchangeTransfersServicePermissions.MoneyExchangeTransferCurrencies.Delete, L("Permission:Delete"));

        var cashierToCashierTransferPermission = myGroup.AddPermission(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Default, L("Permission:CashierToCashierTransfer"));
        cashierToCashierTransferPermission.AddChild(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Save, L("Permission:Save"));
        cashierToCashierTransferPermission.AddChild(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Execute, L("Permission:Execute"));
        cashierToCashierTransferPermission.AddChild(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.Delete, L("Permission:Delete"));
        cashierToCashierTransferPermission.AddChild(MoneyExchangeTransfersServicePermissions.CashierToCashierTransfer.ViewToExecute, L("Permission:ViewToExecute"));

        var servicePointToCompanyTransferPermission = myGroup.AddPermission(MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Default, L("Permission:ServicePointToCompanyTransfer"));
        servicePointToCompanyTransferPermission.AddChild(MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Save, L("Permission:Save"));
        servicePointToCompanyTransferPermission.AddChild(MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Execute, L("Permission:Execute"));
        servicePointToCompanyTransferPermission.AddChild(MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Delete, L("Permission:Delete"));
        servicePointToCompanyTransferPermission.AddChild(MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Approve, L("Permission:Approve"));
        servicePointToCompanyTransferPermission.AddChild(MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.ViewToExecute, L("Permission:ViewToExecute"));

        var companyToServicePointTransferPermission = myGroup.AddPermission(MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Default, L("Permission:CompanyToServicePointTransfer"));
        companyToServicePointTransferPermission.AddChild(MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Save, L("Permission:Save"));
        companyToServicePointTransferPermission.AddChild(MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Execute, L("Permission:Execute"));
        companyToServicePointTransferPermission.AddChild(MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Delete, L("Permission:Delete"));
        companyToServicePointTransferPermission.AddChild(MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Approve, L("Permission:Approve"));

        var paymentCashVoucherPermission = myGroup.AddPermission(MoneyExchangeTransfersServicePermissions.PaymentCashVouchers.Default, L("Permission:PaymentCashVouchers"));
        paymentCashVoucherPermission.AddChild(MoneyExchangeTransfersServicePermissions.PaymentCashVouchers.Save, L("Permission:Save"));
        paymentCashVoucherPermission.AddChild(MoneyExchangeTransfersServicePermissions.PaymentCashVouchers.Delete, L("Permission:Delete"));
        paymentCashVoucherPermission.AddChild(MoneyExchangeTransfersServicePermissions.PaymentCashVouchers.Approve, L("Permission:Approve"));
        paymentCashVoucherPermission.AddChild(MoneyExchangeTransfersServicePermissions.PaymentCashVouchers.Pay, L("Permission:Pay"));

        var receiptCashVoucherPermission = myGroup.AddPermission(MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Default, L("Permission:ReceiptCashVouchers"));
        receiptCashVoucherPermission.AddChild(MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Save, L("Permission:Save"));
        receiptCashVoucherPermission.AddChild(MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Delete, L("Permission:Delete"));
        receiptCashVoucherPermission.AddChild(MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Approve, L("Permission:Approve"));
        receiptCashVoucherPermission.AddChild(MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Receipt, L("Permission:Receipt"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<MoneyExchangeTransfersServiceResource>(name);
    }
}