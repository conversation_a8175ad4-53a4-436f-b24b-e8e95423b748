using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq.Dynamic.Core;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.MoneyExchangeTransfersService.CashVouchers;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.CompanyService.Staffs;
using EMPS.CompanyService.Application.Contracts.Dtos.Staffs;
using EMPS.CompanyService.CentralVaults;
using Volo.Abp.Domain.Entities;
using EMPS.CompanyService.ServicePoints;
using Volo.Abp.Identity;
using EMPS.CompanyService.ServicePointSeats;
using EMPS.CompanyService.Currencies;
using Bogus;
using EMPS.Shared.Enum;
using EMPS.MoneyExchangeLedgerService.MoneyExchangeBalanceTrackings;

namespace EMPS.MoneyExchangeTransfersService.CashVouchers
{

    public abstract class CashVouchersAppService : MoneyExchangeBlobBaseFileSystemAppService
    {

        protected readonly ICashVoucherRepository _cashVoucherRepository;
        protected readonly CashVoucherManager _cashVoucherManager;
        protected readonly IIdentityUserAppService _IdentityUserAppService;
        protected readonly IStaffsAppService _StaffsAppService;
        protected readonly IServicePointSeatsAppService _ServicePointSeatsAppService;
        protected readonly IServicePointsAppService _ServicePointsAppService;
        protected readonly ICentralVaultsAppService _centralVaultsAppService;
        protected readonly ICurrenciesAppService _currencyAppService;
        protected readonly IOrganizationUnitAppService _organizationUnitAppService;
        protected readonly IMoneyExchangeBalanceTrackingsAppService _moneyExchangeBalanceTrackingsAppService;

        protected CashVouchersAppService(ICashVoucherRepository cashVoucherRepository, CashVoucherManager cashVoucherManager, IIdentityUserAppService identityUserAppService, IStaffsAppService staffsAppService, IServicePointSeatsAppService servicePointSeatsAppService, IServicePointsAppService servicePointsAppService, ICentralVaultsAppService centralVaultsAppService, ICurrenciesAppService currencyAppService, IOrganizationUnitAppService organizationUnitAppService, IMoneyExchangeBalanceTrackingsAppService moneyExchangeBalanceTrackingsAppService)
        {
            _cashVoucherRepository = cashVoucherRepository;
            _cashVoucherManager = cashVoucherManager;
            _IdentityUserAppService = identityUserAppService;
            _StaffsAppService = staffsAppService;
            _ServicePointSeatsAppService = servicePointSeatsAppService;
            _ServicePointsAppService = servicePointsAppService;
            _centralVaultsAppService = centralVaultsAppService;
            _currencyAppService = currencyAppService;
            _organizationUnitAppService = organizationUnitAppService;
            _moneyExchangeBalanceTrackingsAppService = moneyExchangeBalanceTrackingsAppService;
        }

        public virtual async Task<PagedResultDto<CashVoucherDto>> GetListAsync(GetCashVouchersInput input)
        {
            var totalCount = await _cashVoucherRepository.GetCountAsync(input.FilterText, input.No, input.AmountMin, input.AmountMax, input.CurrencyCode, input.CurrencyId, input.SeatLocationName, input.SeatLocationId, input.IssueUserName, input.IssueUserId, input.IssueStaffName, input.IssueStaffId, input.IssueSeatId, input.IsApproved, input.ApprovedByUserName, input.ApprovedByUserId, input.ApprovalDateMin, input.ApprovalDateMax, input.ApprovalDateTimeMin, input.ApprovalDateTimeMax, input.Notes, input.IsExecute, input.ExecutedByUserName, input.ExecutedByUserId, input.ExecutedByStaffName, input.ExecutedByStaffId, input.ExecutedBySeatId, input.ExecuteDateMin, input.ExecuteDateMax, input.ExecuteDateTimeMin, input.ExecuteDateTimeMax, input.ApproveByStaffName, input.ApproveByStaffId, input.ApproveBySeatId, input.AccountingType, input.BolbKey, input.FileName, input.FileExtension, input.FileSize, input.FinancialPeriodId, input.FinancialPeriodName, input.FinancialAccountId, input.FinancialAccountName, input.AssignSeatLocation, input.CostCenterId, input.CostCenterName);
            var items = await _cashVoucherRepository.GetListAsync(input.FilterText, input.No, input.AmountMin, input.AmountMax, input.CurrencyCode, input.CurrencyId, input.SeatLocationName, input.SeatLocationId, input.IssueUserName, input.IssueUserId, input.IssueStaffName, input.IssueStaffId, input.IssueSeatId, input.IsApproved, input.ApprovedByUserName, input.ApprovedByUserId, input.ApprovalDateMin, input.ApprovalDateMax, input.ApprovalDateTimeMin, input.ApprovalDateTimeMax, input.Notes, input.IsExecute, input.ExecutedByUserName, input.ExecutedByUserId, input.ExecutedByStaffName, input.ExecutedByStaffId, input.ExecutedBySeatId, input.ExecuteDateMin, input.ExecuteDateMax, input.ExecuteDateTimeMin, input.ExecuteDateTimeMax, input.ApproveByStaffName, input.ApproveByStaffId, input.ApproveBySeatId, input.AccountingType, input.BolbKey, input.FileName, input.FileExtension, input.FileSize, input.FinancialPeriodId, input.FinancialPeriodName, input.FinancialAccountId, input.FinancialAccountName, input.AssignSeatLocation, input.CostCenterId, input.CostCenterName, input.Sorting, input.MaxResultCount, input.SkipCount);

            return new PagedResultDto<CashVoucherDto>
            {
                TotalCount = totalCount,
                Items = ObjectMapper.Map<List<CashVoucher>, List<CashVoucherDto>>(items)
            };
        }

        public virtual async Task<CashVoucherDto> GetAsync(Guid id)
        {
            return ObjectMapper.Map<CashVoucher, CashVoucherDto>(await _cashVoucherRepository.GetAsync(id));
        }

        public async virtual Task<CashVoucherDto?> LoadByNoAsync(string no)
        {

            var Staff = await CheckStaffInformation(CurrentUser.Id!.Value);
            ServicePointDto? ServicePoint = null;
            CentralVaultDto? CentralVault = null;
            if (Staff != null)
            {
                StaffSeatDto? staffSeatDto = await GetStaffSeatInformation(Staff);

                if (staffSeatDto != null)
                {
                    if (staffSeatDto.IsSeatForCentralVault != default)
                    {
                        CentralVault = await GetCentralVaultAsync(staffSeatDto.AssignedLocationId);

                    }
                    else
                    if (staffSeatDto.IsSeatForServicePoint != default)
                    {
                        ServicePoint = await GetServicePointAsync(staffSeatDto.AssignedLocationId);
                    }
                }

            }
            else
            {
                await CheckIfTheUserIsActive();

            }
            Guid? spId = ServicePoint == null ? null : ServicePoint.Id;

            Guid? cvId = CentralVault == null ? null : CentralVault.Id;

            var voucher = await GetRequiredCashVoucher(no, spId, cvId);
            if (voucher == null) return null;
            var voucherDto = ObjectMapper.Map<CashVoucher, CashVoucherDto>(voucher);
            return voucherDto;

        }


        public async virtual Task<CashVoucherDto> SaveAsync(CashVoucherCreateDto input)
        {
            var Staff = await CheckStaffInformation(CurrentUser.Id!.Value);
            ServicePointDto? ServicePoint = null;
            CentralVaultDto? CentralVault = null;
            Guid SeatId = Guid.Empty;
            if (Staff != null)
            {
                StaffSeatDto? staffSeatDto = await GetStaffSeatInformation(Staff);
                if (staffSeatDto != null)
                {

                    SeatId = staffSeatDto.SeatId;

                    if (staffSeatDto.IsSeatForCentralVault != default)
                    {
                        CentralVault = await GetCentralVaultAsync(staffSeatDto.AssignedLocationId);
                    }
                    else
                    if (staffSeatDto.IsSeatForServicePoint != default)
                    {
                        ServicePoint = await GetServicePointAsync(staffSeatDto.AssignedLocationId);
                    }

                }
            }
            else
            {
                await CheckIfTheUserIsActive();

            }
            Guid? spId = ServicePoint == null ? null : ServicePoint.Id;

            Guid? cvId = CentralVault == null ? null : CentralVault.Id;

            bool IsUpdating = await DetectUpdateModeByNoAsync(input.No, GetAccountingMode());
            CheckRequiredInputs(input);
            var voucher = new CashVoucher();
            if (IsUpdating)
            {
                voucher = await GetRequiredCashVoucher(input.No!, spId, cvId);
                CheckCanUpdateOrDeleteVoucher(voucher);

                if (spId.HasValue || cvId.HasValue)//this means that the staff who make the operation approve is staff for service point or central vault 
                {
                    if (voucher.IssueStaffId == null) throw new UserFriendlyException(L["Error:ThisOperationIsCreatedFromStaffCompany"]);
                    if (voucher.IssueStaffId.HasValue)//this means that the operation issue  made form staff for service point or central vault  
                    {
                        if (voucher.AssignSeatLocation == SeatLocationType.ServicePoint)
                            if (spId == null || voucher.SeatLocationId != spId) throw new UserFriendlyException(L["Error:ThisOperationIsCreatedFromAnotherServicePointForThisStaff"]);
                        if (voucher.AssignSeatLocation == SeatLocationType.CentralVault)
                            if (cvId == null || voucher.SeatLocationId != cvId) throw new UserFriendlyException(L["Error:ThisOperationIsCreatedFromAnotherCentralVaultForThisStaff"]);

                    }
                }

                if (voucher.BolbKey != null)
                    voucher.BolbKey = await UpdateBlobByNameAndContentAsync(voucher.BolbKey, input.BlobContent);
                else if (voucher.BolbKey != null)
                {
                    DeleteBlobFromTheFolderByNameAsync(voucher.BolbKey);
                    voucher.FillFileInfo(null, null, null, null);
                }
            }
            else
            {
                voucher.No = await GenerateNewNumberForCashVoucher();
                if (voucher.No == "-1") throw new UserFriendlyException(L["Error:SomeDataInDatabaseEncrypted"]);
                if (input.BlobContent != null)
                    voucher.BolbKey = await GenerateNewBlob(input.BlobContent);
            }

            voucher.Amount = input.Amount;
            var currency = await GetCurrencyAsync(input.CurrencyId!.Value);
            voucher.FillCurrencyInfo(currency.Code, currency.Id);
            //this line will remove when the operation support multi currencies
            if (currency.CurrencyType != CurrencyType.Local) throw new UserFriendlyException(L["Error:CurrencyTypeMustBeLocal"]);
            //get cost center and financial period and financial account from ziad 
            voucher.FillFinancialInfo(input.FinancialPeriodId, input.FinancialPeriodName, input.FinancialAccountId, input.FinancialAccountName, input.CostCenterId, input.CostCenterName);

            if (spId.HasValue || cvId.HasValue)//this means that the staff who make the operation is staff for service point or central vault 
            {
                var cashierStaff = await CheckStaffInformation(input.CashierId!.Value) ?? throw new UserFriendlyException(L["Error:TheSelectedCashierIsNotSraff"]);
                StaffSeatDto cashierStaffSeatDto = await GetStaffSeatInformation(cashierStaff);
                await CheckCashierStaffIsCashier(cashierStaffSeatDto!);
                voucher.FillCashierInfo(cashierStaff.Name, cashierStaff.Id);
                if (spId.HasValue)
                {

                    CheckCashierIsStaffInServicePoint(cashierStaffSeatDto);
                    voucher.FillSeatLocationInfo(ServicePoint!.Name, ServicePoint.Id);
                    voucher.AssignSeatLocation = SeatLocationType.ServicePoint;


                }
                else if (cvId.HasValue)
                {
                    CheckCashierIsStaffInCentralVault(cashierStaffSeatDto);
                    voucher.FillSeatLocationInfo(CentralVault!.Name, CentralVault.Id);
                    voucher.AssignSeatLocation = SeatLocationType.CentralVault;

                }
                voucher.FillIssueInfo(GetFullUserName(), GeTUserId(), $"{Staff!.Name} {Staff.Surname}", SeatId);
            }
            else
            {
                if (input.AssignSeatLocation != SeatLocationType.Company)
                {
                    var cashierStaff = await CheckStaffInformation(input.CashierId!.Value) ?? throw new UserFriendlyException(L["Error:TheSelectedCashierIsNotSraff"]);
                    StaffSeatDto cashierStaffSeatDto = await GetStaffSeatInformation(cashierStaff);
                    await CheckCashierStaffIsCashier(cashierStaffSeatDto);
                    voucher.FillCashierInfo(cashierStaff.Name, cashierStaff.Id);

                    if (input.AssignSeatLocation == SeatLocationType.ServicePoint)
                    {
                        var servicePoint = await GetServicePointAsync(input.SeatLocationId!.Value);
                        CheckCashierIsStaffInServicePoint(cashierStaffSeatDto);
                        voucher.FillSeatLocationInfo(ServicePoint!.Name, ServicePoint.Id);
                        voucher.AssignSeatLocation = SeatLocationType.ServicePoint;


                    }
                    else if (input.AssignSeatLocation == SeatLocationType.CentralVault)
                    {
                        var centralvault = await GetCentralVaultAsync(input.SeatLocationId!.Value);
                        CheckCashierIsStaffInCentralVault(cashierStaffSeatDto);
                        voucher.FillSeatLocationInfo(CentralVault!.Name, CentralVault.Id);
                        voucher.AssignSeatLocation = SeatLocationType.CentralVault;

                    }

                }
                else if (input.AssignSeatLocation == SeatLocationType.Company)
                {
                    voucher.FillSeatLocationInfo(null, Guid.Empty);
                    voucher.AssignSeatLocation = SeatLocationType.Company;
                    voucher.FillCashierInfo(null, Guid.Empty);

                }
                voucher.FillIssueInfo(GetFullUserName(), GeTUserId());

            }
            voucher = await SetBusinessLogicFromInheritanceClassesForSaveAsync(voucher);
            voucher = await SaveOrUpdateCashVoucher(IsUpdating, voucher);

            return ObjectMapper.Map<CashVoucher, CashVoucherDto>(voucher);

        }

        public virtual async Task DeletedByNoAsync(string no)
        {
            var Staff = await CheckStaffInformation(CurrentUser.Id!.Value);
            ServicePointDto? ServicePoint = null;
            CentralVaultDto CentralVault = null;
            Guid SeatId = Guid.Empty;
            if (Staff != null)
            {
                StaffSeatDto staffSeatDto = await GetStaffSeatInformation(Staff);
                if (staffSeatDto != null)
                {
                    SeatId = staffSeatDto.SeatId;

                    if (staffSeatDto.IsSeatForCentralVault != default)
                    {
                        CentralVault = await GetCentralVaultAsync(staffSeatDto.AssignedLocationId);
                    }
                    else
                    if (staffSeatDto.IsSeatForServicePoint != default)
                    {
                        ServicePoint = await GetServicePointAsync(staffSeatDto.AssignedLocationId);
                    }
                 
                }
            }
            else
            {
                await CheckIfTheUserIsActive();

            }
            Guid? spId = ServicePoint == null ? null : ServicePoint.Id;

            Guid? cvId = CentralVault == null ? null : CentralVault.Id;
            var voucher = await GetRequiredCashVoucher(no, spId, cvId) ?? throw new UserFriendlyException(L["Error:ThisNoIsNotExist"]);
            CheckCanUpdateOrDeleteVoucher(voucher);

            if (spId.HasValue || cvId.HasValue)//this means that the staff who make the operation approve is staff for service point or central vault 
            {
                if (voucher.IssueStaffId == null) throw new UserFriendlyException(L["Error:ThisOperationIsCreatedFromStaffCompany"]);
                if (voucher.IssueStaffId.HasValue)//this means that the operation issue  made form staff for service point or central vault  
                {
                    if (voucher.AssignSeatLocation == SeatLocationType.ServicePoint)
                        if (spId == null || voucher.SeatLocationId != spId) throw new UserFriendlyException(L["Error:ThisOperationIsCreatedFromAnotherServicePointForThisStaff"]);
                    if (voucher.AssignSeatLocation == SeatLocationType.CentralVault)
                        if (cvId == null || voucher.SeatLocationId != cvId) throw new UserFriendlyException(L["Error:ThisOperationIsCreatedFromAnotherCentralVaultForThisStaff"]);

                }
            }
            await _cashVoucherRepository.DeleteAsync(voucher);

        }



        public async virtual Task<CashVoucherDto> ApproveByNoAsync(string no)
        {

            var Staff = await CheckStaffInformation(CurrentUser.Id!.Value);
            ServicePointDto? ServicePoint = null;
            CentralVaultDto CentralVault = null;
            Guid SeatId = Guid.Empty;
            if (Staff != null)
            {
                StaffSeatDto staffSeatDto = await GetStaffSeatInformation(Staff);
                if (staffSeatDto != null)
                {
                    SeatId = staffSeatDto.SeatId;

                    if (staffSeatDto.IsSeatForCentralVault != default)
                    {
                        CentralVault = await GetCentralVaultAsync(staffSeatDto.AssignedLocationId);
                    }
                    else
                    if (staffSeatDto.IsSeatForServicePoint != default)
                    {
                        ServicePoint = await GetServicePointAsync(staffSeatDto.AssignedLocationId);
                    }
                }


            }
            else
            {
                await CheckIfTheUserIsActive();

            }
            Guid? spId = ServicePoint == null ? null : ServicePoint.Id;

            Guid? cvId = CentralVault == null ? null : CentralVault.Id;
            var voucher = await GetRequiredCashVoucher(no, spId, cvId) ?? throw new UserFriendlyException(L["Error:ThisNoIsNotExist"]);
            CheckCanApproveVoucher(voucher);

            if (spId.HasValue || cvId.HasValue)//this means that the staff who make the operation approve is staff for service point or central vault 
            {
                if (voucher.IssueStaffId == null) throw new UserFriendlyException(L["Error:ThisOperationIsCreatedFromStaffCompany"]);
                if (voucher.IssueStaffId.HasValue)//this means that the operation issue  made form staff for service point or central vault  
                {
                    if (voucher.AssignSeatLocation == SeatLocationType.ServicePoint)
                        if (spId == null || voucher.SeatLocationId != spId) throw new UserFriendlyException(L["Error:ThisOperationIsCreatedFromAnotherServicePointForThisStaff"]);
                    if (voucher.AssignSeatLocation == SeatLocationType.CentralVault)
                        if (cvId == null || voucher.SeatLocationId != cvId) throw new UserFriendlyException(L["Error:ThisOperationIsCreatedFromAnotherCentralVaultForThisStaff"]);
                    voucher.FillApprovalInfo(GetFullUserName(), GeTUserId(), $"{Staff!.Name} {Staff.Surname}", SeatId);

                }
            }
            else
            {

                voucher.FillApprovalInfo(GetFullUserName(), GeTUserId());
            }
            voucher = await SetBusinessLogicFromInheritanceClassesForApproveAsync(voucher);
            voucher = await SaveOrUpdateCashVoucher(true, voucher);

            return ObjectMapper.Map<CashVoucher, CashVoucherDto>(voucher);
        }
        public async virtual Task<CashVoucherDto> ExecuteByNoAsync(string no)
        {
            var Staff = await CheckStaffInformation(CurrentUser.Id!.Value);
            ServicePointDto? ServicePoint = null;
            CentralVaultDto? CentralVault = null;
            StaffSeatDto? staffSeatDto = null;
            if (Staff != null)
            {
                if (staffSeatDto != null)
                {

                    staffSeatDto = await GetStaffSeatInformation(Staff);

                    if (staffSeatDto.IsSeatForCentralVault != default)
                    {
                        CentralVault = await GetCentralVaultAsync(staffSeatDto.AssignedLocationId);
                    }
                    else
                    if (staffSeatDto.IsSeatForServicePoint != default)
                    {
                        ServicePoint = await GetServicePointAsync(staffSeatDto.AssignedLocationId);
                    }

                }
            }
            else
            {
                await CheckIfTheUserIsActive();

            }
            Guid? spId = ServicePoint == null ? null : ServicePoint.Id;

            Guid? cvId = CentralVault == null ? null : CentralVault.Id;
            var voucher = await GetRequiredCashVoucher(no, spId, cvId) ?? throw new UserFriendlyException(L["Error:ThisNoIsNotExist"]);
            CheckCanExcuteVoucher(voucher);

            if (spId.HasValue || cvId.HasValue)//this means that the staff who make the operation approve is staff for service point or central vault 
            {
                if (voucher.IssueStaffId == null) throw new UserFriendlyException(L["Error:ThisOperationIsCreatedFromStaffCompany"]);
                if (voucher.IssueStaffId.HasValue)//this means that the operation issue  made form staff for service point or central vault  
                {
                    if (voucher.AssignSeatLocation == SeatLocationType.ServicePoint)
                        if (spId == null || voucher.SeatLocationId != spId) throw new UserFriendlyException(L["Error:ThisOperationIsCreatedFromAnotherServicePointForThisStaff"]);


                    if (voucher.AssignSeatLocation == SeatLocationType.CentralVault)
                        if (cvId == null || voucher.SeatLocationId != cvId) throw new UserFriendlyException(L["Error:ThisOperationIsCreatedFromAnotherCentralVaultForThisStaff"]);


                    if (voucher.CashierId != Staff.Id) throw new UserFriendlyException(L["Error:ThisStaffIsNotTheSameCashier"]);
                    await CheckCashierStaffIsCashier(staffSeatDto);

                    voucher.FillExecutionInfo(GetFullUserName(), GeTUserId(), $"{Staff!.Name} {Staff.Surname}", staffSeatDto.SeatId);

                }
            }
            else
            {

                voucher.FillExecutionInfo(GetFullUserName(), GeTUserId());
            }
            voucher = await SetBusinessLogicFromInheritanceClassesForExecuteAsync(voucher, Staff, staffSeatDto != null ? staffSeatDto.SeatId : null);
            voucher = await SaveOrUpdateCashVoucher(true, voucher);

            return ObjectMapper.Map<CashVoucher, CashVoucherDto>(voucher);
        }
        public async Task<StaffSeatDto?> GetStaffSeatAsync()
        {
            var staff = await CheckStaffInformation(CurrentUser.Id!.Value);
            if (staff == null) return null;
            var staffSeat = await _StaffsAppService.GetStaffSeat(staff.Id);
            if (staffSeat == null) return null;
            return staffSeat;
        }
        public async Task<List<StaffDto>> GetAllCashierInServicePointAsync(Guid ServicePointId)
        {
            var servicePoint = await GetServicePointAsync(ServicePointId);
            servicePoint = CheckServicePointInformation(servicePoint);
            return await _ServicePointSeatsAppService.GetAllCashierByServicePointAsync(new GetServicePointSeatsInput() { ServicePointId = ServicePointId });

        }
        protected abstract Task<CashVoucher?> GetRequiredCashVoucher(string no, Guid? spId = null, Guid? cvId = null);
        private async Task<CashVoucher> SaveOrUpdateCashVoucher(bool isUpdating, CashVoucher voucher)
        {
            if (isUpdating)
                return await _cashVoucherRepository.UpdateAsync(voucher);

            return await _cashVoucherRepository.InsertAsync(voucher);
        }

        protected abstract Task<CashVoucher> SetBusinessLogicFromInheritanceClassesForSaveAsync(CashVoucher voucher);

        private void CheckCashierIsStaffInCentralVault(StaffSeatDto cashierStaffSeatDto)
        {
            if (cashierStaffSeatDto.IsSeatForCentralVault == default)
                throw new UserFriendlyException(L["Error:TheSelectedCashierIsNotSraffInThisCentralVault"]);
        }

        private void CheckCashierIsStaffInServicePoint(StaffSeatDto cashierStaffSeatDto)
        {
            if (cashierStaffSeatDto.IsSeatForServicePoint == default)
                throw new UserFriendlyException(L["Error:TheSelectedCashierIsNotSraffInThisServicePoint"]);
        }

        private async Task CheckCashierStaffIsCashier(StaffSeatDto cashierStaffSeatDto)
        {
            var OuSeat = await _organizationUnitAppService.GetAsync(cashierStaffSeatDto.OuId);
            if (OuSeat == null || !OuSeat.IsCash) throw new UserFriendlyException(L["ThisSeatNoCashier"]);
        }

        protected abstract AccountingMode GetAccountingMode();
        protected abstract Task<string?> GenerateNewNumberForCashVoucher();

        private void CheckRequiredInputs(CashVoucherCreateDto input)
        {
            if (input.Amount <= 0)
                throw new UserFriendlyException(L["TheAmountMustBeGreaterThanZero"]);
            if (input.CurrencyId == null || input.CurrencyId == Guid.Empty)
                throw new UserFriendlyException(L["CurrencyMustBeSelected"]);
        }

        private async Task<bool> DetectUpdateModeByNoAsync(string? no, AccountingMode mode)
        {
            return await _cashVoucherRepository.AnyAsync(x => x.No == no && x.AccountingType == mode);
        }
        protected abstract Task<CashVoucher> SetBusinessLogicFromInheritanceClassesForApproveAsync(CashVoucher voucher);

        private void CheckCanApproveVoucher(CashVoucher voucher)
        {
            if (voucher.IsApproved)
                throw new UserFriendlyException(L["Error:CashVoucherIsApprovedAlready"]);
            if (voucher.IsExecute)
                throw new UserFriendlyException(L["Error:CashVoucherIsExecuteCannotBeApproved"]);
        }
        private void CheckCanExcuteVoucher(CashVoucher voucher)
        {
            if (voucher.IsExecute)
                throw new UserFriendlyException(L["Error:CashVoucherIsExecuteAlready"]);
            if (!voucher.IsApproved)
                throw new UserFriendlyException(L["Error:CashVoucherIsNotApprovedCannotBeExecute"]);
        }

        private void CheckCanUpdateOrDeleteVoucher(CashVoucher voucher)
        {
            if (voucher.IsApproved)
                throw new UserFriendlyException(L["Error:CashVoucherIsApprovedCannotBeDeleted"]);
            if (voucher.IsExecute)
                throw new UserFriendlyException(L["Error:CashVoucherIsExecuteCannotBeDeleted"]);
        }


        protected abstract Task<CashVoucher> SetBusinessLogicFromInheritanceClassesForExecuteAsync(CashVoucher voucher, StaffDto? staff, Guid? seatId);

        protected ServicePointDto CheckServicePointInformation(ServicePointDto? servicePoint)
        {
            if (servicePoint == null)
                throw new UserFriendlyException(L["ServicePointIsNotExist"]);
            if (!servicePoint.IsActive)
                throw new UserFriendlyException(L["thisServicePointNotActive"]);

            return servicePoint;
        }

        protected async Task<StaffDto?> CheckStaffInformation(Guid userId)
        {
            var res = await _StaffsAppService.GetListAsync(new GetStaffsInput { UserID = userId });

            var Staff = res.Items.FirstOrDefault();
            if (Staff == null)
            {
                return null;
            }
            else
            {
                if (Staff != null && !Staff.Staff.IsActive)
                    throw new UserFriendlyException(L["Error:StaffIsNotActive"]);
                return Staff!.Staff;
            }
        }

        private async Task<StaffSeatDto?> GetStaffSeatInformation(StaffDto Staff)
        {
            var staffSeat = await _StaffsAppService.GetStaffSeat(Staff.Id);
            if (staffSeat == null) return null;
            return staffSeat;
        }
        protected async Task<CentralVaultDto> GetCentralVaultAsync(Guid id)
        {
            var cw = new CentralVaultDto();
            try
            {
                cw = await _centralVaultsAppService.GetAsync(id);
            }
            catch (EntityNotFoundException ex)
            {
                throw new UserFriendlyException(L["Error:CentralVaultIsNotExist"]);
            }
            if (!cw.IsActive) throw new UserFriendlyException(L["Error:CentralVaultIsNotActive"]);
            return cw;
        }
        protected async Task<ServicePointDto> GetServicePointAsync(Guid servicePointId)
        {
            var sp = new ServicePointDto();
            try
            {
                sp = await _ServicePointsAppService.GetAsync(servicePointId);
            }
            catch (EntityNotFoundException ex)
            {
                throw new UserFriendlyException(L["Error:ServicePointIsNotExist"]);
            }
            if (!sp.IsActive) throw new UserFriendlyException(L["Error:ServicePointIsNotActive"]);
            return sp;
        }
        /// <summary>
        /// this method to check if the user is active or not,
        /// throw UserFriendlyException if the user is not active
        /// </summary>
        /// <returns></returns>
        /// <exception cref="UserFriendlyException"></exception>
        protected async Task CheckIfTheUserIsActive()
        {
            // Asynchronously fetch the user information using the _identityUserAppService
            // by passing the current user's ID, which is cast to a Guid.
            if (CurrentUser.Id == null) throw new UserFriendlyException(L["thisIsNotUser"]);
            var user = await _IdentityUserAppService.GetAsync((Guid)CurrentUser.Id);

            // Check if the user's IsActive property is false.
            if (user.IsActive == false)
            {
                // If the user is not active, throw an exception with the message "thisUserIsNotActive".
                throw new UserFriendlyException(L["thisUserIsNotActive"]);
            }
        }
        protected async Task<CurrencyDto> GetCurrencyAsync(Guid CurrencyID)
        {
            try
            {
                var Currency = await _currencyAppService.GetAsync(CurrencyID);

                if (!Currency.Status)
                    throw new UserFriendlyException(L["ThisCurrencyIsNotActive"]);
                return Currency;

            }
            catch (Exception ex)
            {
                if (ex is EntityNotFoundException)
                    throw new UserFriendlyException(L["CurrencyNotFound"]);
                throw;
            }

        }
        protected string GetFullUserName()
        {
            return $"{CurrentUser.Name} {CurrentUser.SurName}";
        }
        protected Guid? GeTUserId()
        {
            return CurrentUser.Id;
        }
    }
}