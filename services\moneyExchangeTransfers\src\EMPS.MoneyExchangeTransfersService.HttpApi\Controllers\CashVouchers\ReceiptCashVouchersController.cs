using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.CashVouchers;
using EMPS.CompanyService.Application.Contracts.Dtos.Staffs;
using EMPS.CompanyService.Staffs;
using System.Collections.Generic;

namespace EMPS.MoneyExchangeTransfersService.Controllers.MoneyExchangeTransferCurrencies
{
    [RemoteService(Name = "MoneyExchangeTransfersService")]
    [Area("moneyExchangeTransfersService")]
    [ControllerName("ReceiptCashVouchersController")]
    [Route("api/money-exchange-transfers-service/ReceiptCashVouchersController")]
    public class ReceiptCashVouchersController : AbpController, IReceiptCashVouchersAppService
    {
        private readonly IReceiptCashVouchersAppService ReceiptCashVouchersAppService;

        public ReceiptCashVouchersController(IReceiptCashVouchersAppService ReceiptCashVouchersAppService)
        {
            this.ReceiptCashVouchersAppService = ReceiptCashVouchersAppService;
        }

        [HttpPut]
        [Route("ApproveByNoAsync")]
        public Task<CashVoucherDto> ApproveByNoAsync(string no)
        {
            return ReceiptCashVouchersAppService.ApproveByNoAsync(no);
        }

        [HttpDelete]
        [Route("DeletedByNoAsync")]
        public Task DeletedByNoAsync(string no)
        {
            return ReceiptCashVouchersAppService.DeletedByNoAsync(no);
        }

        [HttpPut]
        [Route("ReceiptByNoAsync")]
        public Task<CashVoucherDto> ReceiptByNoAsync(string no)
        {
            return ReceiptCashVouchersAppService.ReceiptByNoAsync(no);
        }

        [HttpGet]
        [Route("{id}")]
        public Task<CashVoucherDto> GetAsync(Guid id)
        {
            return ReceiptCashVouchersAppService.GetAsync(id);
        }
        [HttpGet]

        public Task<PagedResultDto<CashVoucherDto>> GetListAsync(GetCashVouchersInput input)
        {
            return ReceiptCashVouchersAppService.GetListAsync(input);
        }
        [HttpGet]
        [Route("LoadByNoAsync")]
        public Task<CashVoucherDto?> LoadByNoAsync(string no)
        {
            return ReceiptCashVouchersAppService.LoadByNoAsync(no);
        }
        [HttpPut]
        [Route("SaveAsync")]
        public Task<CashVoucherDto> SaveAsync(CashVoucherCreateDto input)
        {
            return ReceiptCashVouchersAppService.SaveAsync(input);
        }
        [HttpGet]
        [Route("GetStaffSeatAsync")]
        public Task<StaffSeatDto?> GetStaffSeatAsync()
        {
            return ReceiptCashVouchersAppService.GetStaffSeatAsync();
        }
        [HttpGet]
        [Route("GetAllCashierInServicePointAsync")]
        public Task<List<StaffDto>> GetAllCashierInServicePointAsync(Guid ServicePointId)
        {
            return ReceiptCashVouchersAppService.GetAllCashierInServicePointAsync(ServicePointId);
        }
        [HttpGet]
        [Route("GetBlobByNameAsync")]
        public Task<byte[]> GetBlobByNameAsync(string Name)
        {
            return ReceiptCashVouchersAppService.GetBlobByNameAsync(Name);
        }
    }
}