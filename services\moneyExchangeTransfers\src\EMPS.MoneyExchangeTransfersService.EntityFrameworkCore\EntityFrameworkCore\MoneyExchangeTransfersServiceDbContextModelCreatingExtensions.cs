using EMPS.MoneyExchangeTransfersService.CashVouchers;
using Volo.Abp.EntityFrameworkCore.Modeling;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;

namespace EMPS.MoneyExchangeTransfersService.EntityFrameworkCore;

public static class MoneyExchangeTransfersServiceDbContextModelCreatingExtensions
{
    public static void ConfigureMoneyExchangeTransfersService(this ModelBuilder builder)
    {
        Check.NotNull(builder, nameof(builder));

        /* Configure your own tables/entities inside here */

        //builder.Entity<YourEntity>(b =>
        //{
        //    b.ToTable(MoneyExchangeTransfersServiceConsts.DbTablePrefix + "YourEntities", MoneyExchangeTransfersServiceConsts.DbSchema);
        //    b.ConfigureByConvention(); //auto configure for the base class props
        //    //...
        //});
        if (builder.IsHostDatabase())
        {
            builder.Entity<MoneyExchangeTransferCurrency>(b =>
{
    b.ToTable(MoneyExchangeTransfersServiceDbProperties.DbTablePrefix + "MoneyExchangeTransferCurrencies", MoneyExchangeTransfersServiceDbProperties.DbSchema);
    b.ConfigureByConvention();
    b.Property(x => x.No).HasColumnName(nameof(MoneyExchangeTransferCurrency.No));
    b.Property(x => x.Amount).HasColumnName(nameof(MoneyExchangeTransferCurrency.Amount));
    b.Property(x => x.CurrencyCode).HasColumnName(nameof(MoneyExchangeTransferCurrency.CurrencyCode));
    b.Property(x => x.CurrencyId).HasColumnName(nameof(MoneyExchangeTransferCurrency.CurrencyId));
    b.Property(x => x.ServicePointName).HasColumnName(nameof(MoneyExchangeTransferCurrency.ServicePointName));
    b.Property(x => x.ServicePointId).HasColumnName(nameof(MoneyExchangeTransferCurrency.ServicePointId));
    b.Property(x => x.TransactionIssueUserName).HasColumnName(nameof(MoneyExchangeTransferCurrency.TransactionIssueUserName));
    b.Property(x => x.TransactionIssueUserId).HasColumnName(nameof(MoneyExchangeTransferCurrency.TransactionIssueUserId));
    b.Property(x => x.TransactionIssueStaffName).HasColumnName(nameof(MoneyExchangeTransferCurrency.TransactionIssueStaffName));
    b.Property(x => x.TransactionIssueStaffId).HasColumnName(nameof(MoneyExchangeTransferCurrency.TransactionIssueStaffId));
    b.Property(x => x.TransactionIssueSeatId).HasColumnName(nameof(MoneyExchangeTransferCurrency.TransactionIssueSeatId));
    b.Property(x => x.IsApproved).HasColumnName(nameof(MoneyExchangeTransferCurrency.IsApproved));
    b.Property(x => x.ApprovedByUserName).HasColumnName(nameof(MoneyExchangeTransferCurrency.ApprovedByUserName));
    b.Property(x => x.ApprovedByUserId).HasColumnName(nameof(MoneyExchangeTransferCurrency.ApprovedByUserId));
    b.Property(x => x.ApprovalDate).HasColumnName(nameof(MoneyExchangeTransferCurrency.ApprovalDate));
    b.Property(x => x.ApprovalDateTime).HasColumnName(nameof(MoneyExchangeTransferCurrency.ApprovalDateTime));
    b.Property(x => x.Notes).HasColumnName(nameof(MoneyExchangeTransferCurrency.Notes));
    b.Property(x => x.IsExecute).HasColumnName(nameof(MoneyExchangeTransferCurrency.IsExecute));
    b.Property(x => x.ExecutedByUserName).HasColumnName(nameof(MoneyExchangeTransferCurrency.ExecutedByUserName));
    b.Property(x => x.ExecutedByUserId).HasColumnName(nameof(MoneyExchangeTransferCurrency.ExecutedByUserId));
    b.Property(x => x.ExecutedByStaffName).HasColumnName(nameof(MoneyExchangeTransferCurrency.ExecutedByStaffName));
    b.Property(x => x.ExecutedByStaffId).HasColumnName(nameof(MoneyExchangeTransferCurrency.ExecutedByStaffId));
    b.Property(x => x.ExecutedBySeatId).HasColumnName(nameof(MoneyExchangeTransferCurrency.ExecutedBySeatId));
    b.Property(x => x.ExecuteDate).HasColumnName(nameof(MoneyExchangeTransferCurrency.ExecuteDate));
    b.Property(x => x.ExecuteDateTime).HasColumnName(nameof(MoneyExchangeTransferCurrency.ExecuteDateTime));
    b.Property(x => x.TransferStatus).HasColumnName(nameof(MoneyExchangeTransferCurrency.TransferStatus));
    b.Property(x => x.TransferType).HasColumnName(nameof(MoneyExchangeTransferCurrency.TransferType));
});

        }
        if (builder.IsHostDatabase())
        {

        }
        if (builder.IsHostDatabase())
        {

        }
        if (builder.IsHostDatabase())
        {
            builder.Entity<CashVoucher>(b =>
{
    b.ToTable(MoneyExchangeTransfersServiceDbProperties.DbTablePrefix + "CashVouchers", MoneyExchangeTransfersServiceDbProperties.DbSchema);
    b.ConfigureByConvention();
    b.Property(x => x.No).HasColumnName(nameof(CashVoucher.No));
    b.Property(x => x.Amount).HasColumnName(nameof(CashVoucher.Amount));
    b.Property(x => x.CurrencyCode).HasColumnName(nameof(CashVoucher.CurrencyCode));
    b.Property(x => x.CurrencyId).HasColumnName(nameof(CashVoucher.CurrencyId));
    b.Property(x => x.SeatLocationName).HasColumnName(nameof(CashVoucher.SeatLocationName));
    b.Property(x => x.SeatLocationId).HasColumnName(nameof(CashVoucher.SeatLocationId));
    b.Property(x => x.IssueUserName).HasColumnName(nameof(CashVoucher.IssueUserName));
    b.Property(x => x.IssueUserId).HasColumnName(nameof(CashVoucher.IssueUserId));
    b.Property(x => x.IssueStaffName).HasColumnName(nameof(CashVoucher.IssueStaffName));
    b.Property(x => x.IssueStaffId).HasColumnName(nameof(CashVoucher.IssueStaffId));
    b.Property(x => x.IssueSeatId).HasColumnName(nameof(CashVoucher.IssueSeatId));
    b.Property(x => x.IsApproved).HasColumnName(nameof(CashVoucher.IsApproved));
    b.Property(x => x.ApprovedByUserName).HasColumnName(nameof(CashVoucher.ApprovedByUserName));
    b.Property(x => x.ApprovedByUserId).HasColumnName(nameof(CashVoucher.ApprovedByUserId));
    b.Property(x => x.ApprovalDate).HasColumnName(nameof(CashVoucher.ApprovalDate));
    b.Property(x => x.ApprovalDateTime).HasColumnName(nameof(CashVoucher.ApprovalDateTime));
    b.Property(x => x.Notes).HasColumnName(nameof(CashVoucher.Notes));
    b.Property(x => x.IsExecute).HasColumnName(nameof(CashVoucher.IsExecute));
    b.Property(x => x.ExecutedByUserName).HasColumnName(nameof(CashVoucher.ExecutedByUserName));
    b.Property(x => x.ExecutedByUserId).HasColumnName(nameof(CashVoucher.ExecutedByUserId));
    b.Property(x => x.ExecutedByStaffName).HasColumnName(nameof(CashVoucher.ExecutedByStaffName));
    b.Property(x => x.ExecutedByStaffId).HasColumnName(nameof(CashVoucher.ExecutedByStaffId));
    b.Property(x => x.ExecutedBySeatId).HasColumnName(nameof(CashVoucher.ExecutedBySeatId));
    b.Property(x => x.ExecuteDate).HasColumnName(nameof(CashVoucher.ExecuteDate));
    b.Property(x => x.ExecuteDateTime).HasColumnName(nameof(CashVoucher.ExecuteDateTime));
    b.Property(x => x.ApproveByStaffName).HasColumnName(nameof(CashVoucher.ApproveByStaffName));
    b.Property(x => x.ApproveByStaffId).HasColumnName(nameof(CashVoucher.ApproveByStaffId));
    b.Property(x => x.ApproveBySeatId).HasColumnName(nameof(CashVoucher.ApproveBySeatId));
    b.Property(x => x.AccountingType).HasColumnName(nameof(CashVoucher.AccountingType));
    b.Property(x => x.BolbKey).HasColumnName(nameof(CashVoucher.BolbKey));
    b.Property(x => x.FileName).HasColumnName(nameof(CashVoucher.FileName));
    b.Property(x => x.FileExtension).HasColumnName(nameof(CashVoucher.FileExtension));
    b.Property(x => x.FileSize).HasColumnName(nameof(CashVoucher.FileSize));
    b.Property(x => x.FinancialPeriodId).HasColumnName(nameof(CashVoucher.FinancialPeriodId));
    b.Property(x => x.FinancialPeriodName).HasColumnName(nameof(CashVoucher.FinancialPeriodName));
    b.Property(x => x.FinancialAccountId).HasColumnName(nameof(CashVoucher.FinancialAccountId));
    b.Property(x => x.FinancialAccountName).HasColumnName(nameof(CashVoucher.FinancialAccountName));
    b.Property(x => x.AssignSeatLocation).HasColumnName(nameof(CashVoucher.AssignSeatLocation));
    b.Property(x => x.CostCenterId).HasColumnName(nameof(CashVoucher.CostCenterId));
    b.Property(x => x.CostCenterName).HasColumnName(nameof(CashVoucher.CostCenterName));
});

        }
    }
}