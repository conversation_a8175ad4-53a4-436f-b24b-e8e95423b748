using Volo.Abp.EntityFrameworkCore.Modeling;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;

namespace EMPS.MoneyExchangeTransfersService.EntityFrameworkCore;

public static class MoneyExchangeTransfersServiceDbContextModelCreatingExtensions
{
    public static void ConfigureMoneyExchangeTransfersService(this ModelBuilder builder)
    {
        Check.NotNull(builder, nameof(builder));

        /* Configure your own tables/entities inside here */

        //builder.Entity<YourEntity>(b =>
        //{
        //    b.ToTable(MoneyExchangeTransfersServiceConsts.DbTablePrefix + "YourEntities", MoneyExchangeTransfersServiceConsts.DbSchema);
        //    b.ConfigureByConvention(); //auto configure for the base class props
        //    //...
        //});
        if (builder.IsHostDatabase())
        {
            builder.Entity<MoneyExchangeTransferCurrency>(b =>
{
    b.ToTable(MoneyExchangeTransfersServiceDbProperties.DbTablePrefix + "MoneyExchangeTransferCurrencies", MoneyExchangeTransfersServiceDbProperties.DbSchema);
    b.ConfigureByConvention();
    b.Property(x => x.No).HasColumnName(nameof(MoneyExchangeTransferCurrency.No));
    b.Property(x => x.Amount).HasColumnName(nameof(MoneyExchangeTransferCurrency.Amount));
    b.Property(x => x.CurrencyCode).HasColumnName(nameof(MoneyExchangeTransferCurrency.CurrencyCode));
    b.Property(x => x.CurrencyId).HasColumnName(nameof(MoneyExchangeTransferCurrency.CurrencyId));
    b.Property(x => x.ServicePointName).HasColumnName(nameof(MoneyExchangeTransferCurrency.ServicePointName));
    b.Property(x => x.ServicePointId).HasColumnName(nameof(MoneyExchangeTransferCurrency.ServicePointId));
    b.Property(x => x.TransactionIssueUserName).HasColumnName(nameof(MoneyExchangeTransferCurrency.TransactionIssueUserName));
    b.Property(x => x.TransactionIssueUserId).HasColumnName(nameof(MoneyExchangeTransferCurrency.TransactionIssueUserId));
    b.Property(x => x.TransactionIssueStaffName).HasColumnName(nameof(MoneyExchangeTransferCurrency.TransactionIssueStaffName));
    b.Property(x => x.TransactionIssueStaffId).HasColumnName(nameof(MoneyExchangeTransferCurrency.TransactionIssueStaffId));
    b.Property(x => x.TransactionIssueSeatId).HasColumnName(nameof(MoneyExchangeTransferCurrency.TransactionIssueSeatId));
    b.Property(x => x.IsApproved).HasColumnName(nameof(MoneyExchangeTransferCurrency.IsApproved));
    b.Property(x => x.ApprovedByUserName).HasColumnName(nameof(MoneyExchangeTransferCurrency.ApprovedByUserName));
    b.Property(x => x.ApprovedByUserId).HasColumnName(nameof(MoneyExchangeTransferCurrency.ApprovedByUserId));
    b.Property(x => x.ApprovalDate).HasColumnName(nameof(MoneyExchangeTransferCurrency.ApprovalDate));
    b.Property(x => x.ApprovalDateTime).HasColumnName(nameof(MoneyExchangeTransferCurrency.ApprovalDateTime));
    b.Property(x => x.Notes).HasColumnName(nameof(MoneyExchangeTransferCurrency.Notes));
    b.Property(x => x.IsExecute).HasColumnName(nameof(MoneyExchangeTransferCurrency.IsExecute));
    b.Property(x => x.ExecutedByUserName).HasColumnName(nameof(MoneyExchangeTransferCurrency.ExecutedByUserName));
    b.Property(x => x.ExecutedByUserId).HasColumnName(nameof(MoneyExchangeTransferCurrency.ExecutedByUserId));
    b.Property(x => x.ExecutedByStaffName).HasColumnName(nameof(MoneyExchangeTransferCurrency.ExecutedByStaffName));
    b.Property(x => x.ExecutedByStaffId).HasColumnName(nameof(MoneyExchangeTransferCurrency.ExecutedByStaffId));
    b.Property(x => x.ExecutedBySeatId).HasColumnName(nameof(MoneyExchangeTransferCurrency.ExecutedBySeatId));
    b.Property(x => x.ExecuteDate).HasColumnName(nameof(MoneyExchangeTransferCurrency.ExecuteDate));
    b.Property(x => x.ExecuteDateTime).HasColumnName(nameof(MoneyExchangeTransferCurrency.ExecuteDateTime));
    b.Property(x => x.TransferStatus).HasColumnName(nameof(MoneyExchangeTransferCurrency.TransferStatus));
    b.Property(x => x.TransferType).HasColumnName(nameof(MoneyExchangeTransferCurrency.TransferType));
});

        }
    }
}