<Project Sdk="Microsoft.NET.Sdk.Razor">
	<!--Suite code generation requirement: Microsoft.NET.Sdk.BlazorWebAssembly / do not remove this line-->
	<Import Project="..\..\..\..\common.props" />

	<PropertyGroup>
		<TargetFramework>net7.0</TargetFramework>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
    <PackageReference Include="Blazorise.LoadingIndicator" Version="1.4.1" />
    <PackageReference Include="Volo.Abp.RemoteServices" Version="7.3.2" />

    <PackageReference Include="Volo.Abp.AutoMapper" Version="7.3.2" />
		<PackageReference Include="Volo.Abp.AspNetCore.Components.Web.Theming" Version="7.3.2" />
		<ProjectReference Include="..\EMPS.MoneyExchangeTransfersService.Application.Contracts\EMPS.MoneyExchangeTransfersService.Application.Contracts.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Pages\MoneyExchangeTransfersService\ServicePointToCompanyTransferPages\" />
	</ItemGroup>
</Project>
	