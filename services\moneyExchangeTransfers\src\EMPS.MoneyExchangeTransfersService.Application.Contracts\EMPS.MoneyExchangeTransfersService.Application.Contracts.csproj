﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\..\..\common.props" />

  <PropertyGroup>
    <TargetFrameworks>net7.0</TargetFrameworks>
    <Nullable>enable</Nullable>
    <RootNamespace>EMPS.MoneyExchangeTransfersService</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Ddd.Application.Contracts" Version="7.3.2" />
    <PackageReference Include="Volo.Abp.Authorization" Version="7.3.2" />
    <ProjectReference Include="..\EMPS.MoneyExchangeTransfersService.Domain.Shared\EMPS.MoneyExchangeTransfersService.Domain.Shared.csproj" />
    <ProjectReference Include="..\..\..\company\src\EMPS.CompanyService.Application.Contracts\EMPS.CompanyService.Application.Contracts.csproj" />
    <ProjectReference Include="..\..\..\moneyExchangeLedger\src\EMPS.MoneyExchangeLedgerService.Application.Contracts\EMPS.MoneyExchangeLedgerService.Application.Contracts.csproj" />


  </ItemGroup>


</Project>