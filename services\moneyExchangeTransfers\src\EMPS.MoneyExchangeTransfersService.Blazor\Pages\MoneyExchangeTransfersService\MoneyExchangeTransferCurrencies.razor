@page "/MoneyExchangeTransfersService/MoneyExchangeTransferCurrencies"



@attribute [Authorize(MoneyExchangeTransfersServicePermissions.MoneyExchangeTransferCurrencies.Default)]
@using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
@using EMPS.MoneyExchangeTransfersService.Localization
@using EMPS.MoneyExchangeTransfersService.Shared
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Components.Web
@using Blazorise
@using Blazorise.Components
@using Blazorise.DataGrid
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using EMPS.MoneyExchangeTransfersService.Permissions



@inherits MoneyExchangeTransfersServiceComponentBase
@inject IMoneyExchangeTransferCurrenciesAppService MoneyExchangeTransferCurrenciesAppService
@inject IUiMessageService UiMessageService


@using EMPS.MoneyExchangeTransfersService

@using EMPS.MoneyExchangeTransfersService


@* ************************* PAGE HEADER ************************* *@
<PageHeader Title="@L["MoneyExchangeTransferCurrencies"]" BreadcrumbItems="BreadcrumbItems" Toolbar="Toolbar">

</PageHeader>

@* ************************* SEARCH ************************* *@
<Card>
    <CardBody>
        <Form id="MoneyExchangeTransferCurrencySearchForm" class="mb-3">
            <Addons>
                <Addon AddonType="AddonType.Body">
                    <TextEdit @bind-Text="@Filter.FilterText"
                              Autofocus="true"
                              Placeholder="@L["Search"]">
                    </TextEdit>
                </Addon>
                <Addon AddonType="AddonType.End">
                    <SubmitButton Form="MoneyExchangeTransferCurrencySearchForm" Clicked="GetMoneyExchangeTransferCurrenciesAsync">
                        <Icon Name="IconName.Search" Class="me-1"></Icon>@L["Search"]
                    </SubmitButton>
                </Addon>
            </Addons>
        </Form>
    </CardBody>
</Card>

@* ************************* DATA GRID ************************* *@
<Card>
    <CardBody>
        <DataGrid TItem="MoneyExchangeTransferCurrencyDto"
                  Data="MoneyExchangeTransferCurrencyList"
                  ReadData="OnDataGridReadAsync"
                  TotalItems="TotalCount"
                  ShowPager="true"
                  Responsive="true"
                  PageSize="PageSize">
            <DataGridColumns>
                <DataGridEntityActionsColumn TItem="MoneyExchangeTransferCurrencyDto" @ref="@EntityActionsColumn">
                    <DisplayTemplate>
                        <EntityActions TItem="MoneyExchangeTransferCurrencyDto" EntityActionsColumn="@EntityActionsColumn">
                            <EntityAction TItem="MoneyExchangeTransferCurrencyDto"
                                          Visible="@CanEditMoneyExchangeTransferCurrency"
                                          Clicked="async () => await OpenEditMoneyExchangeTransferCurrencyModalAsync(context)"
                                          Text="@L["Edit"]"></EntityAction>
                            <EntityAction TItem="MoneyExchangeTransferCurrencyDto"
                                          Visible="@CanDeleteMoneyExchangeTransferCurrency"
                                          Clicked="() => DeleteMoneyExchangeTransferCurrencyAsync(context)"
                                          ConfirmationMessage="@(()=> L["DeleteConfirmationMessage"])"
                                          Text="@L["Delete"]"></EntityAction>
                        </EntityActions>
                    </DisplayTemplate>
                </DataGridEntityActionsColumn>
               
              <DataGridColumn TItem="MoneyExchangeTransferCurrencyDto"
                      Field="No"
                      Caption="@L["No"]">
              </DataGridColumn>

              <DataGridColumn TItem="MoneyExchangeTransferCurrencyDto"
                      Field="Amount"
                      Caption="@L["Amount"]">
              </DataGridColumn>

              <DataGridColumn TItem="MoneyExchangeTransferCurrencyDto"
                      Field="CurrencyCode"
                      Caption="@L["CurrencyCode"]">
              </DataGridColumn>



              <DataGridColumn TItem="MoneyExchangeTransferCurrencyDto"
                      Field="ServicePointName"
                      Caption="@L["ServicePointName"]">
              </DataGridColumn>



              <DataGridColumn TItem="MoneyExchangeTransferCurrencyDto"
                      Field="TransactionIssueUserName"
                      Caption="@L["TransactionIssueUserName"]">
              </DataGridColumn>



              <DataGridColumn TItem="MoneyExchangeTransferCurrencyDto"
                      Field="TransactionIssueStaffName"
                      Caption="@L["TransactionIssueStaffName"]">
              </DataGridColumn>





              <DataGridColumn TItem="MoneyExchangeTransferCurrencyDto"
                      Field="IsApproved"
                      Caption="@L["IsApproved"]">
                    <DisplayTemplate>
                        @if (context.IsApproved)
                        {
                            <Icon TextColor="TextColor.Success" Name="@IconName.Check" />
                        }
                        else
                        {
                            <Icon TextColor="TextColor.Danger" Name="@IconName.Times" />
                        }
                    </DisplayTemplate>
              </DataGridColumn>


              <DataGridColumn TItem="MoneyExchangeTransferCurrencyDto"
                      Field="ApprovedByUserName"
                      Caption="@L["ApprovedByUserName"]">
              </DataGridColumn>



              <DataGridColumn TItem="MoneyExchangeTransferCurrencyDto"
                      Field="ApprovalDate"
                      Caption="@L["ApprovalDate"]">
                  <DisplayTemplate>
                        @(context.ApprovalDate.HasValue ? context.ApprovalDate.Value.ToShortDateString() : string.Empty)
                  </DisplayTemplate>
              </DataGridColumn>

              <DataGridColumn TItem="MoneyExchangeTransferCurrencyDto"
                      Field="ApprovalDateTime"
                      Caption="@L["ApprovalDateTime"]">
                  <DisplayTemplate>
                        @(context.ApprovalDateTime.HasValue ? context.ApprovalDateTime.Value.ToShortDateString() : string.Empty)
                  </DisplayTemplate>
              </DataGridColumn>

              <DataGridColumn TItem="MoneyExchangeTransferCurrencyDto"
                      Field="Notes"
                      Caption="@L["Notes"]">
              </DataGridColumn>

              <DataGridColumn TItem="MoneyExchangeTransferCurrencyDto"
                      Field="IsExecute"
                      Caption="@L["IsExecute"]">
                    <DisplayTemplate>
                        @if (context.IsExecute)
                        {
                            <Icon TextColor="TextColor.Success" Name="@IconName.Check" />
                        }
                        else
                        {
                            <Icon TextColor="TextColor.Danger" Name="@IconName.Times" />
                        }
                    </DisplayTemplate>
              </DataGridColumn>


              <DataGridColumn TItem="MoneyExchangeTransferCurrencyDto"
                      Field="ExecutedByUserName"
                      Caption="@L["ExecutedByUserName"]">
              </DataGridColumn>



              <DataGridColumn TItem="MoneyExchangeTransferCurrencyDto"
                      Field="ExecutedByStaffName"
                      Caption="@L["ExecutedByStaffName"]">
              </DataGridColumn>





              <DataGridColumn TItem="MoneyExchangeTransferCurrencyDto"
                      Field="ExecuteDate"
                      Caption="@L["ExecuteDate"]">
                  <DisplayTemplate>
                        @(context.ExecuteDate.HasValue ? context.ExecuteDate.Value.ToShortDateString() : string.Empty)
                  </DisplayTemplate>
              </DataGridColumn>

              <DataGridColumn TItem="MoneyExchangeTransferCurrencyDto"
                      Field="ExecuteDateTime"
                      Caption="@L["ExecuteDateTime"]">
                  <DisplayTemplate>
                        @(context.ExecuteDateTime.HasValue ? context.ExecuteDateTime.Value.ToShortDateString() : string.Empty)
                  </DisplayTemplate>
              </DataGridColumn>

              <DataGridColumn TItem="MoneyExchangeTransferCurrencyDto"
                      Field="TransferStatus"
                      Caption="@L["TransferStatus"]">
                    <DisplayTemplate>
                        @L[$"Enum:MoneyExchangeTransferStatus.{context.TransferStatus.ToString("d")}"]
                    </DisplayTemplate>
              </DataGridColumn>


              <DataGridColumn TItem="MoneyExchangeTransferCurrencyDto"
                      Field="TransferType"
                      Caption="@L["TransferType"]">
                    <DisplayTemplate>
                        @L[$"Enum:MoneyExchangeTransferType.{context.TransferType.ToString("d")}"]
                    </DisplayTemplate>
              </DataGridColumn>


            </DataGridColumns>
        </DataGrid>
    </CardBody>
</Card>

@* ************************* CREATE MODAL ************************* *@
<Modal @ref="CreateMoneyExchangeTransferCurrencyModal" Closing="@CreateMoneyExchangeTransferCurrencyModal.CancelClosingModalWhenFocusLost">
    <ModalContent Centered="true">
        <Form id="CreateMoneyExchangeTransferCurrencyForm">
            <ModalHeader>
                <ModalTitle>@L["NewMoneyExchangeTransferCurrency"]</ModalTitle>
                <CloseButton Clicked="CloseCreateMoneyExchangeTransferCurrencyModalAsync" />
            </ModalHeader>
            <ModalBody>
                <Validations @ref="@NewMoneyExchangeTransferCurrencyValidations"
                            Mode="ValidationMode.Auto"
                            Model="@NewMoneyExchangeTransferCurrency"
                            ValidateOnLoad="false">
                     
                    
                    <Validation>
                        <Field>
                            <FieldLabel>@L["No"]</FieldLabel>
                            <TextEdit @bind-Text="@NewMoneyExchangeTransferCurrency.No"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["Amount"]</FieldLabel>
                            <NumericPicker TValue="double" @bind-Value="@NewMoneyExchangeTransferCurrency.Amount"   >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                             </NumericPicker>
                        </Field>
                     </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["CurrencyCode"]</FieldLabel>
                            <TextEdit @bind-Text="@NewMoneyExchangeTransferCurrency.CurrencyCode"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>




                    <Validation>
                        <Field>
                            <FieldLabel>@L["ServicePointName"]</FieldLabel>
                            <TextEdit @bind-Text="@NewMoneyExchangeTransferCurrency.ServicePointName"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>




                    <Validation>
                        <Field>
                            <FieldLabel>@L["TransactionIssueUserName"]</FieldLabel>
                            <TextEdit @bind-Text="@NewMoneyExchangeTransferCurrency.TransactionIssueUserName"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>




                    <Validation>
                        <Field>
                            <FieldLabel>@L["TransactionIssueStaffName"]</FieldLabel>
                            <TextEdit @bind-Text="@NewMoneyExchangeTransferCurrency.TransactionIssueStaffName"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>






                    <Field>
                        <Check TValue="bool" @bind-Checked="@NewMoneyExchangeTransferCurrency.IsApproved" >@L["IsApproved"]</Check>
                    </Field>

                    <Validation>
                        <Field>
                            <FieldLabel>@L["ApprovedByUserName"]</FieldLabel>
                            <TextEdit @bind-Text="@NewMoneyExchangeTransferCurrency.ApprovedByUserName"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>




                    <Validation>
                        <Field>
                            <FieldLabel>@L["ApprovalDate"]</FieldLabel>
                            <DateEdit TValue="DateTime?" InputMode="DateInputMode.DateTime" @bind-Date="@NewMoneyExchangeTransferCurrency.ApprovalDate" >
                               <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </DateEdit>
                        </Field>
                    </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["ApprovalDateTime"]</FieldLabel>
                            <DateEdit TValue="DateTime?" InputMode="DateInputMode.DateTime" @bind-Date="@NewMoneyExchangeTransferCurrency.ApprovalDateTime" >
                               <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </DateEdit>
                        </Field>
                    </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["Notes"]</FieldLabel>
                            <MemoEdit @bind-Text="@NewMoneyExchangeTransferCurrency.Notes"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </MemoEdit>
                        </Field>
                    </Validation>


                    <Field>
                        <Check TValue="bool" @bind-Checked="@NewMoneyExchangeTransferCurrency.IsExecute" >@L["IsExecute"]</Check>
                    </Field>

                    <Validation>
                        <Field>
                            <FieldLabel>@L["ExecutedByUserName"]</FieldLabel>
                            <TextEdit @bind-Text="@NewMoneyExchangeTransferCurrency.ExecutedByUserName"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>




                    <Validation>
                        <Field>
                            <FieldLabel>@L["ExecutedByStaffName"]</FieldLabel>
                            <TextEdit @bind-Text="@NewMoneyExchangeTransferCurrency.ExecutedByStaffName"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>






                    <Validation>
                        <Field>
                            <FieldLabel>@L["ExecuteDate"]</FieldLabel>
                            <DateEdit TValue="DateTime?" InputMode="DateInputMode.DateTime" @bind-Date="@NewMoneyExchangeTransferCurrency.ExecuteDate" >
                               <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </DateEdit>
                        </Field>
                    </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["ExecuteDateTime"]</FieldLabel>
                            <DateEdit TValue="DateTime?" InputMode="DateInputMode.DateTime" @bind-Date="@NewMoneyExchangeTransferCurrency.ExecuteDateTime" >
                               <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </DateEdit>
                        </Field>
                    </Validation>


                    <Field>
                        <FieldLabel>@L["TransferStatus"]</FieldLabel>
                        <Select TValue="MoneyExchangeTransferStatus" @bind-SelectedValue="@NewMoneyExchangeTransferCurrency.TransferStatus" >
                            @foreach (var itemValue in Enum.GetValues(typeof(MoneyExchangeTransferStatus)))
                            {
                                <SelectItem TValue="MoneyExchangeTransferStatus" Value="@((MoneyExchangeTransferStatus) itemValue)">
                                    @L[$"Enum:MoneyExchangeTransferStatus.{((MoneyExchangeTransferStatus) itemValue).ToString("d")}"]
                                </SelectItem>
                            }
                        </Select>
                    </Field>


                    <Field>
                        <FieldLabel>@L["TransferType"]</FieldLabel>
                        <Select TValue="MoneyExchangeTransferType" @bind-SelectedValue="@NewMoneyExchangeTransferCurrency.TransferType" >
                            @foreach (var itemValue in Enum.GetValues(typeof(MoneyExchangeTransferType)))
                            {
                                <SelectItem TValue="MoneyExchangeTransferType" Value="@((MoneyExchangeTransferType) itemValue)">
                                    @L[$"Enum:MoneyExchangeTransferType.{((MoneyExchangeTransferType) itemValue).ToString("d")}"]
                                </SelectItem>
                            }
                        </Select>
                    </Field>


                    
                    
                </Validations>
            </ModalBody>
            <ModalFooter>
                <Button Color="Color.Secondary"
                        Clicked="CloseCreateMoneyExchangeTransferCurrencyModalAsync">
                    @L["Cancel"]
                </Button>
                <SubmitButton Form="CreateMoneyExchangeTransferCurrencyForm" Clicked="CreateMoneyExchangeTransferCurrencyAsync" />
            </ModalFooter>
        </Form>
    </ModalContent>
</Modal>

@* ************************* EDIT MODAL ************************* *@
<Modal @ref="EditMoneyExchangeTransferCurrencyModal" Closing="@EditMoneyExchangeTransferCurrencyModal.CancelClosingModalWhenFocusLost">
    <ModalContent Centered="true">
        <Form id="EditMoneyExchangeTransferCurrencyForm">
            <ModalHeader>
                <ModalTitle>@L["Update"]</ModalTitle>
                <CloseButton Clicked="CloseEditMoneyExchangeTransferCurrencyModalAsync" />
            </ModalHeader>
            <ModalBody>
                <Validations @ref="@EditingMoneyExchangeTransferCurrencyValidations"
                            Mode="ValidationMode.Auto"
                            Model="@EditingMoneyExchangeTransferCurrency"
                            ValidateOnLoad="false">
                     
                    
                    <Validation>
                        <Field>
                            <FieldLabel>@L["No"]</FieldLabel>
                            <TextEdit @bind-Text="@EditingMoneyExchangeTransferCurrency.No"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["Amount"]</FieldLabel>
                            <NumericPicker TValue="double" @bind-Value="@EditingMoneyExchangeTransferCurrency.Amount"   >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                             </NumericPicker>
                        </Field>
                     </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["CurrencyCode"]</FieldLabel>
                            <TextEdit @bind-Text="@EditingMoneyExchangeTransferCurrency.CurrencyCode"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>




                    <Validation>
                        <Field>
                            <FieldLabel>@L["ServicePointName"]</FieldLabel>
                            <TextEdit @bind-Text="@EditingMoneyExchangeTransferCurrency.ServicePointName"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>




                    <Validation>
                        <Field>
                            <FieldLabel>@L["TransactionIssueUserName"]</FieldLabel>
                            <TextEdit @bind-Text="@EditingMoneyExchangeTransferCurrency.TransactionIssueUserName"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>




                    <Validation>
                        <Field>
                            <FieldLabel>@L["TransactionIssueStaffName"]</FieldLabel>
                            <TextEdit @bind-Text="@EditingMoneyExchangeTransferCurrency.TransactionIssueStaffName"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>






                    <Field>
                        <Check TValue="bool" @bind-Checked="@EditingMoneyExchangeTransferCurrency.IsApproved" >@L["IsApproved"]</Check>
                    </Field>

                    <Validation>
                        <Field>
                            <FieldLabel>@L["ApprovedByUserName"]</FieldLabel>
                            <TextEdit @bind-Text="@EditingMoneyExchangeTransferCurrency.ApprovedByUserName"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>




                    <Validation>
                        <Field>
                            <FieldLabel>@L["ApprovalDate"]</FieldLabel>
                            <DateEdit TValue="DateTime?" InputMode="DateInputMode.DateTime" @bind-Date="@EditingMoneyExchangeTransferCurrency.ApprovalDate" >
                               <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </DateEdit>
                        </Field>
                    </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["ApprovalDateTime"]</FieldLabel>
                            <DateEdit TValue="DateTime?" InputMode="DateInputMode.DateTime" @bind-Date="@EditingMoneyExchangeTransferCurrency.ApprovalDateTime" >
                               <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </DateEdit>
                        </Field>
                    </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["Notes"]</FieldLabel>
                            <MemoEdit @bind-Text="@EditingMoneyExchangeTransferCurrency.Notes"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </MemoEdit>
                        </Field>
                    </Validation>


                    <Field>
                        <Check TValue="bool" @bind-Checked="@EditingMoneyExchangeTransferCurrency.IsExecute" >@L["IsExecute"]</Check>
                    </Field>

                    <Validation>
                        <Field>
                            <FieldLabel>@L["ExecutedByUserName"]</FieldLabel>
                            <TextEdit @bind-Text="@EditingMoneyExchangeTransferCurrency.ExecutedByUserName"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>




                    <Validation>
                        <Field>
                            <FieldLabel>@L["ExecutedByStaffName"]</FieldLabel>
                            <TextEdit @bind-Text="@EditingMoneyExchangeTransferCurrency.ExecutedByStaffName"  >
                                <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </TextEdit>
                        </Field>
                    </Validation>






                    <Validation>
                        <Field>
                            <FieldLabel>@L["ExecuteDate"]</FieldLabel>
                            <DateEdit TValue="DateTime?" InputMode="DateInputMode.DateTime" @bind-Date="@EditingMoneyExchangeTransferCurrency.ExecuteDate" >
                               <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </DateEdit>
                        </Field>
                    </Validation>


                    <Validation>
                        <Field>
                            <FieldLabel>@L["ExecuteDateTime"]</FieldLabel>
                            <DateEdit TValue="DateTime?" InputMode="DateInputMode.DateTime" @bind-Date="@EditingMoneyExchangeTransferCurrency.ExecuteDateTime" >
                               <Feedback>
                                    <ValidationError />
                                </Feedback>
                            </DateEdit>
                        </Field>
                    </Validation>


                    <Field>
                        <FieldLabel>@L["TransferStatus"]</FieldLabel>
                        <Select TValue="MoneyExchangeTransferStatus" @bind-SelectedValue="@EditingMoneyExchangeTransferCurrency.TransferStatus" >
                            @foreach (var itemValue in Enum.GetValues(typeof(MoneyExchangeTransferStatus)))
                            {
                                <SelectItem TValue="MoneyExchangeTransferStatus" Value="@((MoneyExchangeTransferStatus) itemValue)">
                                    @L[$"Enum:MoneyExchangeTransferStatus.{((MoneyExchangeTransferStatus) itemValue).ToString("d")}"]
                                </SelectItem>
                            }
                        </Select>
                    </Field>


                    <Field>
                        <FieldLabel>@L["TransferType"]</FieldLabel>
                        <Select TValue="MoneyExchangeTransferType" @bind-SelectedValue="@EditingMoneyExchangeTransferCurrency.TransferType" >
                            @foreach (var itemValue in Enum.GetValues(typeof(MoneyExchangeTransferType)))
                            {
                                <SelectItem TValue="MoneyExchangeTransferType" Value="@((MoneyExchangeTransferType) itemValue)">
                                    @L[$"Enum:MoneyExchangeTransferType.{((MoneyExchangeTransferType) itemValue).ToString("d")}"]
                                </SelectItem>
                            }
                        </Select>
                    </Field>


                    
                    
                </Validations>
            </ModalBody>
            <ModalFooter>
                <Button Color="Color.Secondary"
                        Clicked="CloseEditMoneyExchangeTransferCurrencyModalAsync">
                    @L["Cancel"]
                </Button>
                <SubmitButton Form="CreateMoneyExchangeTransferCurrencyForm" Clicked="UpdateMoneyExchangeTransferCurrencyAsync" />
            </ModalFooter>
        </Form>
    </ModalContent>
</Modal>
