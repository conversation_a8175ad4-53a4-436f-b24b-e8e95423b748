﻿using System;
using Microsoft.Extensions.Logging;
using EMPS.MoneyExchangeTransfersService.EntityFrameworkCore;
using EMPS.Shared.Hosting.Microservices.DbMigrations.EfCore;
using Volo.Abp.DistributedLocking;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Uow;

namespace EMPS.MoneyExchangeTransfersService.DbMigrations;

public class MoneyExchangeTransfersServiceDatabaseMigrationChecker : PendingEfCoreMigrationsChecker<MoneyExchangeTransfersServiceDbContext>
{
    public MoneyExchangeTransfersServiceDatabaseMigrationChecker(
        ILoggerFactory loggerFactory,
        IUnitOfWorkManager unitOfWorkManager,
        IServiceProvider serviceProvider,
        ICurrentTenant currentTenant,
        IDistributedEventBus distributedEventBus,
        IAbpDistributedLock abpDistributedLock)
        : base(
            loggerFactory,
            unitOfWorkManager,
            serviceProvider,
            currentTenant,
            distributedEventBus,
            abpDistributedLock,
            MoneyExchangeTransfersServiceDbProperties.ConnectionStringName)
    {

    }
}
