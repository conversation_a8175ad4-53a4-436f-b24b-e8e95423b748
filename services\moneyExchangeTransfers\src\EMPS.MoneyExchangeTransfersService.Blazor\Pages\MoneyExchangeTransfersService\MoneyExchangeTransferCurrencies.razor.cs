using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazorise;
using Blazorise.DataGrid;
using Volo.Abp.BlazoriseUI.Components;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.MoneyExchangeTransfersService.Shared;

namespace EMPS.MoneyExchangeTransfersService.Blazor.Pages.MoneyExchangeTransfersService
{
    public partial class MoneyExchangeTransferCurrencies
    {
        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar {get;} = new PageToolbar();
        private IReadOnlyList<MoneyExchangeTransferCurrencyDto> MoneyExchangeTransferCurrencyList { get; set; }
        private int PageSize { get; } = LimitedResultRequestDto.DefaultMaxResultCount;
        private int CurrentPage { get; set; } = 1;
        private string CurrentSorting { get; set; } = string.Empty;
        private int TotalCount { get; set; }
        private bool CanCreateMoneyExchangeTransferCurrency { get; set; }
        private bool CanEditMoneyExchangeTransferCurrency { get; set; }
        private bool CanDeleteMoneyExchangeTransferCurrency { get; set; }
        private MoneyExchangeTransferCurrencyCreateDto NewMoneyExchangeTransferCurrency { get; set; }
        private Validations NewMoneyExchangeTransferCurrencyValidations { get; set; } = new();
        private MoneyExchangeTransferCurrencyUpdateDto EditingMoneyExchangeTransferCurrency { get; set; }
        private Validations EditingMoneyExchangeTransferCurrencyValidations { get; set; } = new();
        private Guid EditingMoneyExchangeTransferCurrencyId { get; set; }
        private Modal CreateMoneyExchangeTransferCurrencyModal { get; set; } = new();
        private Modal EditMoneyExchangeTransferCurrencyModal { get; set; } = new();
        private GetMoneyExchangeTransferCurrenciesInput Filter { get; set; }
        private DataGridEntityActionsColumn<MoneyExchangeTransferCurrencyDto> EntityActionsColumn { get; set; } = new();
        protected string SelectedCreateTab = "moneyExchangeTransferCurrency-create-tab";
        protected string SelectedEditTab = "moneyExchangeTransferCurrency-edit-tab";
        
        public MoneyExchangeTransferCurrencies()
        {
            NewMoneyExchangeTransferCurrency = new MoneyExchangeTransferCurrencyCreateDto();
            EditingMoneyExchangeTransferCurrency = new MoneyExchangeTransferCurrencyUpdateDto();
            Filter = new GetMoneyExchangeTransferCurrenciesInput
            {
                MaxResultCount = PageSize,
                SkipCount = (CurrentPage - 1) * PageSize,
                Sorting = CurrentSorting
            };
            MoneyExchangeTransferCurrencyList = new List<MoneyExchangeTransferCurrencyDto>();
        }

        protected override async Task OnInitializedAsync()
        {
            await SetToolbarItemsAsync();
            await SetBreadcrumbItemsAsync();
            await SetPermissionsAsync();
        }

        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:MoneyExchangeTransferCurrencies"]));
            return ValueTask.CompletedTask;
        }

        protected virtual ValueTask SetToolbarItemsAsync()
        {
            
            
            Toolbar.AddButton(L["NewMoneyExchangeTransferCurrency"], async () =>
            {
                await OpenCreateMoneyExchangeTransferCurrencyModalAsync();
            }, IconName.Add, requiredPolicyName: MoneyExchangeTransfersServicePermissions.MoneyExchangeTransferCurrencies.Create);

            return ValueTask.CompletedTask;
        }

        private async Task SetPermissionsAsync()
        {
            CanCreateMoneyExchangeTransferCurrency = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.MoneyExchangeTransferCurrencies.Create);
            CanEditMoneyExchangeTransferCurrency = await AuthorizationService
                            .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.MoneyExchangeTransferCurrencies.Edit);
            CanDeleteMoneyExchangeTransferCurrency = await AuthorizationService
                            .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.MoneyExchangeTransferCurrencies.Delete);
        }

        private async Task GetMoneyExchangeTransferCurrenciesAsync()
        {
            Filter.MaxResultCount = PageSize;
            Filter.SkipCount = (CurrentPage - 1) * PageSize;
            Filter.Sorting = CurrentSorting;

            var result = await MoneyExchangeTransferCurrenciesAppService.GetListAsync(Filter);
            MoneyExchangeTransferCurrencyList = result.Items;
            TotalCount = (int)result.TotalCount;
        }

        protected virtual async Task SearchAsync()
        {
            CurrentPage = 1;
            await GetMoneyExchangeTransferCurrenciesAsync();
            await InvokeAsync(StateHasChanged);
        }

        private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<MoneyExchangeTransferCurrencyDto> e)
        {
            CurrentSorting = e.Columns
                .Where(c => c.SortDirection != SortDirection.Default)
                .Select(c => c.Field + (c.SortDirection == SortDirection.Descending ? " DESC" : ""))
                .JoinAsString(",");
            CurrentPage = e.Page;
            await GetMoneyExchangeTransferCurrenciesAsync();
            await InvokeAsync(StateHasChanged);
        }

        private async Task OpenCreateMoneyExchangeTransferCurrencyModalAsync()
        {
            NewMoneyExchangeTransferCurrency = new MoneyExchangeTransferCurrencyCreateDto{
                ApprovalDate = DateTime.Now,
ApprovalDateTime = DateTime.Now,
ExecuteDate = DateTime.Now,
ExecuteDateTime = DateTime.Now,

                
            };
            await NewMoneyExchangeTransferCurrencyValidations.ClearAll();
            await CreateMoneyExchangeTransferCurrencyModal.Show();
        }

        private async Task CloseCreateMoneyExchangeTransferCurrencyModalAsync()
        {
            NewMoneyExchangeTransferCurrency = new MoneyExchangeTransferCurrencyCreateDto{
                ApprovalDate = DateTime.Now,
ApprovalDateTime = DateTime.Now,
ExecuteDate = DateTime.Now,
ExecuteDateTime = DateTime.Now,

                
            };
            await CreateMoneyExchangeTransferCurrencyModal.Hide();
        }

        private async Task OpenEditMoneyExchangeTransferCurrencyModalAsync(MoneyExchangeTransferCurrencyDto input)
        {
            var moneyExchangeTransferCurrency = await MoneyExchangeTransferCurrenciesAppService.GetAsync(input.Id);
            
            EditingMoneyExchangeTransferCurrencyId = moneyExchangeTransferCurrency.Id;
            EditingMoneyExchangeTransferCurrency = ObjectMapper.Map<MoneyExchangeTransferCurrencyDto, MoneyExchangeTransferCurrencyUpdateDto>(moneyExchangeTransferCurrency);
            await EditingMoneyExchangeTransferCurrencyValidations.ClearAll();
            await EditMoneyExchangeTransferCurrencyModal.Show();
        }

        private async Task DeleteMoneyExchangeTransferCurrencyAsync(MoneyExchangeTransferCurrencyDto input)
        {
            await MoneyExchangeTransferCurrenciesAppService.DeleteAsync(input.Id);
            await GetMoneyExchangeTransferCurrenciesAsync();
        }

        private async Task CreateMoneyExchangeTransferCurrencyAsync()
        {
            try
            {
                if (await NewMoneyExchangeTransferCurrencyValidations.ValidateAll() == false)
                {
                    return;
                }

                await MoneyExchangeTransferCurrenciesAppService.CreateAsync(NewMoneyExchangeTransferCurrency);
                await GetMoneyExchangeTransferCurrenciesAsync();
                await CloseCreateMoneyExchangeTransferCurrencyModalAsync();
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex);
            }
        }

        private async Task CloseEditMoneyExchangeTransferCurrencyModalAsync()
        {
            await EditMoneyExchangeTransferCurrencyModal.Hide();
        }

        private async Task UpdateMoneyExchangeTransferCurrencyAsync()
        {
            try
            {
                if (await EditingMoneyExchangeTransferCurrencyValidations.ValidateAll() == false)
                {
                    return;
                }

                await MoneyExchangeTransferCurrenciesAppService.UpdateAsync(EditingMoneyExchangeTransferCurrencyId, EditingMoneyExchangeTransferCurrency);
                await GetMoneyExchangeTransferCurrenciesAsync();
                await EditMoneyExchangeTransferCurrencyModal.Hide();                
            }
            catch (Exception ex)
            {
                await HandleErrorAsync(ex);
            }
        }

        private void OnSelectedCreateTabChanged(string name)
        {
            SelectedCreateTab = name;
        }

        private void OnSelectedEditTabChanged(string name)
        {
            SelectedEditTab = name;
        }
        

    }
}
