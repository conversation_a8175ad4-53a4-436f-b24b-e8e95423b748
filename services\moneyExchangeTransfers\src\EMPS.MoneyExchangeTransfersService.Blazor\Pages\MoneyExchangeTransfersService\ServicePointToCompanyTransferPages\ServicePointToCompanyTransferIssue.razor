@page "/ServicePointToCompanyTransferIssue"
@using Volo.Abp.BlazoriseUI.Components
@using Volo.Abp.ObjectMapping
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@using Microsoft.AspNetCore.Components
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.Http.Client
@using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
@using EMPS.MoneyExchangeTransfersService.Localization
@using EMPS.MoneyExchangeTransfersService.Shared
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Authorization
@using Blazorise.DataGrid
@using Volo.Abp.BlazoriseUI
@using Volo.Abp.Users
@using Microsoft.AspNetCore.Components.Web




@inherits MoneyExchangeTransfersServiceComponentBase
@inject IUiMessageService UiMessageService


@using EMPS.MoneyExchangeTransfersService

<PageHeader Title="@L["ServicePointToCompanyTransferIssue"]" BreadcrumbItems="BreadcrumbItems" Toolbar="Toolbar" />

<DataGrid TItem="string" Data="stringList" ReadData="OnDataGridReadAsync" hidden />



<Div @onkeydown="HandleKeyDown">
    <EMPS.MoneyExchangeTransfersService.Blazor.Components.ServicePointToCompanyForm @ref="ServicePointToCompanyFormRef"
        OnServicePointToCompanyLoaded="HandleTransfareLoad" IsReadOnlyMode="IsReadOnly"   />
</Div>

