

// Ignore Spelling: Blazor

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Blazorise;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.JSInterop;
using Blazorise.LoadingIndicator;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.CompanyService.Currencies;
using EMPS.CompanyService.ServicePoints;
using EMPS.CompanyService.Staffs;




namespace EMPS.MoneyExchangeTransfersService.Blazor.Components
{
    public partial class CompanyToServicePointForm
    {

        [Inject]
        private IJSRuntime JSRuntime { get; set; }
        [Inject]
        private ICompanyToServicePointTransferService CompanyToServicePointService { get; set; }
        [Inject]
        private IServicePointsAppService ServicePointsAppService { get; set; }

        [Parameter]
        public bool IsReadOnlyMode { get; set; }

        private bool CanDeleteCompanyToServicePoint { get; set; }
        private bool CanCreateOrUpdateCompanyToServicePoint { get; set; }

        [Parameter]
        public MoneyExchangeTransferCurrencyDto FormCompanyToServicePoint { get; set; }

        private Validations FormCompanyToServicePointValidations { get; set; } = new();

        private Guid EditingCompanyToServicePointId { get; set; }

        [Parameter]
        public Func<MoneyExchangeTransferCurrencyDto, Task> OnCompanyToServicePointLoaded { get; set; }

        public Func<Task<bool>> OnReset { get; set; }

        public bool IsCompanyToServicePointLoadedProp { get; private set; }

        public LoadingIndicator loadingIndicator;

        private string LastStatusBy { get; set; }
        private string LastStatusDateTime { get; set; }
        private IReadOnlyList<CurrencyDto> CurrenciesCollection { get; set; } = new List<CurrencyDto>();
        private StaffWithNavigationPropertiesDto StaffWithNavigation { get; set; } = new();
        private IReadOnlyList<StaffDto> CashiersCollection { get; set; } = new List<StaffDto>();
        private IReadOnlyList<ServicePointDto> ServicePointCollection { get; set; } = new List<ServicePointDto>();

        private async Task GetCurrenciesLookupAsync()
        {
            CurrenciesCollection = await CompanyToServicePointService.GetAllActiveCurrenciesAsync();
        }
        private async Task GetServicePointsLookupAsync()
        {
            ServicePointCollection = await CompanyToServicePointService.GetAllActiveServicePointAsync();
        }
        private async Task GetCashiersLookupAsync(Guid id)
        {
            CashiersCollection = await CompanyToServicePointService.GetAllCashierInServicePointAsync(id);
        }

        private void SetLastStatus()
        {

            LastStatusBy = "";
            LastStatusDateTime = "";
            if (FormCompanyToServicePoint == null) return;
            if (FormCompanyToServicePoint.TransactionIssueUserId != null && FormCompanyToServicePoint.TransactionIssueUserId != Guid.Empty)
            {
                LastStatusBy = L["CreatedByUserName"] + ": " + FormCompanyToServicePoint.TransactionIssueUserName;
                var lastDate = FormCompanyToServicePoint.LastModificationTime.HasValue ?
                    FormCompanyToServicePoint.LastModificationTime : FormCompanyToServicePoint.CreationTime;
                LastStatusDateTime = L["CreatedDate"] + ": " + lastDate;

            }
            if (FormCompanyToServicePoint.ApprovedByUserId != null && FormCompanyToServicePoint.ApprovedByUserId != Guid.Empty)
            {
                LastStatusBy = L["ApprovedByUser"] + ": " + FormCompanyToServicePoint.ApprovedByUserName;

                LastStatusDateTime = L["ApprovalDateTime"] + ": " + FormCompanyToServicePoint.ApprovalDateTime;

            }

            if (FormCompanyToServicePoint.IsExecute)
            {
                LastStatusBy = L["ExecutedByUserName"] + ": " + FormCompanyToServicePoint.ExecutedByUserName;
                LastStatusDateTime = L["ExecuteDate"] + ": " + FormCompanyToServicePoint.ExecuteDateTime; ;
            }



        }
        public CompanyToServicePointForm()
        {
            FormCompanyToServicePoint = new();
        }
        protected override async Task OnInitializedAsync()
        {

            await SetPermissionsAsync();
            await GetCurrenciesLookupAsync();

        }

        private async Task SetPermissionsAsync()
        {
            CanCreateOrUpdateCompanyToServicePoint = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Save);
            CanDeleteCompanyToServicePoint = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Delete);

        }
        public async Task<bool> ResetCompanyToServicePoint(bool forceReset = false)
        {
            if (!forceReset)
                if (!await Message.Confirm(L["ResetConfirmationMessage"])) return false;
            FormCompanyToServicePoint = new();
            StaffWithNavigation = new();

            SetLastStatus();
            StateHasChanged();
            IsCompanyToServicePointLoadedProp = false;
            return true;

        }
        public string? GetCompanyToServicePointNo()
        {
            return FormCompanyToServicePoint.No;
        }
        public Guid GetCompanyToServicePointId()
        {
            return FormCompanyToServicePoint.Id;
        }
        public bool IsCompanyToServicePointLoaded()
        {
            return IsCompanyToServicePointLoadedProp;

        }
        public bool IsFormReadOnly()
        {
            if (IsReadOnlyMode) return true;
            if (FormCompanyToServicePoint.IsApproved) return true;
            if (FormCompanyToServicePoint.IsExecute) return true;
            return false;
        }
        private async Task LoadCompanyToServicePoint()
        {

            var LoadResult = await GetLoadResult();
            if (LoadResult == null)
            {
                _ = Message.Error(L["CompanyToServicePointNotFound"]);
                return;
            }

            FormCompanyToServicePoint = LoadResult;


            OnCompanyToServicePointLoaded?.Invoke(LoadResult);
            IsCompanyToServicePointLoadedProp = true;

            SetLastStatus();
            StateHasChanged();

        }
        private async Task<MoneyExchangeTransferCurrencyDto?> GetLoadResult()
        {
            return await CompanyToServicePointService.LoadByNoAsync(FormCompanyToServicePoint.No!);
        }
        private async Task LoadModelByCompanyToServicePointNo(KeyboardEventArgs e)
        {
            await Task.Delay(50);

            if (e.Key != "Enter") return;

            if (string.IsNullOrEmpty(FormCompanyToServicePoint.No)) return;


            await PerformLoadCompanyToServicePoint();

            await Task.CompletedTask;

        }
        public async Task PerformLoadCompanyToServicePoint()
        {

            try
            {
                await loadingIndicator.Show();

                await LoadCompanyToServicePoint();
                StateHasChanged();


            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await loadingIndicator.Hide();

            }
        }
        public void ChangeEntity(MoneyExchangeTransferCurrencyDto CompanyToServicePoint)
        {
            this.FormCompanyToServicePoint = CompanyToServicePoint;
            IsCompanyToServicePointLoadedProp = true;
            SetLastStatus();
            StateHasChanged();
        }

        public async Task OnChangeCurrency(Guid? id)
        {
            FormCompanyToServicePoint.CurrencyId = id;
            if (FormCompanyToServicePoint.CurrencyId == null)
            {
                //rest all
            }
            //get avgCost for this currency 
            //method have 2 parameter currency id and cashier id 

            //avgCost = avgCost for currency
            StateHasChanged();

        }
        public async Task OnChangeServicePoint(Guid? id)
        {
            FormCompanyToServicePoint.ServicePointId = id;
            if (FormCompanyToServicePoint.ServicePointId == null)
            {
                CashiersCollection = new List<StaffDto>();
            }
            else
            {
                await GetCashiersLookupAsync(FormCompanyToServicePoint.ServicePointId!.Value);
            }

            StateHasChanged();

        }



    }
}
