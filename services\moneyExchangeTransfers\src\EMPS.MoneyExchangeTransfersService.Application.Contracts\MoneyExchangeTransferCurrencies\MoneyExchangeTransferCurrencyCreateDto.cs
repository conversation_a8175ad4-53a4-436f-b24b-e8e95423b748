using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
{
    public class MoneyExchangeTransferCurrencyCreateDto
    {
        public string? No { get; set; }
        public double Amount { get; set; }
        public  double AverageCost { get; set; }
        public  DateTime? TransactionDate { get; set; }
        public string? CurrencyCode { get; set; }
        public Guid? CurrencyId { get; set; }
        public string? ServicePointName { get; set; }
        public Guid? ServicePointId { get; set; }
        public string? TransactionIssueUserName { get; set; }
        public Guid? TransactionIssueUserId { get; set; }
        public string? TransactionIssueStaffName { get; set; }
        public Guid? TransactionIssueStaffId { get; set; }
        public Guid? TransactionIssueSeatId { get; set; }
        public bool IsApproved { get; set; } = false;
        public string? ApprovedByUserName { get; set; }
        public Guid? ApprovedByUserId { get; set; }
        public DateTime? ApprovalDate { get; set; }
        public DateTime? ApprovalDateTime { get; set; }
        public string? Notes { get; set; }
        public bool IsExecute { get; set; } = false;
        public string? ExecutedByUserName { get; set; }
        public Guid? ExecutedByUserId { get; set; }
        public string? ExecutedByStaffName { get; set; }
        public Guid? ExecutedByStaffId { get; set; }
        public Guid? ExecutedBySeatId { get; set; }
        public DateTime? ExecuteDate { get; set; }
        public DateTime? ExecuteDateTime { get; set; }
        public MoneyExchangeTransferStatus TransferStatus { get; set; } = ((MoneyExchangeTransferStatus[])Enum.GetValues(typeof(MoneyExchangeTransferStatus)))[0];
        public MoneyExchangeTransferType TransferType { get; set; } = ((MoneyExchangeTransferType[])Enum.GetValues(typeof(MoneyExchangeTransferType)))[0];
    }
}