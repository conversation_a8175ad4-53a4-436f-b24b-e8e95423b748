using Volo.Abp.Reflection;

namespace EMPS.MoneyExchangeTransfersService.Permissions;

public class MoneyExchangeTransfersServicePermissions
{
    public const string GroupName = "MoneyExchangeTransfersService";

    public static string[] GetAll()
    {
        return ReflectionHelper.GetPublicConstantsRecursively(typeof(MoneyExchangeTransfersServicePermissions));
    }

    public static class CashierToCashierTransfer
    {
        public const string Default = GroupName + ".CashierToCashierTransfer";
        public const string Save = Default + ".Save";
        public const string Delete = Default + ".Delete";
        public const string Execute = Default + ".Execute";
        public const string ViewToExecute = Default + ".ViewToExecute";

    }
    public static class CompanyToServicePointTransfer
    {
        public const string Default = GroupName + ".CompanyToServicePointTransfer";
        public const string Save = Default + ".Save";
        public const string Delete = Default + ".Delete";
        public const string Execute = Default + ".Execute";
        public const string Approve = Default + ".Approve";

    }
    public static class ServicePointToCompanyTransfer
    {
        public const string Default = GroupName + ".ServicePointToCompanyTransfer";
        public const string Save = Default + ".Save";
        public const string Delete = Default + ".Delete";
        public const string Execute = Default + ".Execute";
        public const string Approve = Default + ".Approve";
        public const string ViewToExecute = Default + ".ViewToExecute";

    }
    public static class MoneyExchangeTransferCurrencies
    {
        public const string Default = GroupName + ".MoneyExchangeTransferCurrencies";
        public const string Edit = Default + ".Edit";
        public const string Create = Default + ".Create";
        public const string Delete = Default + ".Delete";
    }

    public static class PaymentCashVouchers
    {
        public const string Default = GroupName + ".PaymentCashVouchers";
        public const string Save = Default + ".Save";
        public const string Delete = Default + ".Delete";
        public const string Approve = Default + ".Approve";
        public const string Pay = Default + ".Pay";

    }
    public static class ReceiptCashVouchers
    {
        public const string Default = GroupName + ".ReceiptCashVouchers";
        public const string Save = Default + ".Save";
        public const string Delete = Default + ".Delete";
        public const string Approve = Default + ".Approve";
        public const string Receipt = Default + ".Receipt";

    }
}