

// Ignore Spelling: Blazor

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Blazorise;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.JSInterop;
using Blazorise.LoadingIndicator;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.CompanyService.Currencies;
using EMPS.CompanyService.ServicePoints;
using EMPS.CompanyService.Staffs;
using System.ServiceModel.Channels;




namespace EMPS.MoneyExchangeTransfersService.Blazor.Components
{
    public partial class ServicePointToCompanyForm
    {

        [Inject]
        private IJSRuntime JSRuntime { get; set; }
        [Inject]
        private IServicePointToCompanyTransferService ServicePointToCompanyService { get; set; }
        [Inject]
        private IServicePointsAppService ServicePointsAppService { get; set; }

        [Parameter]
        public bool IsReadOnlyMode { get; set; }

        private bool CanDeleteServicePointToCompany { get; set; }
        private bool CanCreateOrUpdateServicePointToCompany { get; set; }
        public Guid LocalCurrencyId { get; set; }

        [Parameter]
        public MoneyExchangeTransferCurrencyDto FormServicePointToCompany { get; set; }

        private Validations FormServicePointToCompanyValidations { get; set; } = new();

        private Guid EditingServicePointToCompanyId { get; set; }

        [Parameter]
        public Func<MoneyExchangeTransferCurrencyDto, Task> OnServicePointToCompanyLoaded { get; set; }

        public Func<Task<bool>> OnReset { get; set; }

        public bool IsServicePointToCompanyLoadedProp { get; private set; }

        public LoadingIndicator loadingIndicator;

        private string LastStatusBy { get; set; }
        private string LastStatusDateTime { get; set; }
        private IReadOnlyList<CurrencyDto> CurrenciesCollection { get; set; } = new List<CurrencyDto>();
        private async Task GetCurrenciesLookupAsync()
        {
            CurrenciesCollection = await ServicePointToCompanyService.GetAllActiveCurrenciesAsync();
        }


        private void SetLastStatus()
        {

            LastStatusBy = "";
            LastStatusDateTime = "";
            if (FormServicePointToCompany == null) return;
            if (FormServicePointToCompany.TransactionIssueUserId != null && FormServicePointToCompany.TransactionIssueUserId != Guid.Empty)
            {
                LastStatusBy = L["CreatedByUserName"] + ": " + FormServicePointToCompany.TransactionIssueUserName;

                LastStatusDateTime = L["CreatedDate"] + ": " + FormServicePointToCompany.TransactionDate;

            }
            if (FormServicePointToCompany.ApprovedByUserId != null && FormServicePointToCompany.ApprovedByUserId != Guid.Empty)
            {
                LastStatusBy = L["ApprovedByUser"] + ": " + FormServicePointToCompany.ApprovedByUserName;

                LastStatusDateTime = L["ApprovalDateTime"] + ": " + FormServicePointToCompany.ApprovalDateTime;

            }

            if (FormServicePointToCompany.IsExecute)
            {
                LastStatusBy = L["ExecutedByUserName"] + ": " + FormServicePointToCompany.ExecutedByUserName;
                LastStatusDateTime = L["ExecuteDate"] + ": " + FormServicePointToCompany.ExecuteDateTime; ;
            }



        }
        public ServicePointToCompanyForm()
        {
            FormServicePointToCompany = new();
        }
        protected override async Task OnInitializedAsync()
        {

            await SetPermissionsAsync();
            await GetCurrenciesLookupAsync();

        }

        private async Task SetPermissionsAsync()
        {
            CanCreateOrUpdateServicePointToCompany = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Save);
            CanDeleteServicePointToCompany = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Delete);

        }
        public async Task<bool> ResetServicePointToCompany(bool forceReset = false)
        {
            if (!forceReset)
                if (!await Message.Confirm(L["ResetConfirmationMessage"])) return false;
            FormServicePointToCompany = new();


            SetLastStatus();
            StateHasChanged();
            IsServicePointToCompanyLoadedProp = false;
            return true;

        }
        public string? GetServicePointToCompanyNo()
        {
            return FormServicePointToCompany.No;
        }
        public Guid GetServicePointToCompanyId()
        {
            return FormServicePointToCompany.Id;
        }
        public bool IsServicePointToCompanyLoaded()
        {
            return IsServicePointToCompanyLoadedProp;

        }
        public bool IsFormReadOnly()
        {
            if (IsReadOnlyMode) return true;
            if (FormServicePointToCompany.IsApproved) return true;
            if (FormServicePointToCompany.IsExecute) return true;
            return false;
        }
        private async Task LoadServicePointToCompany()
        {

            var LoadResult = await GetLoadResult();
            if (LoadResult == null)
            {
                _ = Message.Error(L["ServicePointToCompanyNotFound"]);
                return;
            }

            FormServicePointToCompany = LoadResult;


            OnServicePointToCompanyLoaded?.Invoke(LoadResult);
            IsServicePointToCompanyLoadedProp = true;

            SetLastStatus();
            StateHasChanged();

        }
        private async Task<MoneyExchangeTransferCurrencyDto?> GetLoadResult()
        {
            return await ServicePointToCompanyService.LoadByNoAsync(FormServicePointToCompany.No!);
        }
        private async Task LoadModelByServicePointToCompanyNo(KeyboardEventArgs e)
        {
            await Task.Delay(50);

            if (e.Key != "Enter") return;

            if (string.IsNullOrEmpty(FormServicePointToCompany.No)) return;


            await PerformLoadServicePointToCompany();

            await Task.CompletedTask;

        }
        public async Task PerformLoadServicePointToCompany()
        {

            try
            {
                await loadingIndicator.Show();

                await LoadServicePointToCompany();
                StateHasChanged();


            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await loadingIndicator.Hide();

            }
        }
        public void ChangeEntity(MoneyExchangeTransferCurrencyDto ServicePointToCompany)
        {
            this.FormServicePointToCompany = ServicePointToCompany;
            IsServicePointToCompanyLoadedProp = true;
            SetLastStatus();
            StateHasChanged();
        }

        public async Task OnChangeCurrency(Guid? id)
        {
            FormServicePointToCompany.CurrencyId = id;
            if (FormServicePointToCompany.CurrencyId == null)
            {
                //rest all
            }
            //get avgCost for this currency 
            //method have 2 parameter currency id and cashier id 

            //avgCost = avgCost for currency
            StateHasChanged();

        }




    }
}
