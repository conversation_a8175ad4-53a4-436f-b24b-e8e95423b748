using EMPS.MoneyExchangeTransfersService.CashVouchers;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.Shared.Enum;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace EMPS.MoneyExchangeTransfersService.CashVouchers
{
    public interface ICashVoucherRepository : IRepository<CashVoucher, Guid>
    {
        Task<string> GenerateNewNoByAccountingMode(AccountingMode accountingMode);

        Task<List<CashVoucher>> GetListAsync(
            string filterText = null,
            string no = null,
            double? amountMin = null,
            double? amountMax = null,
            string currencyCode = null,
            Guid? currencyId = null,
            string seatLocationName = null,
            Guid? seatLocationId = null,
            string issueUserName = null,
            Guid? issueUserId = null,
            string issueStaffName = null,
            Guid? issueStaffId = null,
            Guid? issueSeatId = null,
            bool? isApproved = null,
            string approvedByUserName = null,
            Guid? approvedByUserId = null,
            DateTime? approvalDateMin = null,
            DateTime? approvalDateMax = null,
            DateTime? approvalDateTimeMin = null,
            DateTime? approvalDateTimeMax = null,
            string notes = null,
            bool? isExecute = null,
            string executedByUserName = null,
            Guid? executedByUserId = null,
            string executedByStaffName = null,
            Guid? executedByStaffId = null,
            Guid? executedBySeatId = null,
            DateTime? executeDateMin = null,
            DateTime? executeDateMax = null,
            DateTime? executeDateTimeMin = null,
            DateTime? executeDateTimeMax = null,
            string approveByStaffName = null,
            Guid? approveByStaffId = null,
            Guid? approveBySeatId = null,
            AccountingMode? accountingType = null,
            string bolbKey = null,
            string fileName = null,
            string fileExtension = null,
            string fileSize = null,
            Guid? financialPeriodId = null,
            string financialPeriodName = null,
            Guid? financialAccountId = null,
            string financialAccountName = null,
            SeatLocationType? assignSeatLocation = null,
            Guid? costCenterId = null,
            string costCenterName = null,
            string sorting = null,
            int maxResultCount = int.MaxValue,
            int skipCount = 0,
            CancellationToken cancellationToken = default
        );

        Task<long> GetCountAsync(
            string filterText = null,
            string no = null,
            double? amountMin = null,
            double? amountMax = null,
            string currencyCode = null,
            Guid? currencyId = null,
            string seatLocationName = null,
            Guid? seatLocationId = null,
            string issueUserName = null,
            Guid? issueUserId = null,
            string issueStaffName = null,
            Guid? issueStaffId = null,
            Guid? issueSeatId = null,
            bool? isApproved = null,
            string approvedByUserName = null,
            Guid? approvedByUserId = null,
            DateTime? approvalDateMin = null,
            DateTime? approvalDateMax = null,
            DateTime? approvalDateTimeMin = null,
            DateTime? approvalDateTimeMax = null,
            string notes = null,
            bool? isExecute = null,
            string executedByUserName = null,
            Guid? executedByUserId = null,
            string executedByStaffName = null,
            Guid? executedByStaffId = null,
            Guid? executedBySeatId = null,
            DateTime? executeDateMin = null,
            DateTime? executeDateMax = null,
            DateTime? executeDateTimeMin = null,
            DateTime? executeDateTimeMax = null,
            string approveByStaffName = null,
            Guid? approveByStaffId = null,
            Guid? approveBySeatId = null,
            AccountingMode? accountingType = null,
            string bolbKey = null,
            string fileName = null,
            string fileExtension = null,
            string fileSize = null,
            Guid? financialPeriodId = null,
            string financialPeriodName = null,
            Guid? financialAccountId = null,
            string financialAccountName = null,
            SeatLocationType? assignSeatLocation = null,
            Guid? costCenterId = null,
            string costCenterName = null,
            CancellationToken cancellationToken = default);
    }
}