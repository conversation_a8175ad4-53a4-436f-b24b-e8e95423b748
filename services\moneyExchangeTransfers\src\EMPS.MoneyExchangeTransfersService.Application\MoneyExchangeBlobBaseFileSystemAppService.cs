﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EMPS.MoneyExchangeTransfersService
{
    public class MoneyExchangeBlobBaseFileSystemAppService : MoneyExchangeTransfersServiceAppService
    {
        private readonly string _blobStoragePath;
        public MoneyExchangeBlobBaseFileSystemAppService()
        {
            // Define the root path for storing blobs
            _blobStoragePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "BlobStorage");

            // Ensure the directory exists
            if (!Directory.Exists(_blobStoragePath))
            {
                Directory.CreateDirectory(_blobStoragePath);
            }
        }
        /// <summary>
        /// Method to retrieve a blob by its name asynchronously and return its byte array representation
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public async Task<byte[]> GetBlobByNameAsync(string name)
        {
            var filePath = Path.Combine(_blobStoragePath, name);
            if (File.Exists(filePath))
            {
                return await File.ReadAllBytesAsync(filePath);
            }
            else
            {
                throw new FileNotFoundException(L[$"Blob with name: {name} not found."]);
            }
        }

        /// <summary>
        /// Method to generate a new blob with the given byte array as input,
        /// It returns the name of the created blob
        /// </summary>
        public async Task<string> GenerateNewBlob(byte[] input)
        {

            // Generate a new unique blob name using a GUID
            var blobName = Guid.NewGuid().ToString();

            // Save the blob with the generated name to the file system
            var filePath = Path.Combine(_blobStoragePath, blobName);
            await File.WriteAllBytesAsync(filePath, input);

            // Return the name of the newly created blob
            return blobName;
        }
        public async Task<string> GenerateNewBlobForImg(byte[] input)
        {
            if (!IsPngOrJpeg(input))
            {
                throw new InvalidDataException(L["The file is neither a PNG nor a JPEG image."]);
            }

            return await GenerateNewBlob(input);
        }

        /// <summary>
        /// Check if the upload image is PNG or JPEG
        /// </summary>
        /// <param name="blobContent"></param>
        /// <returns></returns>
        public bool IsPngOrJpeg(byte[] blobContent)
        {
            // Check if the file is PNG
            if (blobContent.Length >= 8 && blobContent[0] == 0x89 && blobContent[1] == 0x50 && blobContent[2] == 0x4E && blobContent[3] == 0x47 && blobContent[4] == 0x0D && blobContent[5] == 0x0A && blobContent[6] == 0x1A && blobContent[7] == 0x0A)
            {
                return true;
            }

            // Check if the file is JPEG
            if (blobContent.Length >= 2 && blobContent[0] == 0xFF && blobContent[1] == 0xD8)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// this method take two parameter the first is old blob name and the secound the new blob content ,
        /// they make a decision to generate anew bolb key (name ) or not
        /// they return a key (blob name)
        /// </summary>
        /// <param name="oldBlobName"></param>
        /// <param name="newBlobContent"></param>
        /// <returns></returns>
        public async Task<string?> UpdateBlobByNameAndContentAsync(string? oldBlobName, byte[]? newBlobContent)
        {
            if (newBlobContent != null)
            {
                if (oldBlobName != null)
                {
                    var bolbcontent = await GetBlobByNameAsync(oldBlobName!);

                    if (!bolbcontent.SequenceEqual(newBlobContent))
                    {
                        DeleteBlobFromTheFolderByNameAsync(oldBlobName);
                        return await GenerateNewBlob(newBlobContent);
                    }
                    return oldBlobName;
                }
                else
                {
                    return await GenerateNewBlob(newBlobContent);
                }
            }
            else
            {
                return oldBlobName;
            }
        }

        /// <summary>
        /// Method to retrieve a blob by its name asynchronously and deleted its file form the folder
        /// </summary>
        /// <param name="name"></param>
        /// <exception cref="FileNotFoundException"></exception>
        public void DeleteBlobFromTheFolderByNameAsync(string name)
        {
            var filePath = Path.Combine(_blobStoragePath, name);
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
            else
            {
                throw new FileNotFoundException($"Blob with name: {name} not found.");
            }
        }
    }
}
