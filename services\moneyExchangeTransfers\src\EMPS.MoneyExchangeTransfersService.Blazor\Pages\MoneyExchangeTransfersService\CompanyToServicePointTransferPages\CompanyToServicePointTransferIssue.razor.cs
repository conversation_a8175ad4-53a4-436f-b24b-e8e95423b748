using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazorise;
using Blazorise.DataGrid;
using Volo.Abp.BlazoriseUI.Components;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.MoneyExchangeTransfersService.Shared;
using AutoMapper.Internal.Mappers;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.ServiceModel.Channels;
using Volo.Abp;
using EMPS.MoneyExchangeTransfersService.Blazor.Components;

namespace EMPS.MoneyExchangeTransfersService.Blazor.Pages.MoneyExchangeTransfersService.CompanyToServicePointTransferPages
{
    public partial class CompanyToServicePointTransferIssue
    {

        [Inject]
        private ICompanyToServicePointTransferService _CompanyToServicePointTransferService { get; set; }



        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar { get; } = new PageToolbar();


        private bool CanCreateOrUpdateCompanyToServicePointTransferDraft { get; set; }
        private bool CanDeleteCompanyToServicePointTransferDraft { get; set; }

        private bool IsReadOnly { get; set; }


        private CompanyToServicePointForm CompanyToServicePointFormRef { get; set; }


        public CompanyToServicePointTransferIssue()
        {
        }

        protected override async Task OnInitializedAsync()
        {

            await SetToolbarButtons(null);
            await SetBreadcrumbItemsAsync();
            await SetPermissionsAsync();
        }


        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:CompanyToServicePointTransferIssue"]));


            return ValueTask.CompletedTask;
        }


        private async void ResetCompanyToServicePointTransfer(bool forceReset = false)
        {
            if (await CompanyToServicePointFormRef.ResetCompanyToServicePoint(forceReset))
            {
                await SetToolbarButtons(null);
                SetFormIsReadOnly(null);
                StateHasChanged();

            }


        }

        private async Task<MoneyExchangeTransferCurrencyDto?> SaveTransfare()
        {
            var CompanyToServicePointTransfer = CompanyToServicePointFormRef.FormCompanyToServicePoint;
            MoneyExchangeTransferCurrencyCreateDto createDto = ObjectMapper.Map<MoneyExchangeTransferCurrencyDto, MoneyExchangeTransferCurrencyCreateDto>(CompanyToServicePointTransfer);
            if (!await ValidateForm(createDto)) return null;

            var newCompanyToServicePointTransfer = await _CompanyToServicePointTransferService.SaveAsync(createDto);
            CompanyToServicePointFormRef.ChangeEntity(newCompanyToServicePointTransfer);
            return newCompanyToServicePointTransfer;
        }

        private async Task SetPermissionsAsync()
        {
            CanCreateOrUpdateCompanyToServicePointTransferDraft = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Save);
            CanDeleteCompanyToServicePointTransferDraft = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Delete);
        }


        private Task<bool> ValidateForm(MoneyExchangeTransferCurrencyCreateDto FormCompanyToServicePointTransferDraft)
        {




            return Task.FromResult(true);

        }


        private void Log(string message)
        {
            Console.WriteLine($"MY-LOGGING: {message}");
        }



        private IReadOnlyList<string> stringList { get; set; }

        private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<string> e)
        {
        }

        private async Task HandleTransfareLoad(MoneyExchangeTransferCurrencyDto LoadedCompanyToServicePointTransfer)
        {
            await SetToolbarButtons(LoadedCompanyToServicePointTransfer);
            SetFormIsReadOnly(LoadedCompanyToServicePointTransfer);
            Log(IsReadOnly.ToString());
            StateHasChanged();

        }

        private async Task DeleteTransfareAsync(string _No)
        {
            await _CompanyToServicePointTransferService.DeletedByNoAsync(_No);
            ResetCompanyToServicePointTransfer(true);
        }


        private async Task SetToolbarButtons(MoneyExchangeTransferCurrencyDto? accountPayment)
        {
            Toolbar.Contributors.Clear();


            Toolbar.AddButton(
                L["NewCompanyToServicePointTransfer"], () =>
                {
                    ResetCompanyToServicePointTransfer();

                    return Task.CompletedTask;

                }, IconName.Add, Color.Warning


            );

            Toolbar.AddButton(
                L["SaveCompanyToServicePointTransfer"], async () =>
                {
                    await PerformSave();
                }, IconName.Save, requiredPolicyName:
                MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Save, color: Color.Success, disabled: !CanCompanyToServicePointTransferBeUpdated(accountPayment)
            );
            if (accountPayment != null && accountPayment.Id != Guid.Empty)
            {

                Toolbar.AddButton(
                            L["Delete"], async () =>
                            {
                                await PerformDeleteTransfare();

                            }, IconName.Delete,
                            requiredPolicyName: MoneyExchangeTransfersServicePermissions.CompanyToServicePointTransfer.Delete,
                            color: Color.Danger,
                            disabled: !CanCompanyToServicePointTransferBeDeleted(accountPayment)
                        );

            }

            StateHasChanged();


        }


        private bool CanCompanyToServicePointTransferBeDeleted(MoneyExchangeTransferCurrencyDto accountPayment)
        {



            if (accountPayment == null) return false;
            if (accountPayment.Id == Guid.Empty) return false;
            if (accountPayment.IsApproved) return false;

            if (accountPayment.IsExecute) return false;

            return true;
        }
        private bool CanCompanyToServicePointTransferBeUpdated(MoneyExchangeTransferCurrencyDto accountPayment)
        {

            if (accountPayment != null)
            {

                if (accountPayment.IsApproved) return false;

                if (accountPayment.IsExecute) return false;
            }

            return true;
        }

        private void SetFormIsReadOnly(MoneyExchangeTransferCurrencyDto? accountPayment)
        {

            this.IsReadOnly = false;
            if (accountPayment == null) return;
            if (accountPayment.IsExecute)
            {
                this.IsReadOnly = true;
            }
        }

        private async Task PerformSave()
        {
            await Task.Delay(300);


            if (!CanCompanyToServicePointTransferBeUpdated(CompanyToServicePointFormRef.FormCompanyToServicePoint))
            {
                await Message.Error(L["Error:ThisCompanyToServicePointTransferCannotBeUpdated"]);
                return;
            }
            Console.WriteLine("********************* :1");
            MoneyExchangeTransferCurrencyDto? newCompanyToServicePointTransfer = new();
            var CompanyToServicePointTransfer = CompanyToServicePointFormRef.FormCompanyToServicePoint;
            Console.WriteLine("********************* :2");



            await CompanyToServicePointFormRef.loadingIndicator.Show();
            Console.WriteLine("********************* :3");

            try
            {
                newCompanyToServicePointTransfer = await SaveTransfare();

                if (newCompanyToServicePointTransfer != null)
                {
                    await SetToolbarButtons(newCompanyToServicePointTransfer);
                    await Message.Success(L["SaveSuccess"]);
                }


            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await CompanyToServicePointFormRef.loadingIndicator.Hide();

            }

        }
        private async Task PerformDeleteTransfare()
        {
            if (!CompanyToServicePointFormRef.IsCompanyToServicePointLoaded())
            {
                await Message.Error(L["Error:LoadCompanyToServicePointTransferFirst"]);
                return;
            }
            if (!CanCompanyToServicePointTransferBeDeleted(CompanyToServicePointFormRef.FormCompanyToServicePoint))
            {
                await Message.Error(L["Error:ThisCompanyToServicePointTransferCannotBeDeleted"]);
                return;
            }
            if (!await Message.Confirm(L["DeleteConfirmationMessage"])) return;
            await CompanyToServicePointFormRef.loadingIndicator.Show();

            try
            {
                var CompanyToServicePointTransferNo = CompanyToServicePointFormRef.GetCompanyToServicePointNo();


                await DeleteTransfareAsync(CompanyToServicePointTransferNo!);
                await Message.Success(L["SuccessfullyDeleted"]);

            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await CompanyToServicePointFormRef.loadingIndicator.Hide();

            }
        }
        private async Task PerformNewTransfare()
        {
            ResetCompanyToServicePointTransfer();

        }
        public async Task HandleKeyDown(KeyboardEventArgs e)
        {

            ExecuteJS("event.stopPropagation();");

            if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyD"))
            {
                await PerformDeleteTransfare();
            }
            else if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyR"))
            {
                await PerformNewTransfare();
            }
            else if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyG"))
            {
                await Task.Delay(50);
                await CompanyToServicePointFormRef.PerformLoadCompanyToServicePoint();
            }
            else if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyS"))
            {
                await PerformSave();
            }


        }
        private void ExecuteJS(string script)
        {
            JSRuntime.InvokeVoidAsync("eval", script);
        }
        [Inject] IJSRuntime JSRuntime { get; set; }
        protected override void OnAfterRender(bool firstRender)
        {
            base.OnAfterRender(firstRender);
            if (firstRender)
            {
                // See warning about memory above in the article
                var dotNetReference = DotNetObjectReference.Create(this);
                JSRuntime.InvokeVoidAsync("GetDotnetInstance", dotNetReference);
            }
        }






    }
}
