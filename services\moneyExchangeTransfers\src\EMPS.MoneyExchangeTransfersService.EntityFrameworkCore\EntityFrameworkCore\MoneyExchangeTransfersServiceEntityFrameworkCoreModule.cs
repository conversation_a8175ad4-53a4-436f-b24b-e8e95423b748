using EMPS.MoneyExchangeTransfersService.CashVouchers;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.SqlServer;
using Volo.Abp.Modularity;

namespace EMPS.MoneyExchangeTransfersService.EntityFrameworkCore;

[DependsOn(
    typeof(AbpEntityFrameworkCoreSqlServerModule),
    typeof(AbpEntityFrameworkCoreModule),
    typeof(MoneyExchangeTransfersServiceDomainModule)
)]
public class MoneyExchangeTransfersServiceEntityFrameworkCoreModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        MoneyExchangeTransfersServiceEfCoreEntityExtensionMappings.Configure();
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddAbpDbContext<MoneyExchangeTransfersServiceDbContext>(options =>
        {
            /* Remove "includeAllEntities: true" to create
             * default repositories only for aggregate roots */
            options.AddDefaultRepositories(includeAllEntities: true);
            options.AddRepository<MoneyExchangeTransferCurrency, MoneyExchangeTransferCurrencies.EfCoreMoneyExchangeTransferCurrencyRepository>();

            options.AddRepository<CashVoucher, CashVouchers.EfCoreCashVoucherRepository>();

        });

        Configure<AbpDbContextOptions>(options =>
        {
            options.Configure<MoneyExchangeTransfersServiceDbContext>(c =>
            {
                c.UseSqlServer(b =>
                {
                    b.MigrationsHistoryTable("__MoneyExchangeTransfersService_Migrations");
                });
            });
        });
    }
}