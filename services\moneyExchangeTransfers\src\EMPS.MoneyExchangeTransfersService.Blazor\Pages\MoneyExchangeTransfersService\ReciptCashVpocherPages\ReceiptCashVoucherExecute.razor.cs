using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazorise;
using Blazorise.DataGrid;
using Volo.Abp.BlazoriseUI.Components;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.MoneyExchangeTransfersService.Shared;
using AutoMapper.Internal.Mappers;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.ServiceModel.Channels;
using Volo.Abp;
using EMPS.MoneyExchangeTransfersService.Blazor.Components;
using EMPS.MoneyExchangeTransfersService.CashVouchers;

namespace EMPS.MoneyExchangeTransfersService.Blazor.Pages.MoneyExchangeTransfersService.ReciptCashVpocherPages
{
    public partial class ReceiptCashVoucherExecute
    {

        [Inject]
        private IReceiptCashVouchersAppService _ReceiptVoucherService { get; set; }
        


        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar { get; } = new PageToolbar();


        private bool CanReceiptReceiptVoucherDraft { get; set; }

        private bool IsReadOnly { get; set; }


        private ReceiptCashVoucherForm ReceiptVoucherFormRef { get; set; }


        public ReceiptCashVoucherExecute()
        {
        }

        protected override async Task OnInitializedAsync()
        {

            await SetToolbarButtons(null);
            await SetBreadcrumbItemsAsync();
            await SetPermissionsAsync();
        }


        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:ReceiptCashVoucherExecute"]));


            return ValueTask.CompletedTask;
        }


        private async void ResetReceiptVoucher(bool forceReset = false)
        {
            if (await ReceiptVoucherFormRef.ResetReceiptVoucher(forceReset))
            {
                await SetToolbarButtons(null);
                SetFormIsReadOnly(null);
                StateHasChanged();

            }


        }

    
        private async Task SetPermissionsAsync()
        {
            CanReceiptReceiptVoucherDraft = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Receipt);

        }




    



        private IReadOnlyList<string> stringList { get; set; }

        private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<string> e)
        {
        }

        private async Task HandleTransfareLoad(CashVoucherDto LoadedReceiptVoucher)
        {
            await SetToolbarButtons(LoadedReceiptVoucher);
            SetFormIsReadOnly(LoadedReceiptVoucher);
            StateHasChanged();

        }

     

        private async Task SetToolbarButtons(CashVoucherDto? accountReceipt)
        {
            Toolbar.Contributors.Clear();


            Toolbar.AddButton(
                L["NewReceiptVoucher"], () =>
                {
                    ResetReceiptVoucher();

                    return Task.CompletedTask;

                }, IconName.Add, Color.Warning


            );
            Toolbar.AddButton(
           L["Receipt"], async () =>
           {
               await PerformReceipt();
           }, IconName.Save, requiredPolicyName:
           MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Receipt, color: Color.Success, disabled: !CanReceiptVoucherBeReceipt(accountReceipt)
       );


            StateHasChanged();


        }


        private bool CanReceiptVoucherBeReceipt(CashVoucherDto ?accountReceipt)
        {
            if(accountReceipt == null) return false;
            if (accountReceipt != null)
                if (accountReceipt.IsExecute||! accountReceipt.IsApproved) return false;

            return true;
        }

        private void SetFormIsReadOnly(CashVoucherDto? accountReceipt)
        {

            this.IsReadOnly = false;
            if (accountReceipt == null) this.IsReadOnly = true; 
            else

            if (accountReceipt.IsExecute|| !accountReceipt.IsApproved)
            {
                this.IsReadOnly = true;
            }
        }

        private async Task PerformReceipt()
        {
            await Task.Delay(800);
            if (!ReceiptVoucherFormRef.IsReceiptVoucherLoadedProp)
            {
                await Message.Error(L["Error:LoadReceiptVoucherFirst"]);
                return;
            }

            if (!CanReceiptVoucherBeReceipt(ReceiptVoucherFormRef.FormReceiptVoucher))
            {
                await Message.Error(L["Error:ThisReceiptVoucherCannotBeReceiptd"]);
                return;
            }

            CashVoucherDto? newReceiptVoucher = new();
            var ReceiptVoucher = ReceiptVoucherFormRef.FormReceiptVoucher;


            var confirmation = await Message.Confirm(L["ReceiptReceiptVoucherConfirmationMessage"] + ReceiptVoucherFormRef.GetReceiptVoucherNo());
            if (!confirmation) return;
            await ReceiptVoucherFormRef.loadingIndicator.Show();

            try
            {
                newReceiptVoucher = await ReceiptReceiptVoucher();

                if (newReceiptVoucher != null)
                {
                    await SetToolbarButtons(newReceiptVoucher);

                    await Message.Success(L["ReceiptSuccess"]);
                }


            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await ReceiptVoucherFormRef.loadingIndicator.Hide();

            }
        }
        private async Task<CashVoucherDto> ReceiptReceiptVoucher()
        {
            var ReceiptdTransaction = await _ReceiptVoucherService.ReceiptByNoAsync(ReceiptVoucherFormRef.GetReceiptVoucherNo());
            ReceiptVoucherFormRef.ChangeEntity(ReceiptdTransaction);
            return ReceiptdTransaction;
        }
        private async Task PerformNewTransfare()
        {
            ResetReceiptVoucher();

        }
        public async Task HandleKeyDown(KeyboardEventArgs e)
        {

            ExecuteJS("event.stopPropagation();");
            if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyA"))
            {
                await PerformReceipt();
            } else
            if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyR"))
            {
                await PerformNewTransfare();
            }
            else if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyG"))
            {
                await Task.Delay(50);
                await ReceiptVoucherFormRef.PerformLoadReceiptVoucher();
            }
             


        }
        private void ExecuteJS(string script)
        {
            JSRuntime.InvokeVoidAsync("eval", script);
        }
        [Inject] IJSRuntime JSRuntime { get; set; }
        protected override void OnAfterRender(bool firstRender)
        {
            base.OnAfterRender(firstRender);
            if (firstRender)
            {
                // See warning about memory above in the article
                var dotNetReference = DotNetObjectReference.Create(this);
                JSRuntime.InvokeVoidAsync("GetDotnetInstance", dotNetReference);
            }
        }






    }
}
