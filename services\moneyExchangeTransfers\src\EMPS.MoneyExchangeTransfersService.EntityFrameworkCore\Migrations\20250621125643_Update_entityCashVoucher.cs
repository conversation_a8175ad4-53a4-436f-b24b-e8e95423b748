﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMPS.MoneyExchangeTransfersService.Migrations
{
    /// <inheritdoc />
    public partial class UpdateentityCashVoucher : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "CashierId",
                table: "CashVouchers",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CashierName",
                table: "CashVouchers",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "IssueDateTime",
                table: "CashVouchers",
                type: "datetime2",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CashierId",
                table: "CashVouchers");

            migrationBuilder.DropColumn(
                name: "CashierName",
                table: "CashVouchers");

            migrationBuilder.DropColumn(
                name: "IssueDateTime",
                table: "CashVouchers");
        }
    }
}
