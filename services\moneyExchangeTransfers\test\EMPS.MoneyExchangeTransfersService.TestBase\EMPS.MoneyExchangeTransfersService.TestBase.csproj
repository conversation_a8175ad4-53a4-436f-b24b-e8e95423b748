<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>EMPS.MoneyExchangeTransfersService</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="appsettings.json" />
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <None Remove="appsettings.secrets.json" />
    <Content Include="appsettings.secrets.json">
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.2.0" />
    <PackageReference Include="NSubstitute" Version="4.3.0" />
    <PackageReference Include="NSubstitute.Analyzers.CSharp" Version="1.0.15">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Shouldly" Version="4.1.0" />
    <PackageReference Include="xunit" Version="2.4.1" />
    <PackageReference Include="xunit.extensibility.execution" Version="2.4.1" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5" />
    <PackageReference Include="Volo.Abp.Autofac" Version="7.3.2" />
    <PackageReference Include="Volo.Abp.Authorization" Version="7.3.2" />
    <PackageReference Include="Volo.Abp.TestBase" Version="7.3.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\EMPS.MoneyExchangeTransfersService.Domain\EMPS.MoneyExchangeTransfersService.Domain.csproj" />
  </ItemGroup>

</Project>
