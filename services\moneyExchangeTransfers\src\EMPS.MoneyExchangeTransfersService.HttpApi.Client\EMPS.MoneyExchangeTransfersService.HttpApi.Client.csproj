<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\..\..\common.props" />

  <PropertyGroup>
    <TargetFrameworks>net7.0</TargetFrameworks>
    <Nullable>enable</Nullable>
    <RootNamespace>EMPS.MoneyExchangeTransfersService</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Http.Client" Version="7.3.2" />
    <ProjectReference
      Include="..\EMPS.MoneyExchangeTransfersService.Application.Contracts\EMPS.MoneyExchangeTransfersService.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="**\*generate-proxy.json" />
    <Content Remove="**\*generate-proxy.json" />
  </ItemGroup>

</Project>