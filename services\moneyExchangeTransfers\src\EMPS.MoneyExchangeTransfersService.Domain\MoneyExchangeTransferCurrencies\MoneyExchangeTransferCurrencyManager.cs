using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JetBrains.Annotations;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Data;

namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
{
    public class MoneyExchangeTransferCurrencyManager : DomainService
    {
        private readonly IMoneyExchangeTransferCurrencyRepository _moneyExchangeTransferCurrencyRepository;

        public MoneyExchangeTransferCurrencyManager(IMoneyExchangeTransferCurrencyRepository moneyExchangeTransferCurrencyRepository)
        {
            _moneyExchangeTransferCurrencyRepository = moneyExchangeTransferCurrencyRepository;
        }

        public async Task<MoneyExchangeTransferCurrency> CreateAsync(
        string no, double amount, string currencyCode, string servicePointName, string transactionIssueUserName, string transactionIssueStaffName, bool isApproved, string approvedByUserName, string notes, bool isExecute, string executedByUserName, string executedByStaffName, MoneyExchangeTransferStatus transferStatus, MoneyExchangeTransferType transferType, Guid? currencyId = null, Guid? servicePointId = null, Guid? transactionIssueUserId = null, Guid? transactionIssueStaffId = null, Guid? transactionIssueSeatId = null, Guid? approvedByUserId = null, DateTime? approvalDate = null, DateTime? approvalDateTime = null, Guid? executedByUserId = null, Guid? executedByStaffId = null, Guid? executedBySeatId = null, DateTime? executeDate = null, DateTime? executeDateTime = null)
        {
            Check.NotNull(transferStatus, nameof(transferStatus));
            Check.NotNull(transferType, nameof(transferType));

            var moneyExchangeTransferCurrency = new MoneyExchangeTransferCurrency(
             GuidGenerator.Create(),
             no, amount, currencyCode, servicePointName, transactionIssueUserName, transactionIssueStaffName, isApproved, approvedByUserName, notes, isExecute, executedByUserName, executedByStaffName, transferStatus, transferType, currencyId, servicePointId, transactionIssueUserId, transactionIssueStaffId, transactionIssueSeatId, approvedByUserId, approvalDate, approvalDateTime, executedByUserId, executedByStaffId, executedBySeatId, executeDate, executeDateTime
             );

            return await _moneyExchangeTransferCurrencyRepository.InsertAsync(moneyExchangeTransferCurrency);
        }

        public async Task<MoneyExchangeTransferCurrency> UpdateAsync(
            Guid id,
            string no, double amount, string currencyCode, string servicePointName, string transactionIssueUserName, string transactionIssueStaffName, bool isApproved, string approvedByUserName, string notes, bool isExecute, string executedByUserName, string executedByStaffName, MoneyExchangeTransferStatus transferStatus, MoneyExchangeTransferType transferType, Guid? currencyId = null, Guid? servicePointId = null, Guid? transactionIssueUserId = null, Guid? transactionIssueStaffId = null, Guid? transactionIssueSeatId = null, Guid? approvedByUserId = null, DateTime? approvalDate = null, DateTime? approvalDateTime = null, Guid? executedByUserId = null, Guid? executedByStaffId = null, Guid? executedBySeatId = null, DateTime? executeDate = null, DateTime? executeDateTime = null, [CanBeNull] string concurrencyStamp = null
        )
        {
            Check.NotNull(transferStatus, nameof(transferStatus));
            Check.NotNull(transferType, nameof(transferType));

            var moneyExchangeTransferCurrency = await _moneyExchangeTransferCurrencyRepository.GetAsync(id);

            moneyExchangeTransferCurrency.No = no;
            moneyExchangeTransferCurrency.Amount = amount;
            moneyExchangeTransferCurrency.CurrencyCode = currencyCode;
            moneyExchangeTransferCurrency.ServicePointName = servicePointName;
            moneyExchangeTransferCurrency.TransactionIssueUserName = transactionIssueUserName;
            moneyExchangeTransferCurrency.TransactionIssueStaffName = transactionIssueStaffName;
            moneyExchangeTransferCurrency.IsApproved = isApproved;
            moneyExchangeTransferCurrency.ApprovedByUserName = approvedByUserName;
            moneyExchangeTransferCurrency.Notes = notes;
            moneyExchangeTransferCurrency.IsExecute = isExecute;
            moneyExchangeTransferCurrency.ExecutedByUserName = executedByUserName;
            moneyExchangeTransferCurrency.ExecutedByStaffName = executedByStaffName;
            moneyExchangeTransferCurrency.TransferStatus = transferStatus;
            moneyExchangeTransferCurrency.TransferType = transferType;
            moneyExchangeTransferCurrency.CurrencyId = currencyId;
            moneyExchangeTransferCurrency.ServicePointId = servicePointId;
            moneyExchangeTransferCurrency.TransactionIssueUserId = transactionIssueUserId;
            moneyExchangeTransferCurrency.TransactionIssueStaffId = transactionIssueStaffId;
            moneyExchangeTransferCurrency.TransactionIssueSeatId = transactionIssueSeatId;
            moneyExchangeTransferCurrency.ApprovedByUserId = approvedByUserId;
            moneyExchangeTransferCurrency.ApprovalDate = approvalDate;
            moneyExchangeTransferCurrency.ApprovalDateTime = approvalDateTime;
            moneyExchangeTransferCurrency.ExecutedByUserId = executedByUserId;
            moneyExchangeTransferCurrency.ExecutedByStaffId = executedByStaffId;
            moneyExchangeTransferCurrency.ExecutedBySeatId = executedBySeatId;
            moneyExchangeTransferCurrency.ExecuteDate = executeDate;
            moneyExchangeTransferCurrency.ExecuteDateTime = executeDateTime;

            moneyExchangeTransferCurrency.SetConcurrencyStampIfNotNull(concurrencyStamp);
            return await _moneyExchangeTransferCurrencyRepository.UpdateAsync(moneyExchangeTransferCurrency);
        }

    }
}