using EMPS.Shared.Enum;
using EMPS.MoneyExchangeTransfersService.CashVouchers;
using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace EMPS.MoneyExchangeTransfersService.CashVouchers
{
    public class CashVoucherCreateDto
    {
        public string? No { get; set; }
        public double Amount { get; set; }
        public string? CurrencyCode { get; set; }
        public Guid? CurrencyId { get; set; }
        public string? SeatLocationName { get; set; }
        public Guid? SeatLocationId { get; set; }
        public string? IssueUserName { get; set; }
        public Guid? IssueUserId { get; set; }
        public string? IssueStaffName { get; set; }
        public Guid? IssueStaffId { get; set; }
        public Guid? IssueSeatId { get; set; }
        public bool IsApproved { get; set; } = false;
        public string? ApprovedByUserName { get; set; }
        public Guid? ApprovedByUserId { get; set; }
        public DateTime? ApprovalDate { get; set; }
        public DateTime? ApprovalDateTime { get; set; }
        public string? Notes { get; set; }
        public bool IsExecute { get; set; } = false;
        public string? ExecutedByUserName { get; set; }
        public Guid? ExecutedByUserId { get; set; }
        public string? ExecutedByStaffName { get; set; }
        public Guid? ExecutedByStaffId { get; set; }
        public Guid? ExecutedBySeatId { get; set; }
        public DateTime? ExecuteDate { get; set; }
        public DateTime? ExecuteDateTime { get; set; }
        public string? ApproveByStaffName { get; set; }
        public Guid ApproveByStaffId { get; set; }
        public Guid ApproveBySeatId { get; set; }
        public AccountingMode AccountingType { get; set; } = ((AccountingMode[])Enum.GetValues(typeof(AccountingMode)))[0];
        public string? BolbKey { get; set; }
        public byte[]? BlobContent { get; set; }

        public string? FileName { get; set; }
        public string? FileExtension { get; set; }
        public string? FileSize { get; set; }
        public Guid FinancialPeriodId { get; set; }
        public string? FinancialPeriodName { get; set; }
        public Guid FinancialAccountId { get; set; }
        public string? FinancialAccountName { get; set; }
        public  string? CashierName { get; set; }
        public  Guid? CashierId { get; set; }
        public SeatLocationType AssignSeatLocation { get; set; } = ((SeatLocationType[])Enum.GetValues(typeof(SeatLocationType)))[0];
        public Guid CostCenterId { get; set; }
        public string? CostCenterName { get; set; }
    }
}