using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using Volo.Abp.EntityFrameworkCore.Modeling;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Data;
using Volo.Abp.EntityFrameworkCore;

namespace EMPS.MoneyExchangeTransfersService.EntityFrameworkCore;

[ConnectionStringName(MoneyExchangeTransfersServiceDbProperties.ConnectionStringName)]
public class MoneyExchangeTransfersServiceDbContext : AbpDbContext<MoneyExchangeTransfersServiceDbContext>
{
    public DbSet<MoneyExchangeTransferCurrency> MoneyExchangeTransferCurrencies { get; set; }

    public MoneyExchangeTransfersServiceDbContext(DbContextOptions<MoneyExchangeTransfersServiceDbContext> options)
        : base(options)
    {

    }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        builder.ConfigureMoneyExchangeTransfersService();
    }
}