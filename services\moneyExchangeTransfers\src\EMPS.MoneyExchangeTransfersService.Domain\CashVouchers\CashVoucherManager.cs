using EMPS.Shared.Enum;
using EMPS.MoneyExchangeTransfersService.CashVouchers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JetBrains.Annotations;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Data;

namespace EMPS.MoneyExchangeTransfersService.CashVouchers
{
    public class CashVoucherManager : DomainService
    {
        private readonly ICashVoucherRepository _cashVoucherRepository;

        public CashVoucherManager(ICashVoucherRepository cashVoucherRepository)
        {
            _cashVoucherRepository = cashVoucherRepository;
        }

        public async Task<CashVoucher> CreateAsync(
        string no, double amount, string currencyCode, string seatLocationName, string issueUserName, string issueStaffName, bool isApproved, string approvedByUserName, string notes, bool isExecute, string executedByUserName, string executedByStaffName, string approveByStaffName, Guid approveByStaffId, Guid approveBySeatId, AccountingMode accountingType, string bolbKey, string fileName, string fileExtension, string fileSize, Guid financialPeriodId, string financialPeriodName, Guid financialAccountId, string financialAccountName, SeatLocationType assignSeatLocation, Guid costCenterId, string costCenterName, Guid? currencyId = null, Guid? seatLocationId = null, Guid? issueUserId = null, Guid? issueStaffId = null, Guid? issueSeatId = null, Guid? approvedByUserId = null, DateTime? approvalDate = null, DateTime? approvalDateTime = null, Guid? executedByUserId = null, Guid? executedByStaffId = null, Guid? executedBySeatId = null, DateTime? executeDate = null, DateTime? executeDateTime = null)
        {
            Check.NotNull(accountingType, nameof(accountingType));
            Check.NotNull(assignSeatLocation, nameof(assignSeatLocation));

            var cashVoucher = new CashVoucher(
             GuidGenerator.Create(),
             no, amount, currencyCode, seatLocationName, issueUserName, issueStaffName, isApproved, approvedByUserName, notes, isExecute, executedByUserName, executedByStaffName, approveByStaffName, approveByStaffId, approveBySeatId, accountingType, bolbKey, fileName, fileExtension, fileSize, financialPeriodId, financialPeriodName, financialAccountId, financialAccountName, assignSeatLocation, costCenterId, costCenterName, currencyId, seatLocationId, issueUserId, issueStaffId, issueSeatId, approvedByUserId, approvalDate, approvalDateTime, executedByUserId, executedByStaffId, executedBySeatId, executeDate, executeDateTime
             );

            return await _cashVoucherRepository.InsertAsync(cashVoucher);
        }

        public async Task<CashVoucher> UpdateAsync(
            Guid id,
            string no, double amount, string currencyCode, string seatLocationName, string issueUserName, string issueStaffName, bool isApproved, string approvedByUserName, string notes, bool isExecute, string executedByUserName, string executedByStaffName, string approveByStaffName, Guid approveByStaffId, Guid approveBySeatId, AccountingMode accountingType, string bolbKey, string fileName, string fileExtension, string fileSize, Guid financialPeriodId, string financialPeriodName, Guid financialAccountId, string financialAccountName, SeatLocationType assignSeatLocation, Guid costCenterId, string costCenterName, Guid? currencyId = null, Guid? seatLocationId = null, Guid? issueUserId = null, Guid? issueStaffId = null, Guid? issueSeatId = null, Guid? approvedByUserId = null, DateTime? approvalDate = null, DateTime? approvalDateTime = null, Guid? executedByUserId = null, Guid? executedByStaffId = null, Guid? executedBySeatId = null, DateTime? executeDate = null, DateTime? executeDateTime = null, [CanBeNull] string concurrencyStamp = null
        )
        {
            Check.NotNull(accountingType, nameof(accountingType));
            Check.NotNull(assignSeatLocation, nameof(assignSeatLocation));

            var cashVoucher = await _cashVoucherRepository.GetAsync(id);

            cashVoucher.No = no;
            cashVoucher.Amount = amount;
            cashVoucher.CurrencyCode = currencyCode;
            cashVoucher.SeatLocationName = seatLocationName;
            cashVoucher.IssueUserName = issueUserName;
            cashVoucher.IssueStaffName = issueStaffName;
            cashVoucher.IsApproved = isApproved;
            cashVoucher.ApprovedByUserName = approvedByUserName;
            cashVoucher.Notes = notes;
            cashVoucher.IsExecute = isExecute;
            cashVoucher.ExecutedByUserName = executedByUserName;
            cashVoucher.ExecutedByStaffName = executedByStaffName;
            cashVoucher.ApproveByStaffName = approveByStaffName;
            cashVoucher.ApproveByStaffId = approveByStaffId;
            cashVoucher.ApproveBySeatId = approveBySeatId;
            cashVoucher.AccountingType = accountingType;
            cashVoucher.BolbKey = bolbKey;
            cashVoucher.FileName = fileName;
            cashVoucher.FileExtension = fileExtension;
            cashVoucher.FileSize = fileSize;
            cashVoucher.FinancialPeriodId = financialPeriodId;
            cashVoucher.FinancialPeriodName = financialPeriodName;
            cashVoucher.FinancialAccountId = financialAccountId;
            cashVoucher.FinancialAccountName = financialAccountName;
            cashVoucher.AssignSeatLocation = assignSeatLocation;
            cashVoucher.CostCenterId = costCenterId;
            cashVoucher.CostCenterName = costCenterName;
            cashVoucher.CurrencyId = currencyId;
            cashVoucher.SeatLocationId = seatLocationId;
            cashVoucher.IssueUserId = issueUserId;
            cashVoucher.IssueStaffId = issueStaffId;
            cashVoucher.IssueSeatId = issueSeatId;
            cashVoucher.ApprovedByUserId = approvedByUserId;
            cashVoucher.ApprovalDate = approvalDate;
            cashVoucher.ApprovalDateTime = approvalDateTime;
            cashVoucher.ExecutedByUserId = executedByUserId;
            cashVoucher.ExecutedByStaffId = executedByStaffId;
            cashVoucher.ExecutedBySeatId = executedBySeatId;
            cashVoucher.ExecuteDate = executeDate;
            cashVoucher.ExecuteDateTime = executeDateTime;

            cashVoucher.SetConcurrencyStampIfNotNull(concurrencyStamp);
            return await _cashVoucherRepository.UpdateAsync(cashVoucher);
        }

    }
}