﻿// <auto-generated />
using System;
using EMPS.MoneyExchangeTransfersService.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Volo.Abp.EntityFrameworkCore;

#nullable disable

namespace EMPS.MoneyExchangeTransfersService.Migrations
{
    [DbContext(typeof(MoneyExchangeTransfersServiceDbContext))]
    partial class MoneyExchangeTransfersServiceDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("_Abp_DatabaseProvider", EfCoreDatabaseProvider.SqlServer)
                .HasAnnotation("ProductVersion", "7.0.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("EMPS.MoneyExchangeTransfersService.CashVouchers.CashVoucher", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("AccountingType")
                        .HasColumnType("int")
                        .HasColumnName("AccountingType");

                    b.Property<double>("Amount")
                        .HasColumnType("float")
                        .HasColumnName("Amount");

                    b.Property<DateTime?>("ApprovalDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ApprovalDate");

                    b.Property<DateTime?>("ApprovalDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("ApprovalDateTime");

                    b.Property<Guid>("ApproveBySeatId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ApproveBySeatId");

                    b.Property<Guid>("ApproveByStaffId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ApproveByStaffId");

                    b.Property<string>("ApproveByStaffName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ApproveByStaffName");

                    b.Property<Guid?>("ApprovedByUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ApprovedByUserId");

                    b.Property<string>("ApprovedByUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ApprovedByUserName");

                    b.Property<int>("AssignSeatLocation")
                        .HasColumnType("int")
                        .HasColumnName("AssignSeatLocation");

                    b.Property<string>("BolbKey")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("BolbKey");

                    b.Property<Guid?>("CashierId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CashierName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<Guid>("CostCenterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CostCenterId");

                    b.Property<string>("CostCenterName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CostCenterName");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<string>("CurrencyCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CurrencyCode");

                    b.Property<Guid?>("CurrencyId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CurrencyId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<DateTime?>("ExecuteDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ExecuteDate");

                    b.Property<DateTime?>("ExecuteDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("ExecuteDateTime");

                    b.Property<Guid?>("ExecutedBySeatId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ExecutedBySeatId");

                    b.Property<Guid?>("ExecutedByStaffId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ExecutedByStaffId");

                    b.Property<string>("ExecutedByStaffName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExecutedByStaffName");

                    b.Property<Guid?>("ExecutedByUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ExecutedByUserId");

                    b.Property<string>("ExecutedByUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExecutedByUserName");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("FileExtension")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FileExtension");

                    b.Property<string>("FileName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FileName");

                    b.Property<string>("FileSize")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FileSize");

                    b.Property<Guid>("FinancialAccountId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("FinancialAccountId");

                    b.Property<string>("FinancialAccountName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FinancialAccountName");

                    b.Property<Guid>("FinancialPeriodId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("FinancialPeriodId");

                    b.Property<string>("FinancialPeriodName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FinancialPeriodName");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("bit")
                        .HasColumnName("IsApproved");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<bool>("IsExecute")
                        .HasColumnType("bit")
                        .HasColumnName("IsExecute");

                    b.Property<DateTime?>("IssueDateTime")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("IssueSeatId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("IssueSeatId");

                    b.Property<Guid?>("IssueStaffId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("IssueStaffId");

                    b.Property<string>("IssueStaffName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IssueStaffName");

                    b.Property<Guid?>("IssueUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("IssueUserId");

                    b.Property<string>("IssueUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IssueUserName");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("No")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("No");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Notes");

                    b.Property<Guid?>("SeatLocationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("SeatLocationId");

                    b.Property<string>("SeatLocationName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SeatLocationName");

                    b.HasKey("Id");

                    b.ToTable("CashVouchers", (string)null);
                });

            modelBuilder.Entity("EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies.MoneyExchangeTransferCurrency", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<double>("Amount")
                        .HasColumnType("float")
                        .HasColumnName("Amount");

                    b.Property<DateTime?>("ApprovalDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ApprovalDate");

                    b.Property<DateTime?>("ApprovalDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("ApprovalDateTime");

                    b.Property<Guid?>("ApprovedByUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ApprovedByUserId");

                    b.Property<string>("ApprovedByUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ApprovedByUserName");

                    b.Property<double>("AverageCost")
                        .HasColumnType("float");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CreatorId");

                    b.Property<string>("CurrencyCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CurrencyCode");

                    b.Property<Guid?>("CurrencyId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CurrencyId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DeletionTime");

                    b.Property<DateTime?>("ExecuteDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("ExecuteDate");

                    b.Property<DateTime?>("ExecuteDateTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("ExecuteDateTime");

                    b.Property<Guid?>("ExecutedBySeatId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ExecutedBySeatId");

                    b.Property<Guid?>("ExecutedByStaffId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ExecutedByStaffId");

                    b.Property<string>("ExecutedByStaffName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExecutedByStaffName");

                    b.Property<Guid?>("ExecutedByUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ExecutedByUserId");

                    b.Property<string>("ExecutedByUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExecutedByUserName");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("bit")
                        .HasColumnName("IsApproved");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<bool>("IsExecute")
                        .HasColumnType("bit")
                        .HasColumnName("IsExecute");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("No")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("No");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Notes");

                    b.Property<Guid?>("ServicePointId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ServicePointId");

                    b.Property<string>("ServicePointName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ServicePointName");

                    b.Property<DateTime?>("TransactionDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("TransactionIssueSeatId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("TransactionIssueSeatId");

                    b.Property<Guid?>("TransactionIssueStaffId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("TransactionIssueStaffId");

                    b.Property<string>("TransactionIssueStaffName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TransactionIssueStaffName");

                    b.Property<Guid?>("TransactionIssueUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("TransactionIssueUserId");

                    b.Property<string>("TransactionIssueUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TransactionIssueUserName");

                    b.Property<int>("TransferStatus")
                        .HasColumnType("int")
                        .HasColumnName("TransferStatus");

                    b.Property<int>("TransferType")
                        .HasColumnType("int")
                        .HasColumnName("TransferType");

                    b.HasKey("Id");

                    b.ToTable("MoneyExchangeTransferCurrencies", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
