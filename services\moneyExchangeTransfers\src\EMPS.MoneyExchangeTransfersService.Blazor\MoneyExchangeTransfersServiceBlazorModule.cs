﻿using Microsoft.Extensions.DependencyInjection;
using EMPS.MoneyExchangeTransfersService.Blazor.Menus;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using Volo.Abp.AspNetCore.Components.Web.Theming;
using Volo.Abp.AspNetCore.Components.Web.Theming.Routing;
using Volo.Abp.AutoMapper;
using Volo.Abp.Modularity;
using Volo.Abp.UI.Navigation;

namespace EMPS.MoneyExchangeTransfersService.Blazor;

[DependsOn(
    typeof(MoneyExchangeTransfersServiceApplicationContractsModule),
    typeof(AbpAspNetCoreComponentsWebThemingModule),
    typeof(AbpAutoMapperModule)
    )]
public class MoneyExchangeTransfersServiceBlazorModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddAutoMapperObjectMapper<MoneyExchangeTransfersServiceBlazorModule>();

        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddProfile<MoneyExchangeTransfersServiceBlazorAutoMapperProfile>(validate: true);
        });

        Configure<AbpNavigationOptions>(options =>
        {
            options.MenuContributors.Add(new MoneyExchangeTransfersServiceMenuContributor());
        });

        Configure<AbpRouterOptions>(options =>
        {
            options.AdditionalAssemblies.Add(typeof(MoneyExchangeTransfersServiceBlazorModule).Assembly);
        });
    }
}
