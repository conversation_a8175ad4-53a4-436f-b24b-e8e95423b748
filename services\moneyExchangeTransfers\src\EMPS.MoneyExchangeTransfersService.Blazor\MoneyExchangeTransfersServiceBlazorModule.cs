﻿using Microsoft.Extensions.DependencyInjection;
using EMPS.MoneyExchangeTransfersService.Blazor.Menus;
using Volo.Abp.AspNetCore.Components.Web.Theming;
using Volo.Abp.AspNetCore.Components.Web.Theming.Routing;
using Volo.Abp.AutoMapper;
using Volo.Abp.Modularity;
using Volo.Abp.UI.Navigation;
using Volo.Abp.Http.Client;

namespace EMPS.MoneyExchangeTransfersService.Blazor;

[DependsOn(
    typeof(MoneyExchangeTransfersServiceHttpApiClientModule),
    typeof(AbpAspNetCoreComponentsWebThemingModule),
    typeof(AbpAutoMapperModule)
    )]
public class MoneyExchangeTransfersServiceBlazorModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddAutoMapperObjectMapper<MoneyExchangeTransfersServiceBlazorModule>();

        // Configure remote service for HTTP API Client
        // Note: The BaseUrl should be configured in appsettings.json under RemoteServices:Default:BaseUrl
        // This allows different URLs for different environments (Development, Production, etc.)

        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddProfile<MoneyExchangeTransfersServiceBlazorAutoMapperProfile>(validate: true);
        });

        Configure<AbpNavigationOptions>(options =>
        {
            options.MenuContributors.Add(new MoneyExchangeTransfersServiceMenuContributor());
        });

        Configure<AbpRouterOptions>(options =>
        {
            options.AdditionalAssemblies.Add(typeof(MoneyExchangeTransfersServiceBlazorModule).Assembly);
        });
    }
}
