using EMPS.MoneyExchangeTransfersService.CashVouchers;
using System;
using EMPS.MoneyExchangeTransfersService.Shared;
using Volo.Abp.AutoMapper;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using AutoMapper;

namespace EMPS.MoneyExchangeTransfersService;

public class MoneyExchangeTransfersServiceApplicationAutoMapperProfile : Profile
{
    public MoneyExchangeTransfersServiceApplicationAutoMapperProfile()
    {
        /* You can configure your AutoMapper mapping configuration here.
        * Alternatively, you can split your mapping configurations
        * into multiple profile classes for a better organization. */

        CreateMap<MoneyExchangeTransferCurrency, MoneyExchangeTransferCurrencyDto>();

        CreateMap<CashVoucher, CashVoucherDto>().Ignore(x => x.BlobContent);
    }
}