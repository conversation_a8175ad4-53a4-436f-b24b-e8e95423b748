<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>EMPS.MoneyExchangeTransfersService</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Ddd.Application" Version="7.3.2" />
    <PackageReference Include="Volo.Abp.AutoMapper" Version="7.3.2" />
    <ProjectReference Include="..\EMPS.MoneyExchangeTransfersService.Application.Contracts\EMPS.MoneyExchangeTransfersService.Application.Contracts.csproj" />
    <ProjectReference Include="..\EMPS.MoneyExchangeTransfersService.Domain\EMPS.MoneyExchangeTransfersService.Domain.csproj" />
    <ProjectReference Include="..\..\..\company\src\EMPS.CompanyService.HttpApi.Client\EMPS.CompanyService.HttpApi.Client.csproj" />

  </ItemGroup>

</Project>
