using EMPS.CompanyService.Application.Contracts.Dtos.Staffs;
using EMPS.CompanyService.Staffs;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.Shared;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.BackgroundJobs;

namespace EMPS.MoneyExchangeTransfersService.CashVouchers
{
    public interface ICashVouchersAppService : IApplicationService, IBlobService
    {
        Task<PagedResultDto<CashVoucherDto>> GetListAsync(GetCashVouchersInput input);

        Task<CashVoucherDto> GetAsync(Guid id);
        Task<CashVoucherDto?> LoadByNoAsync(string no);

        Task<CashVoucherDto> SaveAsync(CashVoucherCreateDto input);
        Task<CashVoucherDto> ApproveByNoAsync(string no);
        Task DeletedByNoAsync(string no);
        Task<StaffSeatDto?> GetStaffSeatAsync();
        Task<List<StaffDto>> GetAllCashierInServicePointAsync(Guid ServicePointId);

    }
}