using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazorise;
using Blazorise.DataGrid;
using Volo.Abp.BlazoriseUI.Components;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.MoneyExchangeTransfersService.Shared;
using AutoMapper.Internal.Mappers;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.ServiceModel.Channels;
using EMPS.MoneyExchangeTransfersService.Blazor.Components;
using EMPS.MoneyExchangeTransfersService.CashVouchers;

namespace EMPS.MoneyExchangeTransfersService.Blazor.Pages.MoneyExchangeTransfersService.ReciptCashVpocherPages
{
    public partial class ReceiptCashVoucherIssue
    {

        [Inject]
        private IReceiptCashVouchersAppService _ReceiptVoucherService { get; set; }



        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar { get; } = new PageToolbar();


        private bool CanCreateOrUpdateReceiptVoucherDraft { get; set; }
        private bool CanDeleteReceiptVoucherDraft { get; set; }

        private bool IsReadOnly { get; set; }


        private ReceiptCashVoucherForm ReceiptVoucherFormRef { get; set; }


        public ReceiptCashVoucherIssue()
        {
        }

        protected override async Task OnInitializedAsync()
        {

            await SetToolbarButtons(null);
            await SetBreadcrumbItemsAsync();
            await SetPermissionsAsync();
        }


        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:ReceiptVoucherIssue"]));


            return ValueTask.CompletedTask;
        }


        private async void ResetReceiptVoucher(bool forceReset = false)
        {
            if (await ReceiptVoucherFormRef.ResetReceiptVoucher(forceReset))
            {
                await SetToolbarButtons(null);
                SetFormIsReadOnly(null);
                StateHasChanged();

            }


        }

        private async Task<CashVoucherDto?> SaveTransfare()
        {
            var ReceiptVoucher = ReceiptVoucherFormRef.FormReceiptVoucher;
            CashVoucherCreateDto createDto = ObjectMapper.Map<CashVoucherDto, CashVoucherCreateDto>(ReceiptVoucher);
            if (!await ValidateForm(createDto)) return null;
            createDto.AssignSeatLocation = ReceiptVoucherFormRef.SeatLocation;
            var newReceiptVoucher = await _ReceiptVoucherService.SaveAsync(createDto);

            ReceiptVoucherFormRef.ChangeEntity(newReceiptVoucher);
            return newReceiptVoucher;
        }

        private async Task SetPermissionsAsync()
        {
            CanCreateOrUpdateReceiptVoucherDraft = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Save);
            CanDeleteReceiptVoucherDraft = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Delete);
        }


        private async Task<bool> ValidateForm(CashVoucherCreateDto FormReceiptVoucherDraft)
        {

            if (ReceiptVoucherFormRef.SeatLocation == 0)
            {
                await Message.Error(L["CashierSeatLocationMustBeSelected"]);
                return false;
            }
            if (ReceiptVoucherFormRef.FormReceiptVoucher.Amount <= 0)
            {
                await Message.Error(L["AmountMustBeLargeThanZero"]);
                return false;
            }
            if (ReceiptVoucherFormRef.SeatLocation == SeatLocationType.ServicePoint)
            {
                if (!ReceiptVoucherFormRef.FormReceiptVoucher.SeatLocationId.HasValue)
                {

                    await Message.Error(L["ServicePointMustBeSelected"]);
                    return false;
                }
                if (!ReceiptVoucherFormRef.FormReceiptVoucher.CashierId.HasValue)
                {

                    await Message.Error(L["CashierInServicePointMustBeSelected"]);
                    return false;
                }

            }
            if (ReceiptVoucherFormRef.SeatLocation == SeatLocationType.CentralVault)
            {
                if (!ReceiptVoucherFormRef.FormReceiptVoucher.SeatLocationId.HasValue)
                {

                    await Message.Error(L["CentralVaultMustBeSelected"]);
                    return false;
                }
                if (!ReceiptVoucherFormRef.FormReceiptVoucher.CashierId.HasValue)
                {

                    await Message.Error(L["CashierInCentralVaultMustBeSelected"]);
                    return false;
                }
            }
            return true;

        }


        private void Log(string message)
        {
            Console.WriteLine($"MY-LOGGING: {message}");
        }



        private IReadOnlyList<string> stringList { get; set; }

        private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<string> e)
        {
        }

        private async Task HandleTransfareLoad(CashVoucherDto LoadedReceiptVoucher)
        {
            await SetToolbarButtons(LoadedReceiptVoucher);
            SetFormIsReadOnly(LoadedReceiptVoucher);
            Log(IsReadOnly.ToString());
            StateHasChanged();

        }

        private async Task DeleteTransfareAsync(string _No)
        {
            await _ReceiptVoucherService.DeletedByNoAsync(_No);
            ResetReceiptVoucher(true);
        }


        private async Task SetToolbarButtons(CashVoucherDto? accountReceipt)
        {
            Toolbar.Contributors.Clear();


            Toolbar.AddButton(
                L["NewReceiptVoucher"], () =>
                {
                    ResetReceiptVoucher();

                    return Task.CompletedTask;

                }, IconName.Add, Color.Warning


            );

            Toolbar.AddButton(
                L["SaveReceiptVoucher"], async () =>
                {
                    await PerformSave();
                }, IconName.Save, requiredPolicyName:
                MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Save, color: Color.Success, disabled: !CanReceiptVoucherBeUpdated(accountReceipt)
            );
            if (accountReceipt != null && accountReceipt.Id != Guid.Empty)
            {

                Toolbar.AddButton(
                            L["Delete"], async () =>
                            {
                                await PerformDeleteTransfare();

                            }, IconName.Delete,
                            requiredPolicyName: MoneyExchangeTransfersServicePermissions.ReceiptCashVouchers.Delete,
                            color: Color.Danger,
                            disabled: !CanReceiptVoucherBeDeleted(accountReceipt)
                        );

            }

            StateHasChanged();


        }


        private bool CanReceiptVoucherBeDeleted(CashVoucherDto accountReceipt)
        {



            if (accountReceipt == null) return false;
            if (accountReceipt.Id == Guid.Empty) return false;
            if (accountReceipt.IsApproved) return false;

            if (accountReceipt.IsExecute) return false;

            return true;
        }
        private bool CanReceiptVoucherBeUpdated(CashVoucherDto? accountReceipt)
        {

            if (accountReceipt != null)
                if (accountReceipt.IsExecute || accountReceipt.IsApproved) return false;

            return true;
        }

        private void SetFormIsReadOnly(CashVoucherDto? accountReceipt)
        {

            this.IsReadOnly = false;
            if (accountReceipt == null) return;
            if (accountReceipt.IsExecute || accountReceipt.IsApproved)
            {
                this.IsReadOnly = true;
            }
        }

        private async Task PerformSave()
        {
            await Task.Delay(300);


            if (!CanReceiptVoucherBeUpdated(ReceiptVoucherFormRef.FormReceiptVoucher))
            {
                await Message.Error(L["Error:ThisReceiptVoucherCannotBeUpdated"]);
                return;
            }
            Console.WriteLine("********************* :1");
            CashVoucherDto? newReceiptVoucher = new();
            var ReceiptVoucher = ReceiptVoucherFormRef.FormReceiptVoucher;
            Console.WriteLine("********************* :2");



            await ReceiptVoucherFormRef.loadingIndicator.Show();
            Console.WriteLine("********************* :3");

            try
            {
                newReceiptVoucher = await SaveTransfare();

                if (newReceiptVoucher != null)
                {
                    await SetToolbarButtons(newReceiptVoucher);
                    await Message.Success(L["SaveSuccess"]);
                }


            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await ReceiptVoucherFormRef.loadingIndicator.Hide();

            }

        }
        private async Task PerformDeleteTransfare()
        {
            if (!ReceiptVoucherFormRef.IsReceiptVoucherLoaded())
            {
                await Message.Error(L["Error:LoadReceiptVoucherFirst"]);
                return;
            }
            if (!CanReceiptVoucherBeDeleted(ReceiptVoucherFormRef.FormReceiptVoucher))
            {
                await Message.Error(L["Error:ThisReceiptVoucherCannotBeDeleted"]);
                return;
            }
            if (!await Message.Confirm(L["DeleteConfirmationMessage"])) return;
            await ReceiptVoucherFormRef.loadingIndicator.Show();

            try
            {
                var ReceiptVoucherNo = ReceiptVoucherFormRef.GetReceiptVoucherNo();


                await DeleteTransfareAsync(ReceiptVoucherNo!);
                await Message.Success(L["SuccessfullyDeleted"]);

            }
            catch (Exception ex)
            {
                await Message.Error(ex.Message);

            }
            finally
            {
                await ReceiptVoucherFormRef.loadingIndicator.Hide();

            }
        }
        private async Task PerformNewTransfare()
        {
            ResetReceiptVoucher();

        }
        public async Task HandleKeyDown(KeyboardEventArgs e)
        {

            ExecuteJS("event.stopPropagation();");

            if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyD"))
            {
                await PerformDeleteTransfare();
            }
            else if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyR"))
            {
                await PerformNewTransfare();
            }
            else if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyG"))
            {
                await Task.Delay(50);
                await ReceiptVoucherFormRef.PerformLoadReceiptVoucher();
            }
            else if ((e.MetaKey || e.CtrlKey) && (e.Code == "KeyS"))
            {
                await PerformSave();
            }


        }
        private void ExecuteJS(string script)
        {
            JSRuntime.InvokeVoidAsync("eval", script);
        }
        [Inject] IJSRuntime JSRuntime { get; set; }
        protected override void OnAfterRender(bool firstRender)
        {
            base.OnAfterRender(firstRender);
            if (firstRender)
            {
                // See warning about memory above in the article
                var dotNetReference = DotNetObjectReference.Create(this);
                JSRuntime.InvokeVoidAsync("GetDotnetInstance", dotNetReference);
            }
        }






    }
}
