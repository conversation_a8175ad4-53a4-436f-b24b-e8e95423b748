using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Blazorise;
using Blazorise.DataGrid;
using Volo.Abp.BlazoriseUI.Components;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;
using EMPS.MoneyExchangeTransfersService.Permissions;
using EMPS.MoneyExchangeTransfersService.Shared;
using AutoMapper.Internal.Mappers;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.ServiceModel.Channels;
using Volo.Abp;
using EMPS.MoneyExchangeTransfersService.Blazor.Components;

namespace EMPS.MoneyExchangeTransfersService.Blazor.Pages.MoneyExchangeTransfersService.ServicePointToCompanyTransferPages
{
    public partial class ServicePointToCompanyTransferExecute
    {

        [Inject]
        private IServicePointToCompanyTransferService _ServicePointToCompanyTransferService { get; set; }



        protected List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new List<Volo.Abp.BlazoriseUI.BreadcrumbItem>();
        protected PageToolbar Toolbar { get; } = new PageToolbar();


        private bool CanExecuteServicePointToCompanyTransferDraft { get; set; }

        private bool IsReadOnly { get; set; }


        private ServicePointToCompanyForm ServicePointToCompanyFormRef { get; set; }


        public ServicePointToCompanyTransferExecute()
        {
        }

        protected override async Task OnInitializedAsync()
        {

            await SetToolbarButtons(null);
            await SetBreadcrumbItemsAsync();
            await SetPermissionsAsync();
        }


        protected virtual ValueTask SetBreadcrumbItemsAsync()
        {
            BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Menu:ServicePointToCompanyTransferExecute"]));


            return ValueTask.CompletedTask;
        }


        private async void ResetServicePointToCompanyTransfer(bool forceReset = false)
        {
            if (await ServicePointToCompanyFormRef.ResetServicePointToCompany(forceReset))
            {
                await SetToolbarButtons(null);
                SetFormIsReadOnly(null);
                StateHasChanged();

            }


        }

        private async Task SetPermissionsAsync()
        {

            CanExecuteServicePointToCompanyTransferDraft = await AuthorizationService
                .IsGrantedAsync(MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Execute);
        }




        private void Log(string message)
        {
            Console.WriteLine($"MY-LOGGING: {message}");
        }



        private IReadOnlyList<string> stringList { get; set; }

        private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<string> e)
        {
        }

        private async Task HandleTransfareLoad(MoneyExchangeTransferCurrencyDto LoadedServicePointToCompanyTransfer)
        {
            await SetToolbarButtons(LoadedServicePointToCompanyTransfer);
            SetFormIsReadOnly(LoadedServicePointToCompanyTransfer);
            Log(IsReadOnly.ToString());
            StateHasChanged();

        }



        private async Task SetToolbarButtons(MoneyExchangeTransferCurrencyDto? servicePointToCompanyTransfer)
        {
            Toolbar.Contributors.Clear();




            Toolbar.AddButton(
         L["NewServicePointToCompanyTransfer"], () =>
         {
             ResetServicePointToCompanyTransfer();

             return Task.CompletedTask;

         }, IconName.Add, Color.Warning


     );

            Toolbar.AddButton(
                L["ExecuteServicePointToCompanyTransfer"], async () =>
                {
                    await PerformExecute();
                }, IconName.Save, requiredPolicyName:
                MoneyExchangeTransfersServicePermissions.ServicePointToCompanyTransfer.Execute, color: Color.Success, disabled: !CanServicePointToCompanyTransferBeExecute(servicePointToCompanyTransfer)
            );

            StateHasChanged();


        }

        private bool CanServicePointToCompanyTransferBeExecute(MoneyExchangeTransferCurrencyDto? servicePointToCompanyTransfer)
        {
            return servicePointToCompanyTransfer != null && servicePointToCompanyTransfer.IsApproved && !servicePointToCompanyTransfer.IsExecute;
        }

        private void SetFormIsReadOnly(MoneyExchangeTransferCurrencyDto? servicePointToCompanyTransfer)
        {
            IsReadOnly = true; // Execute page is always read-only
        }

        private async Task PerformExecute()
        {
            if (ServicePointToCompanyFormRef.FormServicePointToCompany != null && !string.IsNullOrEmpty(ServicePointToCompanyFormRef.FormServicePointToCompany.No))
            {
                if (await Message.Confirm(L["ExecuteConfirmationMessage"]))
                {
                    var result = await _ServicePointToCompanyTransferService.ExecuteByNoAsync(ServicePointToCompanyFormRef.FormServicePointToCompany.No);
                    ServicePointToCompanyFormRef.ChangeEntity(result);
                    await SetToolbarButtons(result);
                    SetFormIsReadOnly(result);
                    StateHasChanged();
                }
            }
        }

        private async Task HandleKeyDown(KeyboardEventArgs e)
        {
            if (e.Key == "F2")
            {
                await PerformExecute();
            }
        }


    }
}
