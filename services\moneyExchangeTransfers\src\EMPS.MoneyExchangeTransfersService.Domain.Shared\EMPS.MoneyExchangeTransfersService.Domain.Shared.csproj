<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\..\..\common.props" />

  <PropertyGroup>
    <TargetFrameworks>net7.0</TargetFrameworks>
    <Nullable>enable</Nullable>
    <RootNamespace>EMPS.MoneyExchangeTransfersService</RootNamespace>
    <GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Commercial.SuiteTemplates" Version="7.3.2" />
    <PackageReference Include="Volo.Abp.Validation" Version="7.3.2" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Localization\MoneyExchangeTransfersService\*.json" />
    <Content Remove="Localization\MoneyExchangeTransfersService\*.json" />
  </ItemGroup>

</Project>
