﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMPS.MoneyExchangeTransfersService.Migrations
{
    /// <inheritdoc />
    public partial class AddedCashVoucher : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CashVouchers",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    No = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Amount = table.Column<double>(type: "float", nullable: false),
                    CurrencyCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CurrencyId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    SeatLocationName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SeatLocationId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IssueUserName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IssueUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IssueStaffName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IssueStaffId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IssueSeatId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsApproved = table.Column<bool>(type: "bit", nullable: false),
                    ApprovedByUserName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ApprovedByUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ApprovalDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ApprovalDateTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Notes = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsExecute = table.Column<bool>(type: "bit", nullable: false),
                    ExecutedByUserName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ExecutedByUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ExecutedByStaffName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ExecutedByStaffId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ExecutedBySeatId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ExecuteDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ExecuteDateTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ApproveByStaffName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ApproveByStaffId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ApproveBySeatId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AccountingType = table.Column<int>(type: "int", nullable: false),
                    BolbKey = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FileName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FileExtension = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FileSize = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FinancialPeriodId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FinancialPeriodName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FinancialAccountId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FinancialAccountName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AssignSeatLocation = table.Column<int>(type: "int", nullable: false),
                    CostCenterId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CostCenterName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ExtraProperties = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CashVouchers", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CashVouchers");
        }
    }
}
