﻿using EMPS.CompanyService.Currencies;
using EMPS.CompanyService.ServicePoints;
using EMPS.CompanyService.Staffs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies
{
    public interface IMoneyExchangeTransferBase
    {
        Task<MoneyExchangeTransferCurrencyDto> SaveAsync(MoneyExchangeTransferCurrencyCreateDto input);
        Task<MoneyExchangeTransferCurrencyDto> ExecuteByNoAsync(string no);
        Task DeletedByNoAsync(string no);
        Task<MoneyExchangeTransferCurrencyDto?> LoadByNoAsync(string no);
        Task<List<CurrencyDto>> GetAllActiveCurrenciesAsync();

            
    }
}
