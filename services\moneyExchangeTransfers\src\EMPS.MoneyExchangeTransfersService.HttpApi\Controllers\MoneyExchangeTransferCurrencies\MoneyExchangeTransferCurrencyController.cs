using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using EMPS.MoneyExchangeTransfersService.MoneyExchangeTransferCurrencies;

namespace EMPS.MoneyExchangeTransfersService.Controllers.MoneyExchangeTransferCurrencies
{
    [RemoteService(Name = "MoneyExchangeTransfersService")]
    [Area("moneyExchangeTransfersService")]
    [ControllerName("MoneyExchangeTransferCurrency")]
    [Route("api/money-exchange-transfers-service/money-exchange-transfer-currencies")]
    public class MoneyExchangeTransferCurrencyController : AbpController, IMoneyExchangeTransferCurrenciesAppService
    {
        private readonly IMoneyExchangeTransferCurrenciesAppService _moneyExchangeTransferCurrenciesAppService;

        public MoneyExchangeTransferCurrencyController(IMoneyExchangeTransferCurrenciesAppService moneyExchangeTransferCurrenciesAppService)
        {
            _moneyExchangeTransferCurrenciesAppService = moneyExchangeTransferCurrenciesAppService;
        }

        [HttpGet]
        public virtual Task<PagedResultDto<MoneyExchangeTransferCurrencyDto>> GetListAsync(GetMoneyExchangeTransferCurrenciesInput input)
        {
            return _moneyExchangeTransferCurrenciesAppService.GetListAsync(input);
        }

        [HttpGet]
        [Route("{id}")]
        public virtual Task<MoneyExchangeTransferCurrencyDto> GetAsync(Guid id)
        {
            return _moneyExchangeTransferCurrenciesAppService.GetAsync(id);
        }

        [HttpPost]
        public virtual Task<MoneyExchangeTransferCurrencyDto> CreateAsync(MoneyExchangeTransferCurrencyCreateDto input)
        {
            return _moneyExchangeTransferCurrenciesAppService.CreateAsync(input);
        }

        [HttpPut]
        [Route("{id}")]
        public virtual Task<MoneyExchangeTransferCurrencyDto> UpdateAsync(Guid id, MoneyExchangeTransferCurrencyUpdateDto input)
        {
            return _moneyExchangeTransferCurrenciesAppService.UpdateAsync(id, input);
        }

        [HttpDelete]
        [Route("{id}")]
        public virtual Task DeleteAsync(Guid id)
        {
            return _moneyExchangeTransferCurrenciesAppService.DeleteAsync(id);
        }
    }
}